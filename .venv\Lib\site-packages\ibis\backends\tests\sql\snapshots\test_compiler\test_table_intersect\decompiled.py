import ibis


functional_alltypes = ibis.table(
    name="functional_alltypes",
    schema={
        "id": "int32",
        "bool_col": "boolean",
        "tinyint_col": "int8",
        "smallint_col": "int16",
        "int_col": "int32",
        "bigint_col": "int64",
        "float_col": "float32",
        "double_col": "float64",
        "date_string_col": "string",
        "string_col": "string",
        "timestamp_col": "timestamp",
        "year": "int32",
        "month": "int32",
    },
)
lit = ibis.literal(0)
f = functional_alltypes.filter((functional_alltypes.int_col > lit))
f1 = functional_alltypes.filter((functional_alltypes.int_col <= lit))

result = f.select(
    f.string_col.name("key"), f.float_col.cast("float64").name("value")
).intersect(
    f1.select(f1.string_col.name("key"), f1.double_col.name("value")), distinct=True
)
