oracledb-3.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
oracledb-3.2.0.dist-info/METADATA,sha256=ck8lGZVO76W5uc4tVhOrYsXu_zQ7NaNEX0Hu8edl_dM,6686
oracledb-3.2.0.dist-info/RECORD,,
oracledb-3.2.0.dist-info/WHEEL,sha256=8UP9x9puWI0P1V_d7K2oMTBqfeLNm21CTzZ_Ptr0NXU,101
oracledb-3.2.0.dist-info/licenses/LICENSE.txt,sha256=mCfU4gZrgm7RHOp6hEzBmR6GmKll4lsk9AOExd1AFLY,12799
oracledb-3.2.0.dist-info/licenses/NOTICE.txt,sha256=GDxR6cbc2NhN8aAnjjkzX-8qdmCvEtLMvIdBE79HyhI,57
oracledb-3.2.0.dist-info/licenses/THIRD_PARTY_LICENSES.txt,sha256=5Gb5yJqi23UygN66z0E4U9cTvgWq_YvrfBM06bzcG3Y,688812
oracledb-3.2.0.dist-info/top_level.txt,sha256=DGlJh0bBtAUvhjHF-pPnrVCzNhW87yzaKB_1_FP28uU,9
oracledb/__init__.py,sha256=-0VMTBC4xjUR9lyU036ey8-6OpFUUokcGVn1jske0U4,13476
oracledb/__pycache__/__init__.cpython-312.pyc,,
oracledb/__pycache__/aq.cpython-312.pyc,,
oracledb/__pycache__/builtin_hooks.cpython-312.pyc,,
oracledb/__pycache__/connect_params.cpython-312.pyc,,
oracledb/__pycache__/connection.cpython-312.pyc,,
oracledb/__pycache__/constants.cpython-312.pyc,,
oracledb/__pycache__/constructors.cpython-312.pyc,,
oracledb/__pycache__/cursor.cpython-312.pyc,,
oracledb/__pycache__/dbobject.cpython-312.pyc,,
oracledb/__pycache__/defaults.cpython-312.pyc,,
oracledb/__pycache__/driver_mode.cpython-312.pyc,,
oracledb/__pycache__/dsn.cpython-312.pyc,,
oracledb/__pycache__/enums.cpython-312.pyc,,
oracledb/__pycache__/errors.cpython-312.pyc,,
oracledb/__pycache__/exceptions.cpython-312.pyc,,
oracledb/__pycache__/fetch_info.cpython-312.pyc,,
oracledb/__pycache__/future.cpython-312.pyc,,
oracledb/__pycache__/lob.cpython-312.pyc,,
oracledb/__pycache__/pipeline.cpython-312.pyc,,
oracledb/__pycache__/pool.cpython-312.pyc,,
oracledb/__pycache__/pool_params.cpython-312.pyc,,
oracledb/__pycache__/soda.cpython-312.pyc,,
oracledb/__pycache__/sparse_vector.cpython-312.pyc,,
oracledb/__pycache__/subscr.cpython-312.pyc,,
oracledb/__pycache__/utils.cpython-312.pyc,,
oracledb/__pycache__/var.cpython-312.pyc,,
oracledb/__pycache__/version.cpython-312.pyc,,
oracledb/aq.py,sha256=DXGkHAV5FzVkyUs_K-WEPbofOdPBJizUdPC_mUkd6tM,22579
oracledb/base_impl.cp312-win_amd64.pyd,sha256=NVhKdnol0E4cGqhLBrwyyJqbEEasFj95oBu9KWyr51Y,1402368
oracledb/builtin_hooks.py,sha256=MErvN7eIEOsgqK5PPKrqzyb66wJmwfdjPZFr4JhnEFk,3735
oracledb/connect_params.py,sha256=FIP9JK8bDc-G9ThiHbLU-xuG3xR6pMHc1Dl4h5RpKYI,48237
oracledb/connection.py,sha256=O757IeVC-khQIwL-q98fPDKfudcicLa1wCN6I8Qelro,95631
oracledb/constants.py,sha256=BmOy3i7DmLEiuhgjpkobj1iaC6JnVaQBJaPgaUU0zS0,3556
oracledb/constructors.py,sha256=kKIVeY3QKgf_WHd8wDybTfjwPMAFvwelwzqzQYZFxiE,2985
oracledb/cursor.py,sha256=KJSIU2lJ4mTamBoqZjQ583BNEkH09ZuRI6Lv16BEvvI,46339
oracledb/dbobject.py,sha256=5SXwEO8FdKfuHokKQI9t8bkLASMOskYFx48A3zzD5PE,13039
oracledb/defaults.py,sha256=uMSp5LJLwsjNCH_7ut0yCXvyt-ehBcd8uka4CvHX0q4,5848
oracledb/driver_mode.py,sha256=uJCBux03A68Ag01WErUawYtfP3l6b2IkMvQf1b7ElXQ,5434
oracledb/dsn.py,sha256=IOqAiT-Dkf_AqJ3ptYC0YdxIX3-DPfm9wNaFNINGAkg,3242
oracledb/enums.py,sha256=wRLeVOlq_Wr6bv5buzBrzZA5bFfz9F6ua5k44ZsmU24,4147
oracledb/errors.py,sha256=bu7e0TfMeahmgVfXEz7TDJnyWDUS0c6YfFyCkeNJmkE,36415
oracledb/exceptions.py,sha256=0p-3umiTbuqnfTxdcwJsnwMSo8l7DnppUJqY4kpIcj4,1881
oracledb/fetch_info.py,sha256=bYm9Gq3E_x9VJTs8dphi_ncmyzvRuC-ry0RQiExwpgg,8180
oracledb/future.py,sha256=ZjpMyuFl6o8umvALkLRE1KBhtJjtXP4075fpWDh0xPk,1763
oracledb/interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
oracledb/interchange/__pycache__/__init__.cpython-312.pyc,,
oracledb/interchange/__pycache__/buffer.cpython-312.pyc,,
oracledb/interchange/__pycache__/column.cpython-312.pyc,,
oracledb/interchange/__pycache__/dataframe.cpython-312.pyc,,
oracledb/interchange/__pycache__/protocol.cpython-312.pyc,,
oracledb/interchange/buffer.py,sha256=xe0RVGxtkDAcU-hnAtsxwkIjCZguhwWuQPmS3S1l3ho,2952
oracledb/interchange/column.py,sha256=BJOjzXCgOAcCKchTRQNv2N0oQLmbRxDOhZLh6KGWUrw,8029
oracledb/interchange/dataframe.py,sha256=_1SjIpyMROTKAHuYrMHsXXJTN5xNooCSpif9pQX3jfU,5762
oracledb/interchange/nanoarrow_bridge.cp312-win_amd64.pyd,sha256=genI9jM74bPh7oxaqm29MOSTPPcEYDI8IYPdUYHKUrw,119808
oracledb/interchange/protocol.py,sha256=oBVLiv1rXeJ8AyMceDASsrGyBVX2nzlCvjvMO1x3alQ,19125
oracledb/lob.py,sha256=DkLPpUbk4YCehG1beQA8c_rQB3Ul-CgC5C6LMSzRlgM,11987
oracledb/pipeline.py,sha256=xd6MhGrnUv7h9nFs82unfVfPP-LmiFgV0xI9S_qTwVs,12540
oracledb/plugins/__pycache__/azure_config_provider.cpython-312.pyc,,
oracledb/plugins/__pycache__/azure_tokens.cpython-312.pyc,,
oracledb/plugins/__pycache__/oci_config_provider.cpython-312.pyc,,
oracledb/plugins/__pycache__/oci_tokens.cpython-312.pyc,,
oracledb/plugins/azure_config_provider.py,sha256=8pbGlS6Am_1L87uxlTJ8Zv-7D-qUCc_Z4YhrwQES8o8,10038
oracledb/plugins/azure_tokens.py,sha256=qBjSmNP-wlaJmksk8et22trrtpAPkJKo8I-CCQK7ad4,3008
oracledb/plugins/oci_config_provider.py,sha256=cGSBcqurH6vhaz0FlEBfkFeRRIhdj9pZ8rx2s7w_GUY,9238
oracledb/plugins/oci_tokens.py,sha256=D4NGEWRdQBfO1MVY-VryzsRTrE2QjId7RShOqmRDxtQ,5994
oracledb/pool.py,sha256=IiXevWFvKhWSwPUU_CyeiERFJp9lN81BwoLTAXQGKTQ,68000
oracledb/pool_params.py,sha256=Sr83_DEX4cMDP9INT9gjNxNhRk7BuCTVtqEnHADlIxU,41728
oracledb/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
oracledb/soda.py,sha256=HwJ1CHHbOdnJt3641oc0zHvK3r47rf3QrPFBLaHjZWI,29288
oracledb/sparse_vector.py,sha256=3UXHFRgjzsCvBWKqWN3j84cY0XaTK2F4_FJTOXsfCds,3532
oracledb/subscr.py,sha256=0stBCutBbuX29cQSSowi8MF2bEoCCMBKFzVDq3XAX4U,11423
oracledb/thick_impl.cp312-win_amd64.pyd,sha256=xQBWFkG37FqBhFoaAXlzmeMTQn2WpocWRCB6TTxzLew,978944
oracledb/thin_impl.cp312-win_amd64.pyd,sha256=NIhl0QbqLdqw5pKajgrK10ExLLCsidnfokz3VlOEXuc,1944064
oracledb/utils.py,sha256=pOmx9YkrWOZbJay_p4qpldtFFzqRMSlURI8c_ltiZqE,6834
oracledb/var.py,sha256=bIeJZx7hs-XwgjYTV-3X1shN-n1wwwKziU21ORZObI0,6834
oracledb/version.py,sha256=gQ9cIg-JMIxGYlVRZsys5PWicY1wbqBC4XvadNj6OeY,1566
