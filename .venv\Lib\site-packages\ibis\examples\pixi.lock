version: 4
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/_r-mutex-1.0.1-anacondar_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.9.3-py311h459d7ec_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/appdirs-1.4.4-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/atk-1.0-2.38.0-hd4edc92_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/atpublic-3.0.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-23.2.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.7.16-haed3651_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.6.10-ha9bf9b1_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.9.14-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.2.18-h4466546_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.4.2-he635cd5_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.8.1-hbfc29b2_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.14.6-h6b388c4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.10.3-hffff1cc_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.5.2-h4893938_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.1.15-h4466546_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.1.18-h4466546_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.26.3-h137ae52_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.267-he0cb598_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.40-hf600244_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/blinker-1.7.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py311hb755f60_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bwidget-1.9.14-ha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.27.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2024.2.2-hbcca054_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cachetools-5.3.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.0-h3faef2a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2024.2.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.16.0-py311hb3a22ac_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.1.7-unix_pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cryptography-42.0.5-py311h63ff55d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/curl-8.6.0-hca28451_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.1.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-engine-0.11.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.6.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.13.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.14.2-h14ed4e7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.12.1-h267a509_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.4.1-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2024.3.1-pyhca7485f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.2.0-h338b0a0_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/gcsfs-2024.3.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.10-h829c605_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.21.1-h27087fc_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-he1b5a44_1004.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gfortran_impl_linux-64-13.2.0-h76e1118_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/giflib-5.2.1-h0b41bf4_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.0-hed5481d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/google-api-core-2.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/google-auth-2.29.0-pyhca7485f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/google-auth-oauthlib-1.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/google-cloud-core-2.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/google-cloud-storage-2.16.0-pyhca7485f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/google-crc32c-1.1.2-py311h9b08b9c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/google-resumable-media-2.7.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/googleapis-common-protos-1.63.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.13-h58526e2_1001.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphviz-9.0.0-h78e8752_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/greenlet-3.0.3-py311hb755f60_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-1.62.1-py311ha6695c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gtk2-2.24.33-h280cfa0_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gts-0.7.6-h977cf35_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.2.0-h338b0a0_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-8.3.0-h3d44ed6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/humanize-4.9.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ibis-duckdb-8.0.0-hd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ibis-framework-core-8.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-73.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-7.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-resources-6.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib_resources-6.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-2.6.32-he073ed8_17.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.2-h659d440_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.40-h41732ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h27087fc_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20240116.1-cxx17_h59595ed_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-15.0.2-h6bfc85a_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-15.0.2-h59595ed_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-15.0.2-h59595ed_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-15.0.2-hc6145d9_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-sql-15.0.2-h757c851_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-gandiva-15.0.2-hb016d2e_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-15.0.2-h757c851_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-21_linux64_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-21_linux64_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.6.0-hca28451_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.19-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20191231-he28a2e2_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.2.0-ha9c7c90_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-13.2.0-h807b86a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgd-2.3.3-h119a65a_9.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-ng-13.2.0-h69a702a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-13.2.0-ha4646dd_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.78.4-h783c2da_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-13.2.0-h807b86a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.22.0-h9be4e54_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.22.0-hc7a4891_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.62.1-h15f2491_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.17-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.0.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-21_linux64_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm16-16.0.6-hb3ce162_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.58.0-h47da74e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.9.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.26-pthreads_h413a1c8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-15.0.2-h352af49_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.43-h2797004_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-4.25.3-h08a7969_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2023.09.01-h5a48ba9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.56.3-he3f83f7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.2.0-h7e041cc_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.45.2-h2797004_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.0-h0841786_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.2.0-ha9c7c90_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-13.2.0-h7e041cc_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.19.0-hb90f79a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.6.0-ha9c0a0a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.8.0-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-1.3.2-h658648e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.3.2-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.15-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.12.6-h232c23b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.2.13-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.9.4-hcb278e6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/make-4.3-hd18ef5c_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-2.1.5-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.0.5-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/multipledispatch-0.6.0-py_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.4.20240210-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-1.26.4-py311h64a7726_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/oauthlib-3.2.2-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.2.1-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.0.0-h1e5e2c1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-24.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.2.1-py311h320fe9a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandoc-********-ha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.52.1-ha41ecd1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/parsy-2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.42-hcad00b1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pins-0.8.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.43.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/proto-plus-1.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/protobuf-4.25.3-py311h7b78aeb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-h36c2ea0_1001.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-15.0.2-py311h39c9aba_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyarrow-hotfix-0.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyasn1-0.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyasn1-modules-0.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.21-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.17.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyjwt-2.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyopenssl-24.0.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha2e5f31_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.8-hab00c5b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-duckdb-0.10.1-py311hb755f60_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-graphviz-0.20.3-pyh717bed2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2024.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.4.1-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.11-4_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2024.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyu2f-0.1.5-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.1-py311h459d7ec_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-askpass-1.2.0-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-assertthat-0.2.1-r43hc72bb7e_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-backports-1.4.1-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-base-4.3.3-hb8ee39d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-base64enc-0.1_3-r43h57805ef_1006.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-bit-4.0.5-r43h57805ef_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-bit64-4.0.5-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-blob-1.2.4-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-broom-1.0.5-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-bslib-0.6.1-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-cachem-1.0.8-r43h57805ef_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-callr-3.7.5-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-cellranger-1.1.0-r43hc72bb7e_1006.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-cli-3.6.2-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-clipr-0.8.0-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-colorspace-2.1_0-r43h57805ef_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-conflicted-1.2.0-r43h785f33e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-cpp11-0.4.7-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-crayon-1.5.2-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-curl-5.1.0-r43hf9611b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-data.table-1.15.2-r43h029312a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-dbi-1.2.2-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-dbplyr-2.4.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-digest-0.6.35-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-dplyr-1.1.4-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-dtplyr-1.3.1-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-ellipsis-0.3.2-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-evaluate-0.23-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-fansi-1.0.6-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-farver-2.1.1-r43ha503ecb_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-fastmap-1.1.1-r43ha503ecb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-fontawesome-0.5.2-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-forcats-1.0.0-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-fs-1.6.3-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-gargle-1.5.2-r43h785f33e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-generics-0.1.3-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-ggplot2-3.5.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-glue-1.7.0-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-googledrive-2.1.1-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-googlesheets4-1.1.1-r43h785f33e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-gtable-0.3.4-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-haven-2.5.4-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-highr-0.10-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-hms-1.1.3-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-htmltools-0.5.7-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-httr-1.4.7-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-ids-1.0.1-r43hc72bb7e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-isoband-0.2.7-r43ha503ecb_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-janitor-2.2.0-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-jquerylib-0.1.4-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-jsonlite-1.8.8-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-knitr-1.45-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-labeling-0.4.3-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-lattice-0.22_6-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-lifecycle-1.0.4-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-lubridate-1.9.3-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-magrittr-2.0.3-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-mass-7.3_60-r43h57805ef_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-matrix-1.6_5-r43h316c678_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-memoise-2.0.1-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-mgcv-1.9_1-r43h316c678_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-mime-0.12-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-modelr-0.1.11-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-munsell-0.5.0-r43hc72bb7e_1006.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-nlme-3.1_164-r43h61816a4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-openssl-2.1.1-r43hb353fa6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-palmerpenguins-0.1.1-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-pillar-1.9.0-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-pkgconfig-2.0.3-r43hc72bb7e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-prettyunits-1.2.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-processx-3.8.4-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-progress-1.2.3-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-ps-1.7.6-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-purrr-1.0.2-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-r6-2.5.1-r43hc72bb7e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-ragg-1.3.0-r43h73ae6e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-rappdirs-0.3.3-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-rcolorbrewer-1.1_3-r43h785f33e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-readr-2.1.5-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-readxl-1.4.3-r43ha5c9fba_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-rematch-2.0.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-rematch2-2.1.2-r43hc72bb7e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-reprex-2.1.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-rlang-1.1.3-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-rmarkdown-2.26-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-rstudioapi-0.15.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-rvest-1.0.4-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-sass-0.4.9-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-scales-1.3.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-selectr-0.4_2-r43hc72bb7e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-snakecase-0.11.1-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-stringi-1.8.3-r43h9facbd6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-stringr-1.5.1-r43h785f33e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-sys-3.4.2-r43h57805ef_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-systemfonts-1.0.5-r43haf97adc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-textshaping-0.3.7-r43hd87b9d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-tibble-3.2.1-r43h57805ef_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-tidyr-1.3.1-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-tidyselect-1.2.0-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-tidyverse-2.0.0-r43h785f33e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-timechange-0.3.0-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-tinytex-0.50-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-tzdb-0.4.0-r43ha503ecb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-utf8-1.2.4-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-uuid-1.2_0-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-vctrs-0.6.5-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-viridislite-0.4.2-r43hc72bb7e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-vroom-1.6.5-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/r-withr-3.0.0-r43hc72bb7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-xfun-0.42-r43ha503ecb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-xml2-1.3.6-r43hbfba7a4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/r-yaml-2.3.8-r43h57805ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-50.0-hd3aeb46_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2023.09.01-h7f4b329_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2023.12.25-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.31.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-oauthlib-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-13.7.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rsa-4.9-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.4.7-h06160fa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sed-4.8-he412f7d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.16.0-pyh6c4a22f_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.1.10-h9fff704_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sqlalchemy-2.0.28-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sqlalchemy-views-0.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sqlglot-20.11.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.12-he073ed8_17.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tktable-2.10-h0c5db8f_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/toolz-0.12.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.66.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.10.0-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.10.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ucx-1.15.0-h11edf95_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-kbproto-1.0.7-h7f98852_1002.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.4-h7391055_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.7-h8ee46fc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.11-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.3-h7f98852_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.4-h0b41bf4_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.11-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxt-1.3.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-renderproto-0.11.1-h7f98852_1002.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-xextproto-7.3.0-h0b41bf4_1003.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-xproto-7.0.31-h7f98852_1007.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.2-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.2.6-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.9.4-py311h459d7ec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.2.13-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.5-hfc55251_0.conda
packages:
- kind: conda
  name: _libgcc_mutex
  version: '0.1'
  build: conda_forge
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  size: 2562
  timestamp: 1578324546067
- kind: conda
  name: _openmp_mutex
  version: '4.5'
  build: 2_gnu
  build_number: 16
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23621
  timestamp: 1650670423406
- kind: conda
  name: _r-mutex
  version: 1.0.1
  build: anacondar_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/_r-mutex-1.0.1-anacondar_1.tar.bz2
  sha256: e58f9eeb416b92b550e824bcb1b9fb1958dee69abfe3089dfd1a9173e3a0528a
  md5: 19f9db5f4f1b7f5ef5f6d67207f25f38
  license: BSD
  size: 3566
  timestamp: 1562343890778
- kind: conda
  name: aiohttp
  version: 3.9.3
  build: py311h459d7ec_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.9.3-py311h459d7ec_1.conda
  sha256: f36286e4cfc6e86c6dd296695f066ebd64767a39477d8e951bc2dce7ebdfcde2
  md5: 7fd17e8947afbddd2855720d643a48f0
  depends:
  - aiosignal >=1.1.2
  - attrs >=17.3.0
  - frozenlist >=1.1.1
  - libgcc-ng >=12
  - multidict >=4.5,<7.0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - yarl >=1.0,<2.0
  license: MIT AND Apache-2.0
  license_family: Apache
  size: 803387
  timestamp: 1710511729342
- kind: conda
  name: aiosignal
  version: 1.3.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.1-pyhd8ed1ab_0.tar.bz2
  sha256: 575c742e14c86575986dc867463582a970463da50b77264cdf54df74f5563783
  md5: d1e1eb7e21a9e2c74279d87dafb68156
  depends:
  - frozenlist >=1.1.0
  - python >=3.7
  license: Apache-2.0
  license_family: APACHE
  size: 12730
  timestamp: 1667935912504
- kind: conda
  name: appdirs
  version: 1.4.4
  build: pyh9f0ad1d_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/appdirs-1.4.4-pyh9f0ad1d_0.tar.bz2
  sha256: ae9fb8f68281f84482f2c234379aa12405a9e365151d43af20b3ae1f17312111
  md5: 5f095bc6454094e96f146491fd03633b
  depends:
  - python
  license: MIT
  license_family: MIT
  size: 12840
  timestamp: 1603108499239
- kind: conda
  name: atk-1.0
  version: 2.38.0
  build: hd4edc92_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/atk-1.0-2.38.0-hd4edc92_1.tar.bz2
  sha256: 2f9314de13c1f0b54510a2afa0cdc02c0e3f828fccfc4277734f9590b11a65f1
  md5: 6c72ec3e660a51736913ef6ea68c454b
  depends:
  - libgcc-ng >=12
  - libglib >=2.74.1,<3.0a0
  - libstdcxx-ng >=12
  constrains:
  - atk-1.0 2.38.0
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 551928
  timestamp: 1667420962627
- kind: conda
  name: atpublic
  version: 3.0.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/atpublic-3.0.1-pyhd8ed1ab_0.tar.bz2
  sha256: 6720f02d2e2293864f5f6cc8faf8f8a308706caefd7e6324d144ea492450d16b
  md5: 39904aaf8de88f03fe9c27286dc35681
  depends:
  - python >=3.6
  license: Apache-2.0
  license_family: APACHE
  size: 8296
  timestamp: 1641909440570
- kind: conda
  name: attrs
  version: 23.2.0
  build: pyh71513ae_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/attrs-23.2.0-pyh71513ae_0.conda
  sha256: 77c7d03bdb243a048fff398cedc74327b7dc79169ebe3b4c8448b0331ea55fea
  md5: 5e4c0743c70186509d1412e03c2d8dfa
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 54582
  timestamp: 1704011393776
- kind: conda
  name: aws-c-auth
  version: 0.7.16
  build: haed3651_8
  build_number: 8
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.7.16-haed3651_8.conda
  sha256: 75a540b313e5dc212fc0a6057f8a5bee2dda443f17a5a076bd3ea4d7195d483e
  md5: ce96c083829ab2727c942243ac93ffe0
  depends:
  - aws-c-cal >=0.6.10,<0.6.11.0a0
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.6,<0.14.7.0a0
  - aws-c-sdkutils >=0.1.15,<0.1.16.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 103298
  timestamp: 1710281865011
- kind: conda
  name: aws-c-cal
  version: 0.6.10
  build: ha9bf9b1_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.6.10-ha9bf9b1_2.conda
  sha256: e45d9f1eb862f566bdea3d3229dfc74f31e647a72198fe04aab58ccc03a30a37
  md5: ce2471034f5459a39636aacc292c96b6
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 55421
  timestamp: 1709815095625
- kind: conda
  name: aws-c-common
  version: 0.9.14
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.9.14-hd590300_0.conda
  sha256: c71dd835b1d8c7097c8d152a65680f119a203b73a6a62c5aac414bafe5e997ad
  md5: d44fe0d9a6971a4fb245be0055775d9d
  depends:
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 225655
  timestamp: 1709669368566
- kind: conda
  name: aws-c-compression
  version: 0.2.18
  build: h4466546_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.2.18-h4466546_2.conda
  sha256: 7fcc6a924691f9de65c82fd559cb1cb2ebd121c42da544a9a43623d69a284e23
  md5: b0d9153fc7cfa8dc36b8703e1a59f5f3
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 19072
  timestamp: 1709815144275
- kind: conda
  name: aws-c-event-stream
  version: 0.4.2
  build: he635cd5_6
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.4.2-he635cd5_6.conda
  sha256: 38a30beabafc1dd86c0264b6746315a1010e541a1b3ed7f97e1702873e5eaa51
  md5: 58fc78e523e35a08423c913751a51fde
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-io >=0.14.6,<0.14.7.0a0
  - aws-checksums >=0.1.18,<0.1.19.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 53665
  timestamp: 1710263650074
- kind: conda
  name: aws-c-http
  version: 0.8.1
  build: hbfc29b2_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.8.1-hbfc29b2_7.conda
  sha256: 0dc5b73aa31cef3faeeb902a11f12e1244ac241f995d73e4f4e3e0c01622f7a1
  md5: 8476ec099649e9a6de52f7f4d916cd2a
  depends:
  - aws-c-cal >=0.6.10,<0.6.11.0a0
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-compression >=0.2.18,<0.2.19.0a0
  - aws-c-io >=0.14.6,<0.14.7.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 194307
  timestamp: 1710263686092
- kind: conda
  name: aws-c-io
  version: 0.14.6
  build: h6b388c4_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.14.6-h6b388c4_1.conda
  sha256: ac74c99bfba553c6b480f1d1b46a26a2edf60721d8fc67e9d5c9a38a5f136ad6
  md5: 77612630a759ab015f5507c0a14ffb89
  depends:
  - aws-c-cal >=0.6.10,<0.6.11.0a0
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - libgcc-ng >=12
  - s2n >=1.4.7,<1.4.8.0a0
  license: Apache-2.0
  license_family: Apache
  size: 157894
  timestamp: 1710512765311
- kind: conda
  name: aws-c-mqtt
  version: 0.10.3
  build: hffff1cc_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.10.3-hffff1cc_2.conda
  sha256: 6b2de4a0e6e907310127b1025a0030d023e1051da48ea5821dcc6db094d69ab7
  md5: 14ad8defb307e1edb293c3fc9da8648f
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.6,<0.14.7.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 163172
  timestamp: 1710282530222
- kind: conda
  name: aws-c-s3
  version: 0.5.2
  build: h4893938_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.5.2-h4893938_2.conda
  sha256: 312d67b236c9c6003f92f682c55ff344721f79d50d9a4bcdea44f2144f637642
  md5: 7e24759a8b8ead67ce687f3c31ffd12f
  depends:
  - aws-c-auth >=0.7.16,<0.7.17.0a0
  - aws-c-cal >=0.6.10,<0.6.11.0a0
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.6,<0.14.7.0a0
  - aws-checksums >=0.1.18,<0.1.19.0a0
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 105455
  timestamp: 1710296220268
- kind: conda
  name: aws-c-sdkutils
  version: 0.1.15
  build: h4466546_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.1.15-h4466546_2.conda
  sha256: 349a05cf5fbcb3f6f358fc05098b210aa7da4ec3ab6d4719c79bb93b50a629f8
  md5: 258194cedccd33fd8a7b95a8aa105015
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 55383
  timestamp: 1709830510021
- kind: conda
  name: aws-checksums
  version: 0.1.18
  build: h4466546_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.1.18-h4466546_2.conda
  sha256: 9080f064f572ac1747d32b4dff30452ff44ef2df399e6ec7bf9730da1eb99bba
  md5: 8a04fc5a5ecaba31f66904b47dcc7797
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 49940
  timestamp: 1709826415680
- kind: conda
  name: aws-crt-cpp
  version: 0.26.3
  build: h137ae52_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.26.3-h137ae52_2.conda
  sha256: 596b6d63352b7ae189842dc86510d53438f88d1e2c1d56779eeebc130beef2b6
  md5: 21c8acfdfa31ab5582897dda7c9c8a75
  depends:
  - aws-c-auth >=0.7.16,<0.7.17.0a0
  - aws-c-cal >=0.6.10,<0.6.11.0a0
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-event-stream >=0.4.2,<0.4.3.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.6,<0.14.7.0a0
  - aws-c-mqtt >=0.10.3,<0.10.4.0a0
  - aws-c-s3 >=0.5.2,<0.5.3.0a0
  - aws-c-sdkutils >=0.1.15,<0.1.16.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 333634
  timestamp: 1710309442818
- kind: conda
  name: aws-sdk-cpp
  version: 1.11.267
  build: he0cb598_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.267-he0cb598_3.conda
  sha256: 55bf5d47ba2591507abb9b2120905cdb0b1834b2867f03c6cff4bb88f7ec7c58
  md5: ca4aebdc89bb9b08b3b6dd68ae09080d
  depends:
  - aws-c-common >=0.9.14,<0.9.15.0a0
  - aws-c-event-stream >=0.4.2,<0.4.3.0a0
  - aws-checksums >=0.1.18,<0.1.19.0a0
  - aws-crt-cpp >=0.26.3,<0.26.4.0a0
  - libcurl >=8.5.0,<9.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 3636564
  timestamp: 1710322529863
- kind: conda
  name: binutils_impl_linux-64
  version: '2.40'
  build: hf600244_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/binutils_impl_linux-64-2.40-hf600244_0.conda
  sha256: a7e0ea2b71a5b03d82e5a58fb6b612ab1c44d72ce161f9aa441f7ba467cd4c8d
  md5: 33084421a8c0af6aef1b439707f7662a
  depends:
  - ld_impl_linux-64 2.40 h41732ed_0
  - sysroot_linux-64
  license: GPL-3.0-only
  license_family: GPL
  size: 5414922
  timestamp: 1674833958334
- kind: conda
  name: blinker
  version: 1.7.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/blinker-1.7.0-pyhd8ed1ab_0.conda
  sha256: c8d72c2af4f57898dfd5e4c62ae67f7fea1490a37c8b6855460a170d61591177
  md5: 550da20b2c2e38be9cc44bb819fda5d5
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 17886
  timestamp: 1698890303249
- kind: conda
  name: brotli-python
  version: 1.1.0
  build: py311hb755f60_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py311hb755f60_1.conda
  sha256: 559093679e9fdb6061b7b80ca0f9a31fe6ffc213f1dae65bc5c82e2cd1a94107
  md5: cce9e7c3f1c307f2a5fb08a2922d6164
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - libbrotlicommon 1.1.0 hd590300_1
  license: MIT
  license_family: MIT
  size: 351340
  timestamp: 1695990160360
- kind: conda
  name: bwidget
  version: 1.9.14
  build: ha770c72_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/bwidget-1.9.14-ha770c72_1.tar.bz2
  sha256: 9657f8a522da3852ab663e2ac03b1100404bf1d232bf0da4016cbf0386b4c7c0
  md5: 5746d6202ba2abad4a4707f2a2462795
  depends:
  - tk
  license: Tcl/Tk
  license_family: BSD
  size: 122487
  timestamp: 1634380112870
- kind: conda
  name: bzip2
  version: 1.0.8
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-hd590300_5.conda
  sha256: 242c0c324507ee172c0e0dd2045814e746bb303d1eb78870d182ceb0abc726a8
  md5: 69b8b6202a07720f448be700e300ccf4
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 254228
  timestamp: 1699279927352
- kind: conda
  name: c-ares
  version: 1.27.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.27.0-hd590300_0.conda
  sha256: 2a5866b19d28cb963fab291a62ff1c884291b9d6f59de14643e52f103e255749
  md5: f6afff0e9ee08d2f1b897881a4f38cdb
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 163578
  timestamp: 1708684786032
- kind: conda
  name: ca-certificates
  version: 2024.2.2
  build: hbcca054_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2024.2.2-hbcca054_0.conda
  sha256: 91d81bfecdbb142c15066df70cc952590ae8991670198f92c66b62019b251aeb
  md5: 2f4327a1cbe7f022401b236e915a5fef
  license: ISC
  size: 155432
  timestamp: 1706843687645
- kind: conda
  name: cachetools
  version: 5.3.3
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/cachetools-5.3.3-pyhd8ed1ab_0.conda
  sha256: 561b860cba68da76cab8c6504bb5bfb4756ecb2ec9f124d0c17e76caad4f6dfd
  md5: cd4c26c702a9bcdc70ff05b609ddacbe
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 14665
  timestamp: 1708987821240
- kind: conda
  name: cairo
  version: 1.18.0
  build: h3faef2a_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.0-h3faef2a_0.conda
  sha256: 142e2639a5bc0e99c44d76f4cc8dce9c6a2d87330c4beeabb128832cd871a86e
  md5: f907bb958910dc404647326ca80c263e
  depends:
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libglib >=2.78.0,<3.0a0
  - libpng >=1.6.39,<1.7.0a0
  - libstdcxx-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - pixman >=0.42.2,<1.0a0
  - xorg-libice >=1.1.1,<2.0a0
  - xorg-libsm >=1.2.4,<2.0a0
  - xorg-libx11 >=1.8.6,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  - zlib
  license: LGPL-2.1-only or MPL-1.1
  size: 982351
  timestamp: 1697028423052
- kind: conda
  name: certifi
  version: 2024.2.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/certifi-2024.2.2-pyhd8ed1ab_0.conda
  sha256: f1faca020f988696e6b6ee47c82524c7806380b37cfdd1def32f92c326caca54
  md5: 0876280e409658fc6f9e75d035960333
  depends:
  - python >=3.7
  license: ISC
  size: 160559
  timestamp: 1707022289175
- kind: conda
  name: cffi
  version: 1.16.0
  build: py311hb3a22ac_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.16.0-py311hb3a22ac_0.conda
  sha256: b71c94528ca0c35133da4b7ef69b51a0b55eeee570376057f3d2ad60c3ab1444
  md5: b3469563ac5e808b0cd92810d0697043
  depends:
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - pycparser
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  size: 300207
  timestamp: 1696001873452
- kind: conda
  name: charset-normalizer
  version: 3.3.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.3.2-pyhd8ed1ab_0.conda
  sha256: 20cae47d31fdd58d99c4d2e65fbdcefa0b0de0c84e455ba9d6356a4bdbc4b5b9
  md5: 7f4a9e3fcff3f6356ae99244a014da6a
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 46597
  timestamp: 1698833765762
- kind: conda
  name: click
  version: 8.1.7
  build: unix_pyh707e725_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/click-8.1.7-unix_pyh707e725_0.conda
  sha256: f0016cbab6ac4138a429e28dbcb904a90305b34b3fe41a9b89d697c90401caec
  md5: f3ad426304898027fc619827ff428eca
  depends:
  - __unix
  - python >=3.8
  license: BSD-3-Clause
  license_family: BSD
  size: 84437
  timestamp: 1692311973840
- kind: conda
  name: colorama
  version: 0.4.6
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_0.tar.bz2
  sha256: 2c1b2e9755ce3102bca8d69e8f26e4f087ece73f50418186aee7c74bef8e1698
  md5: 3faab06a954c2a04039983f2c4a50d99
  depends:
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  size: 25170
  timestamp: 1666700778190
- kind: conda
  name: cryptography
  version: 42.0.5
  build: py311h63ff55d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/cryptography-42.0.5-py311h63ff55d_0.conda
  sha256: d3531a63f2bf9e234a8ebbbcef3dffc0721c8320166e3b86c05e05aef8c02480
  md5: 76909c8c7b915f0af4f35e80da5f9a87
  depends:
  - cffi >=1.12
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0 AND BSD-3-Clause AND PSF-2.0 AND MIT
  license_family: BSD
  size: 1992171
  timestamp: 1708780569743
- kind: conda
  name: curl
  version: 8.6.0
  build: hca28451_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/curl-8.6.0-hca28451_0.conda
  sha256: df635ee24e95ebe7ea53045da0755ab283b5a5f56edabf830bb3c6b5ec2988bb
  md5: 75f03e8c698f2ad76c7c502eec247622
  depends:
  - krb5 >=1.21.2,<1.22.0a0
  - libcurl 8.6.0 hca28451_0
  - libgcc-ng >=12
  - libssh2 >=1.11.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: curl
  license_family: MIT
  size: 93160
  timestamp: 1710591005569
- kind: conda
  name: decorator
  version: 5.1.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/decorator-5.1.1-pyhd8ed1ab_0.tar.bz2
  sha256: 328a6a379f9bdfd0230e51de291ce858e6479411ea4b0545fb377c71662ef3e2
  md5: 43afe5ab04e35e17ba28649471dd7364
  depends:
  - python >=3.5
  license: BSD-2-Clause
  license_family: BSD
  size: 12072
  timestamp: 1641555714315
- kind: conda
  name: duckdb-engine
  version: 0.11.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/duckdb-engine-0.11.2-pyhd8ed1ab_0.conda
  sha256: e0a4f214f6b62a32b928104eae59407204f1f3ee2da2c5bedfde9c3ceffdcd23
  md5: 0ec464ed158f6144039f24bd2ddfea7a
  depends:
  - packaging >=21.3
  - python >=3.7
  - python-duckdb >=0.4.0
  - sqlalchemy >=1.3.19
  license: MIT
  license_family: MIT
  size: 49355
  timestamp: 1709909725925
- kind: conda
  name: expat
  version: 2.6.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/expat-2.6.2-h59595ed_0.conda
  sha256: 89916c536ae5b85bb8bf0cfa27d751e274ea0911f04e4a928744735c14ef5155
  md5: 53fb86322bdb89496d7579fe3f02fd61
  depends:
  - libexpat 2.6.2 h59595ed_0
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 137627
  timestamp: 1710362144873
- kind: conda
  name: filelock
  version: 3.13.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/filelock-3.13.1-pyhd8ed1ab_0.conda
  sha256: 4d742d91412d1f163e5399d2b50c5d479694ebcd309127abb549ca3977f89d2b
  md5: 0c1729b74a8152fde6a38ba0a2ab9f45
  depends:
  - python >=3.7
  license: Unlicense
  size: 15605
  timestamp: 1698715139726
- kind: conda
  name: font-ttf-dejavu-sans-mono
  version: '2.37'
  build: hab24e00_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  size: 397370
  timestamp: 1566932522327
- kind: conda
  name: font-ttf-inconsolata
  version: '3.000'
  build: h77eed37_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  size: 96530
  timestamp: 1620479909603
- kind: conda
  name: font-ttf-source-code-pro
  version: '2.038'
  build: h77eed37_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  size: 700814
  timestamp: 1620479612257
- kind: conda
  name: font-ttf-ubuntu
  version: '0.83'
  build: h77eed37_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_1.conda
  sha256: 056c85b482d58faab5fd4670b6c1f5df0986314cca3bc831d458b22e4ef2c792
  md5: 6185f640c43843e5ad6fd1c5372c3f80
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  size: 1619820
  timestamp: 1700944216729
- kind: conda
  name: fontconfig
  version: 2.14.2
  build: h14ed4e7_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.14.2-h14ed4e7_0.conda
  sha256: 155d534c9037347ea7439a2c6da7c24ffec8e5dd278889b4c57274a1d91e0a83
  md5: 0f69b688f52ff6da70bccb7ff7001d1d
  depends:
  - expat >=2.5.0,<3.0a0
  - freetype >=2.12.1,<3.0a0
  - libgcc-ng >=12
  - libuuid >=2.32.1,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  license: MIT
  license_family: MIT
  size: 272010
  timestamp: 1674828850194
- kind: conda
  name: fonts-conda-ecosystem
  version: '1'
  build: '0'
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  size: 3667
  timestamp: 1566974674465
- kind: conda
  name: fonts-conda-forge
  version: '1'
  build: '0'
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  size: 4102
  timestamp: 1566932280397
- kind: conda
  name: freetype
  version: 2.12.1
  build: h267a509_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.12.1-h267a509_2.conda
  sha256: b2e3c449ec9d907dd4656cb0dc93e140f447175b125a3824b31368b06c666bb6
  md5: 9ae35c3d96db2c94ce0cef86efdfa2cb
  depends:
  - libgcc-ng >=12
  - libpng >=1.6.39,<1.7.0a0
  - libzlib >=1.2.13,<1.3.0a0
  license: GPL-2.0-only OR FTL
  size: 634972
  timestamp: 1694615932610
- kind: conda
  name: fribidi
  version: 1.0.10
  build: h36c2ea0_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
  sha256: 5d7b6c0ee7743ba41399e9e05a58ccc1cfc903942e49ff6f677f6e423ea7a627
  md5: ac7bc6a654f8f41b352b38f4051135f8
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  size: 114383
  timestamp: 1604416621168
- kind: conda
  name: frozenlist
  version: 1.4.1
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.4.1-py311h459d7ec_0.conda
  sha256: 56917dda8da109d51a3b25d30256365e1676f7b2fbaf793a3f003e51548bf794
  md5: b267e553a337e1878512621e374845c5
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: APACHE
  size: 60669
  timestamp: 1702645612671
- kind: conda
  name: fsspec
  version: 2024.3.1
  build: pyhca7485f_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/fsspec-2024.3.1-pyhca7485f_0.conda
  sha256: b8621151939bb5ea4ea4aa84f010e6130a47b1453cd9178283f335816b72a895
  md5: b7f0662ef2c9d4404f0af9eef5ed2fde
  depends:
  - python >=3.8
  license: BSD-3-Clause
  license_family: BSD
  size: 129227
  timestamp: 1710808383964
- kind: conda
  name: gcc_impl_linux-64
  version: 13.2.0
  build: h338b0a0_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gcc_impl_linux-64-13.2.0-h338b0a0_5.conda
  sha256: baab8f8b9af54959735e629cf6d5ec9378166aa4c68ba8dc98dc0a781d548409
  md5: a6be13181cb66a78544b1d5f7bac97d0
  depends:
  - binutils_impl_linux-64 >=2.39
  - libgcc-devel_linux-64 13.2.0 ha9c7c90_105
  - libgcc-ng >=13.2.0
  - libgomp >=13.2.0
  - libsanitizer 13.2.0 h7e041cc_5
  - libstdcxx-ng >=13.2.0
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 53318565
  timestamp: 1706819323755
- kind: conda
  name: gcsfs
  version: 2024.3.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/gcsfs-2024.3.1-pyhd8ed1ab_0.conda
  sha256: 3db31215462e399848d49c5258ce03f6c3e72e7c20fb488ad0b1ebbe6a359607
  md5: 2a794cb30f494b38dd57ece3af30ed6a
  depends:
  - aiohttp
  - decorator >4.1.2
  - fsspec 2024.3.1
  - google-auth >=1.2
  - google-auth-oauthlib
  - google-cloud-storage >1.40
  - python >=3.7
  - requests
  license: BSD-3-Clause
  license_family: BSD
  size: 36679
  timestamp: 1710855169152
- kind: conda
  name: gdk-pixbuf
  version: 2.42.10
  build: h829c605_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.10-h829c605_5.conda
  sha256: bacd1cc3ed77699dec11ea5a670160db3cf701f1b19f34f1a19be36cae25c396
  md5: 8fdb82e5d9694dd8e9ed9ac8fdf48a26
  depends:
  - libglib >=2.78.4,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.7.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 573339
  timestamp: 1710203544212
- kind: conda
  name: gettext
  version: 0.21.1
  build: h27087fc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.21.1-h27087fc_0.tar.bz2
  sha256: 4fcfedc44e4c9a053f0416f9fc6ab6ed50644fca3a761126dbd00d09db1f546a
  md5: 14947d8770185e5153fdd04d4673ed37
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 4320628
  timestamp: 1665673494324
- kind: conda
  name: gflags
  version: 2.2.2
  build: he1b5a44_1004
  build_number: 1004
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-he1b5a44_1004.tar.bz2
  sha256: a853c0cacf53cfc59e1bca8d6e5cdfe9f38fce836f08c2a69e35429c2a492e77
  md5: cddaf2c63ea4a5901cf09524c490ecdc
  depends:
  - libgcc-ng >=7.5.0
  - libstdcxx-ng >=7.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 116549
  timestamp: 1594303828933
- kind: conda
  name: gfortran_impl_linux-64
  version: 13.2.0
  build: h76e1118_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gfortran_impl_linux-64-13.2.0-h76e1118_5.conda
  sha256: 570aee110e4dfc59973ccbbf44193336bada180c7f90e5229328c6726fb69547
  md5: 4685e2c6393800ce0d88d3876ceb7416
  depends:
  - gcc_impl_linux-64 >=13.2.0
  - libgcc-ng >=13.2.0
  - libgcc-ng >=4.9
  - libgfortran5 >=13.2.0
  - libstdcxx-ng >=4.9
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 15832740
  timestamp: 1706819504098
- kind: conda
  name: giflib
  version: 5.2.1
  build: h0b41bf4_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/giflib-5.2.1-h0b41bf4_3.conda
  sha256: 41ec165704ccce2faa0437f4f53c03c06261a2cc9ff7614828e51427d9261f4b
  md5: 96f3b11872ef6fad973eac856cd2624f
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 77385
  timestamp: 1678717794467
- kind: conda
  name: glog
  version: 0.7.0
  build: hed5481d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.0-hed5481d_0.conda
  sha256: 19f41db8f189ed9caec68ffb9ec97d5518b5ee6b58e0636d185f392f688a84a1
  md5: a9ea19c48e11754899299f8123070f4e
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 143596
  timestamp: 1708260910243
- kind: conda
  name: google-api-core
  version: 2.18.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/google-api-core-2.18.0-pyhd8ed1ab_0.conda
  sha256: 29ea75a93c596466ebc3954ac05e1c3298bf9d95296bc4769fdc95c71e53a19e
  md5: 58d10fd3977fa2142cc64c5d9c7a9d20
  depends:
  - google-auth >=2.14.1,<3.0.dev0
  - googleapis-common-protos >=1.56.2,<2.0.dev0
  - proto-plus >=1.22.3,<2.0.0dev
  - protobuf >=3.19.5,<5.0.0.dev0,!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5
  - python >=3.7
  - requests >=2.18.0,<3.0.0.dev0
  license: Apache-2.0
  license_family: APACHE
  size: 84888
  timestamp: 1711095333724
- kind: conda
  name: google-auth
  version: 2.29.0
  build: pyhca7485f_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/google-auth-2.29.0-pyhca7485f_0.conda
  sha256: 1eaa741eba0a6d34c80f68438cb8283b2d9d2adf8629d024df14222c0fc0b397
  md5: a12a2abc807053bc378b218a2a525c7d
  depends:
  - aiohttp >=3.6.2,<4.0.0
  - cachetools >=2.0.0,<6.0
  - cryptography >=38.0.3
  - pyasn1-modules >=0.2.1
  - pyopenssl >=20.0.0
  - python >=3.7
  - pyu2f >=0.1.5
  - requests >=2.20.0,<3.0.0
  - rsa >=3.1.4,<5
  license: Apache-2.0
  license_family: Apache
  size: 106600
  timestamp: 1711011268970
- kind: conda
  name: google-auth-oauthlib
  version: 1.2.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/google-auth-oauthlib-1.2.0-pyhd8ed1ab_0.conda
  sha256: 39d031780d9ac2da430ead078a40ff67db3ad57e24ab1e3c68b4e0f2b48a2311
  md5: 2057f12885a73b4d621c075423cec969
  depends:
  - click >=6.0.0
  - google-auth >=2.15.0
  - python >=3.6
  - requests-oauthlib >=0.7.0
  license: Apache-2.0
  license_family: Apache
  size: 25548
  timestamp: 1702415064894
- kind: conda
  name: google-cloud-core
  version: 2.4.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/google-cloud-core-2.4.1-pyhd8ed1ab_0.conda
  sha256: d01b787bad2ec4da9536ce2cedb3e53ed092fe6a4a596c043ab358bb9b2fbcdd
  md5: 1853cdebbfe25fb6ee253855a44945a6
  depends:
  - google-api-core >=1.31.6,<3.0.0dev,!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0
  - google-auth >=1.25.0,<3.0dev
  - grpcio >=1.38.0,<2.0.0dev
  - python >=3.8
  license: Apache-2.0
  license_family: Apache
  size: 28151
  timestamp: 1702003178653
- kind: conda
  name: google-cloud-storage
  version: 2.16.0
  build: pyhca7485f_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/google-cloud-storage-2.16.0-pyhca7485f_0.conda
  sha256: 7c196842cb591516d10af4f90fbf046085f459a326b9cf0e3946f5ec8cae2fbd
  md5: a465dd6977e00f7fd955f03e787a745b
  depends:
  - google-api-core >=2.15.0,<3.0.0dev
  - google-auth >=2.26.1,<3.0dev
  - google-cloud-core >=2.3.0,<3.0dev
  - google-crc32c >=1.0,<2.0dev
  - google-resumable-media >=2.6.0
  - protobuf <5.0.0dev
  - python >=3.7
  - requests >=2.18.0,<3.0.0dev
  license: Apache-2.0
  license_family: APACHE
  size: 89336
  timestamp: 1710833529269
- kind: conda
  name: google-crc32c
  version: 1.1.2
  build: py311h9b08b9c_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/google-crc32c-1.1.2-py311h9b08b9c_5.conda
  sha256: bbd6d4a5d91b8a9e783a03240e906d3cb6fee85ca912f900c46ef027a9eaa289
  md5: 59b908ae2a7e328eae0ccb03fa3fa0dd
  depends:
  - cffi >=1.0.0
  - libcrc32c >=1.1.2,<1.2.0a0
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  size: 25646
  timestamp: 1695545439302
- kind: conda
  name: google-resumable-media
  version: 2.7.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/google-resumable-media-2.7.0-pyhd8ed1ab_0.conda
  sha256: b7f08e89a491cfaa904328b96c1700da18d2cc33affefc2d15077e03ad4ec8bf
  md5: 28d1e160d8b2e405b16bb40773135225
  depends:
  - google-crc32c >=1.0,<2.0.0dev
  - python >=3.7
  constrains:
  - aiohttp >=3.6.2,<4.0.0dev
  - requests >=2.18.0,<3.0.0dev
  license: Apache-2.0
  license_family: APACHE
  size: 46001
  timestamp: 1702437194280
- kind: conda
  name: googleapis-common-protos
  version: 1.63.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/googleapis-common-protos-1.63.0-pyhd8ed1ab_0.conda
  sha256: 41d3eea46623836e2be7234bdbfc0e7a42fc0853229c687cea6d7b652bb4a4fa
  md5: 058e77f4f0285aa4945c5539de931ff0
  depends:
  - protobuf >=3.19.5,<5.0.0dev0,!=3.20.0,!=3.20.1,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5
  - python >=3.7
  license: Apache-2.0
  license_family: APACHE
  size: 121696
  timestamp: 1710166475119
- kind: conda
  name: graphite2
  version: 1.3.13
  build: h58526e2_1001
  build_number: 1001
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.13-h58526e2_1001.tar.bz2
  sha256: 65da967f3101b737b08222de6a6a14e20e480e7d523a5d1e19ace7b960b5d6b1
  md5: 8c54672728e8ec6aa6db90cf2806d220
  depends:
  - libgcc-ng >=7.5.0
  - libstdcxx-ng >=7.5.0
  license: LGPLv2
  size: 104701
  timestamp: 1604365484436
- kind: conda
  name: graphviz
  version: 9.0.0
  build: h78e8752_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/graphviz-9.0.0-h78e8752_1.conda
  sha256: 1813800d655c120a3941d543a6fc64e3c178c737f1c84f6b7ebe1f19f27fa4fb
  md5: a3f4cd4a512ec5db35ffbf25ba11f537
  depends:
  - cairo >=1.18.0,<2.0a0
  - fonts-conda-ecosystem
  - gdk-pixbuf >=2.42.10,<3.0a0
  - gtk2
  - gts >=0.7.6,<0.8.0a0
  - libexpat >=2.5.0,<3.0a0
  - libgcc-ng >=12
  - libgd >=2.3.3,<2.4.0a0
  - libglib >=2.78.1,<3.0a0
  - librsvg >=2.56.3,<3.0a0
  - libstdcxx-ng >=12
  - libwebp-base >=1.3.2,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - pango >=1.50.14,<2.0a0
  license: EPL-1.0
  license_family: Other
  size: 2310834
  timestamp: 1700901584973
- kind: conda
  name: greenlet
  version: 3.0.3
  build: py311hb755f60_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/greenlet-3.0.3-py311hb755f60_0.conda
  sha256: e6228b46b15ee3f54592c8a1fc1bf3846d519719ac65c238c20e21eb431971ec
  md5: 6f4b03b4d1e0da0962ea02113382677c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  size: 236045
  timestamp: 1703201735151
- kind: conda
  name: grpcio
  version: 1.62.1
  build: py311ha6695c7_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/grpcio-1.62.1-py311ha6695c7_0.conda
  sha256: 4a3f6694ef81de0e45ac3c07e3e0cc69836d32bc0dceea89c54dee0067e7021c
  md5: 31ecc2fa765a2d4d66aeedf9b2a1e410
  depends:
  - libgcc-ng >=12
  - libgrpc 1.62.1 h15f2491_0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: APACHE
  size: 1045997
  timestamp: 1709938158973
- kind: conda
  name: gtk2
  version: 2.24.33
  build: h280cfa0_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gtk2-2.24.33-h280cfa0_4.conda
  sha256: b946ba60d177d72157cad8af51723f1d081a4794741d35debe53f8b2c807f3af
  md5: 410f86e58e880dcc7b0e910a8e89c05c
  depends:
  - atk-1.0 >=2.38.0
  - cairo >=1.18.0,<2.0a0
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - gdk-pixbuf >=2.42.10,<3.0a0
  - harfbuzz >=8.3.0,<9.0a0
  - libgcc-ng >=12
  - libglib >=2.78.4,<3.0a0
  - pango >=1.50.14,<2.0a0
  - xorg-libx11 >=1.8.7,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: LGPL-2.1-or-later
  size: 6478240
  timestamp: 1710142047337
- kind: conda
  name: gts
  version: 0.7.6
  build: h977cf35_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gts-0.7.6-h977cf35_4.conda
  sha256: b5cd16262fefb836f69dc26d879b6508d29f8a5c5948a966c47fe99e2e19c99b
  md5: 4d8df0b0db060d33c9a702ada998a8fe
  depends:
  - libgcc-ng >=12
  - libglib >=2.76.3,<3.0a0
  - libstdcxx-ng >=12
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 318312
  timestamp: 1686545244763
- kind: conda
  name: gxx_impl_linux-64
  version: 13.2.0
  build: h338b0a0_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gxx_impl_linux-64-13.2.0-h338b0a0_5.conda
  sha256: 9049d84fef7526e1dde8311acd2a592bf1d6f16453e68087c17d1bda01eb7867
  md5: 88d0ccab114eb0e837725bd48cdddae5
  depends:
  - gcc_impl_linux-64 13.2.0 h338b0a0_5
  - libstdcxx-devel_linux-64 13.2.0 ha9c7c90_105
  - sysroot_linux-64
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 13582212
  timestamp: 1706819574801
- kind: conda
  name: harfbuzz
  version: 8.3.0
  build: h3d44ed6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-8.3.0-h3d44ed6_0.conda
  sha256: 4b55aea03b18a4084b750eee531ad978d4a3690f63019132c26c6ad26bbe3aed
  md5: 5a6f6c00ef982a9bc83558d9ac8f64a0
  depends:
  - cairo >=1.18.0,<2.0a0
  - freetype >=2.12.1,<3.0a0
  - graphite2
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libglib >=2.78.1,<3.0a0
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 1547473
  timestamp: 1699925311766
- kind: conda
  name: humanize
  version: 4.9.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/humanize-4.9.0-pyhd8ed1ab_0.conda
  sha256: 2764840b7ef7219af1288b27dfddbacc383fdf88af70980a355afdb2780f3c30
  md5: f15cf5f33b0eca7b02c468ddb0cbf1ad
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 65116
  timestamp: 1700608057210
- kind: conda
  name: ibis-duckdb
  version: 8.0.0
  build: hd8ed1ab_1
  build_number: 1
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/ibis-duckdb-8.0.0-hd8ed1ab_1.conda
  sha256: b53b73a025c246d4288b50973100ae0f06818b99f41b32af0c475f194f566f1d
  md5: 74ef2e02bc2324bb986d046d41bddb71
  depends:
  - duckdb-engine >=0.1.8
  - ibis-framework-core >=8.0.0,<9.0a0
  - python-duckdb >=0.8.1
  - sqlalchemy >=1.4
  - sqlalchemy-views >=0.3.1
  license: Apache-2.0
  license_family: Apache
  size: 10794
  timestamp: 1707236212200
- kind: conda
  name: ibis-framework-core
  version: 8.0.0
  build: pyhd8ed1ab_1
  build_number: 1
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/ibis-framework-core-8.0.0-pyhd8ed1ab_1.conda
  sha256: fd2b5d427a1be516363a1120ffdffe85c0c869cb7c147e0a0803898d3d015f31
  md5: a7b3fc66c6248d2e6617337d025a6877
  depends:
  - atpublic >=2.3
  - filelock >=3.7.0,<4
  - multipledispatch >=0.6,<2
  - numpy >=1.15,<2
  - pandas >=1.2.5
  - parsy >=2
  - pins >=0.8.2
  - pyarrow >=2
  - pyarrow-hotfix >=0.4
  - python >=3.9
  - python-dateutil >=2.8.2
  - python-graphviz >=0.16
  - pytz >=2022.7
  - regex >=2021.7.6
  - rich >=12.4.4
  - sqlglot >=18.7.0,<=20.11
  - toolz >=0.11
  license: Apache-2.0
  license_family: Apache
  size: 992096
  timestamp: 1707236175984
- kind: conda
  name: icu
  version: '73.2'
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/icu-73.2-h59595ed_0.conda
  sha256: e12fd90ef6601da2875ebc432452590bc82a893041473bc1c13ef29001a73ea8
  md5: cc47e1facc155f91abd89b11e48e72ff
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12089150
  timestamp: 1692900650789
- kind: conda
  name: idna
  version: '3.6'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/idna-3.6-pyhd8ed1ab_0.conda
  sha256: 6ee4c986d69ce61e60a20b2459b6f2027baeba153f0a64995fd3cb47c2cc7e07
  md5: 1a76f09108576397c41c0b0c5bd84134
  depends:
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  size: 50124
  timestamp: 1701027126206
- kind: conda
  name: importlib-metadata
  version: 7.1.0
  build: pyha770c72_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-7.1.0-pyha770c72_0.conda
  sha256: cc2e7d1f7f01cede30feafc1118b7aefa244d0a12224513734e24165ae12ba49
  md5: 0896606848b2dc5cebdf111b6543aa04
  depends:
  - python >=3.8
  - zipp >=0.5
  license: Apache-2.0
  license_family: APACHE
  size: 27043
  timestamp: 1710971498183
- kind: conda
  name: importlib-resources
  version: 6.4.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/importlib-resources-6.4.0-pyhd8ed1ab_0.conda
  sha256: 38db827f445ae437a15d50a94816ae67a48285d0700f736af3eb90800a71f079
  md5: dcbadab7a68738a028e195ab68ab2d2e
  depends:
  - importlib_resources >=6.4.0,<6.4.1.0a0
  - python >=3.8
  license: Apache-2.0
  license_family: APACHE
  size: 9657
  timestamp: 1711041029062
- kind: conda
  name: importlib_resources
  version: 6.4.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/importlib_resources-6.4.0-pyhd8ed1ab_0.conda
  sha256: c6ae80c0beaeabb342c5b041f19669992ae6e937dbec56ced766cb035900f9de
  md5: c5d3907ad8bd7bf557521a1833cf7e6d
  depends:
  - python >=3.8
  - zipp >=3.1.0
  constrains:
  - importlib-resources >=6.4.0,<6.4.1.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 33056
  timestamp: 1711041009039
- kind: conda
  name: jinja2
  version: 3.1.3
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.3-pyhd8ed1ab_0.conda
  sha256: fd517b7dd3a61eca34f8a6f9f92f306397149cae1204fce72ac3d227107dafdc
  md5: e7d8df6509ba635247ff9aea31134262
  depends:
  - markupsafe >=2.0
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  size: 111589
  timestamp: 1704967140287
- kind: conda
  name: joblib
  version: 1.3.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/joblib-1.3.2-pyhd8ed1ab_0.conda
  sha256: 31e05d47970d956206188480b038829d24ac11fe8216409d8584d93d40233878
  md5: 4da50d410f553db77e62ab62ffaa1abc
  depends:
  - python >=3.7
  - setuptools
  license: BSD-3-Clause
  license_family: BSD
  size: 221200
  timestamp: 1691577306309
- kind: conda
  name: kernel-headers_linux-64
  version: 2.6.32
  build: he073ed8_17
  build_number: 17
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-2.6.32-he073ed8_17.conda
  sha256: fb39d64b48f3d9d1acc3df208911a41f25b6a00bd54935d5973b4739a9edd5b6
  md5: d731b543793afc0433c4fd593e693fce
  constrains:
  - sysroot_linux-64 ==2.12
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later AND MPL-2.0
  license_family: GPL
  size: 710627
  timestamp: 1708000830116
- kind: conda
  name: keyutils
  version: 1.6.1
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- kind: conda
  name: krb5
  version: 1.21.2
  build: h659d440_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.2-h659d440_0.conda
  sha256: 259bfaae731989b252b7d2228c1330ef91b641c9d68ff87dae02cbae682cb3e4
  md5: cd95826dbd331ed1be26bdf401432844
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.1.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 1371181
  timestamp: 1692097755782
- kind: conda
  name: ld_impl_linux-64
  version: '2.40'
  build: h41732ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.40-h41732ed_0.conda
  sha256: f6cc89d887555912d6c61b295d398cff9ec982a3417d38025c45d5dd9b9e79cd
  md5: 7aca3059a1729aa76c597603f10b0dd3
  constrains:
  - binutils_impl_linux-64 2.40
  license: GPL-3.0-only
  license_family: GPL
  size: 704696
  timestamp: 1674833944779
- kind: conda
  name: lerc
  version: 4.0.0
  build: h27087fc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h27087fc_0.tar.bz2
  sha256: cb55f36dcd898203927133280ae1dc643368af041a48bcf7c026acb7c47b0c12
  md5: 76bbff344f0134279f225174e9064c8f
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 281798
  timestamp: 1657977462600
- kind: conda
  name: libabseil
  version: '20240116.1'
  build: cxx17_h59595ed_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20240116.1-cxx17_h59595ed_2.conda
  sha256: 9951421311285dd4335ad3aceffb223a4d3bc90fb804245508cd27aceb184a29
  md5: 75648bc5dd3b8eab22406876c24d81ec
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  constrains:
  - libabseil-static =20240116.1=cxx17*
  - abseil-cpp =20240116.1
  license: Apache-2.0
  license_family: Apache
  size: 1266503
  timestamp: 1709159756788
- kind: conda
  name: libarrow
  version: 15.0.2
  build: h6bfc85a_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-15.0.2-h6bfc85a_0_cpu.conda
  sha256: 5f823bcc8be280136f39b400e1acc77564fe24fe55a9a6810623f501c57cee70
  md5: 264579aaf4c30e0e3993adac8148f4e8
  depends:
  - aws-crt-cpp >=0.26.3,<0.26.4.0a0
  - aws-sdk-cpp >=1.11.267,<1.11.268.0a0
  - bzip2 >=1.0.8,<2.0a0
  - glog >=0.7.0,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc-ng >=12
  - libgoogle-cloud >=2.22.0,<2.23.0a0
  - libgoogle-cloud-storage >=2.22.0,<2.23.0a0
  - libre2-11 >=2023.9.1,<2024.0a0
  - libstdcxx-ng >=12
  - libutf8proc >=2.8.0,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - lz4-c >=1.9.3,<1.10.0a0
  - orc >=2.0.0,<2.0.1.0a0
  - re2
  - snappy >=1.1.10,<2.0a0
  - zstd >=1.5.5,<1.6.0a0
  constrains:
  - arrow-cpp <0.0a0
  - parquet-cpp <0.0a0
  - apache-arrow-proc =*=cpu
  license: Apache-2.0
  license_family: APACHE
  size: 8188626
  timestamp: 1710809146720
- kind: conda
  name: libarrow-acero
  version: 15.0.2
  build: h59595ed_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-15.0.2-h59595ed_0_cpu.conda
  sha256: 9576e5909d9da92dfa428ad878fabd061b371413ed5b7c6a31dad876e3042ab0
  md5: 22817ab872736063dcc6de8c0946ab29
  depends:
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 598137
  timestamp: 1710809206011
- kind: conda
  name: libarrow-dataset
  version: 15.0.2
  build: h59595ed_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-15.0.2-h59595ed_0_cpu.conda
  sha256: 2f214172fed5d7bfb37b2ba2e123c7a995701d70aece96f1ddb8da07819e7466
  md5: a0c56d498a36835131fac72f38171e00
  depends:
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libarrow-acero 15.0.2 h59595ed_0_cpu
  - libgcc-ng >=12
  - libparquet 15.0.2 h352af49_0_cpu
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 584257
  timestamp: 1710809351100
- kind: conda
  name: libarrow-flight
  version: 15.0.2
  build: hc6145d9_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-15.0.2-hc6145d9_0_cpu.conda
  sha256: 8cbf2d896874d878fdc8543781dc23b243a351d9d225e26321997e52e5ed2b94
  md5: 74056e00f807ccc546578fd19f7b2151
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libgcc-ng >=12
  - libgrpc >=1.62.1,<1.63.0a0
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - ucx >=1.15.0,<1.16.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 504063
  timestamp: 1710809241519
- kind: conda
  name: libarrow-flight-sql
  version: 15.0.2
  build: h757c851_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-sql-15.0.2-h757c851_0_cpu.conda
  sha256: ed9d46ec1eed1295c0d3369c7ea9afd9b01926f4566a7d0c0b7383c81172011b
  md5: 7bfc32845eb60af5b79bfd79e335a548
  depends:
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libarrow-flight 15.0.2 hc6145d9_0_cpu
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 194614
  timestamp: 1710809385541
- kind: conda
  name: libarrow-gandiva
  version: 15.0.2
  build: hb016d2e_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-gandiva-15.0.2-hb016d2e_0_cpu.conda
  sha256: 20d9dde9e6951c26bfa24fd868cfe67d9cdbb0e1dcacab526f13facfb0c12c8b
  md5: f726794e5792692e98712d054d4d5d20
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libgcc-ng >=12
  - libllvm16 >=16.0.6,<16.1.0a0
  - libre2-11 >=2023.9.1,<2024.0a0
  - libstdcxx-ng >=12
  - libutf8proc >=2.8.0,<3.0a0
  - openssl >=3.2.1,<4.0a0
  - re2
  license: Apache-2.0
  license_family: APACHE
  size: 896348
  timestamp: 1710809278664
- kind: conda
  name: libarrow-substrait
  version: 15.0.2
  build: h757c851_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-15.0.2-h757c851_0_cpu.conda
  sha256: 7235e23ad8310b5ef455c94ddb554a700c578df48f280d17b0e3e51288ce0afe
  md5: c70f0e169c54b1b79c67ab11d5abb401
  depends:
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libarrow-acero 15.0.2 h59595ed_0_cpu
  - libarrow-dataset 15.0.2 h59595ed_0_cpu
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 519713
  timestamp: 1710809420564
- kind: conda
  name: libblas
  version: 3.9.0
  build: 21_linux64_openblas
  build_number: 21
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-21_linux64_openblas.conda
  sha256: ebd5c91f029f779fb88a1fcbd1e499559a9c258e3674ff58a2fbb4e375ae56d9
  md5: 0ac9f44fc096772b0aa092119b00c3ca
  depends:
  - libopenblas >=0.3.26,<0.3.27.0a0
  - libopenblas >=0.3.26,<1.0a0
  constrains:
  - liblapacke 3.9.0 21_linux64_openblas
  - blas * openblas
  - libcblas 3.9.0 21_linux64_openblas
  - liblapack 3.9.0 21_linux64_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 14691
  timestamp: 1705979549006
- kind: conda
  name: libbrotlicommon
  version: 1.1.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hd590300_1.conda
  sha256: 40f29d1fab92c847b083739af86ad2f36d8154008cf99b64194e4705a1725d78
  md5: aec6c91c7371c26392a06708a73c70e5
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 69403
  timestamp: 1695990007212
- kind: conda
  name: libbrotlidec
  version: 1.1.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hd590300_1.conda
  sha256: 86fc861246fbe5ad85c1b6b3882aaffc89590a48b42d794d3d5c8e6d99e5f926
  md5: f07002e225d7a60a694d42a7bf5ff53f
  depends:
  - libbrotlicommon 1.1.0 hd590300_1
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 32775
  timestamp: 1695990022788
- kind: conda
  name: libbrotlienc
  version: 1.1.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hd590300_1.conda
  sha256: f751b8b1c4754a2a8dfdc3b4040fa7818f35bbf6b10e905a47d3a194b746b071
  md5: 5fc11c6020d421960607d821310fcd4d
  depends:
  - libbrotlicommon 1.1.0 hd590300_1
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 282523
  timestamp: 1695990038302
- kind: conda
  name: libcblas
  version: 3.9.0
  build: 21_linux64_openblas
  build_number: 21
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-21_linux64_openblas.conda
  sha256: 467bbfbfe1a1aeb8b1f9f6485eedd8ed1b6318941bf3702da72336ccf4dc25a6
  md5: 4a3816d06451c4946e2db26b86472cb6
  depends:
  - libblas 3.9.0 21_linux64_openblas
  constrains:
  - liblapacke 3.9.0 21_linux64_openblas
  - blas * openblas
  - liblapack 3.9.0 21_linux64_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 14614
  timestamp: 1705979564122
- kind: conda
  name: libcrc32c
  version: 1.1.2
  build: h9c3ff4c_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
  sha256: fd1d153962764433fe6233f34a72cdeed5dcf8a883a85769e8295ce940b5b0c5
  md5: c965a5aa0d5c1c37ffc62dff36e28400
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 20440
  timestamp: 1633683576494
- kind: conda
  name: libcurl
  version: 8.6.0
  build: hca28451_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.6.0-hca28451_0.conda
  sha256: 357ce806adf1818dc8dccdcd64627758e1858eb0d8a9c91aae4a0eeee2a44608
  md5: 704739398d858872cb91610f49f0ef29
  depends:
  - krb5 >=1.21.2,<1.22.0a0
  - libgcc-ng >=12
  - libnghttp2 >=1.58.0,<2.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: curl
  license_family: MIT
  size: 391187
  timestamp: 1710590979402
- kind: conda
  name: libdeflate
  version: '1.19'
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.19-hd590300_0.conda
  sha256: 985ad27aa0ba7aad82afa88a8ede6a1aacb0aaca950d710f15d85360451e72fd
  md5: 1635570038840ee3f9c71d22aa5b8b6d
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 67080
  timestamp: 1694922285678
- kind: conda
  name: libedit
  version: 3.1.20191231
  build: he28a2e2_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20191231-he28a2e2_2.tar.bz2
  sha256: a57d37c236d8f7c886e01656f4949d9dcca131d2a0728609c6f7fa338b65f1cf
  md5: 4d331e44109e3f0e19b4cb8f9b82f3e1
  depends:
  - libgcc-ng >=7.5.0
  - ncurses >=6.2,<7.0.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 123878
  timestamp: 1597616541093
- kind: conda
  name: libev
  version: '4.33'
  build: hd590300_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- kind: conda
  name: libevent
  version: 2.1.12
  build: hf998b51_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
  sha256: 2e14399d81fb348e9d231a82ca4d816bf855206923759b69ad006ba482764131
  md5: a1cfcc585f0c42bf8d5546bb1dfb668d
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 427426
  timestamp: 1685725977222
- kind: conda
  name: libexpat
  version: 2.6.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.2-h59595ed_0.conda
  sha256: 331bb7c7c05025343ebd79f86ae612b9e1e74d2687b8f3179faec234f986ce19
  md5: e7ba12deb7020dd080c6c70e7b6f6a3d
  depends:
  - libgcc-ng >=12
  constrains:
  - expat 2.6.2.*
  license: MIT
  license_family: MIT
  size: 73730
  timestamp: 1710362120304
- kind: conda
  name: libffi
  version: 3.4.2
  build: h7f98852_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
  sha256: ab6e9856c21709b7b517e940ae7028ae0737546122f83c2aa5d692860c3b149e
  md5: d645c6d2ac96843a2bfaccd2d62b3ac3
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 58292
  timestamp: 1636488182923
- kind: conda
  name: libgcc-devel_linux-64
  version: 13.2.0
  build: ha9c7c90_105
  build_number: 105
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/libgcc-devel_linux-64-13.2.0-ha9c7c90_105.conda
  sha256: 858029ad4d66869c533bb5a22e95e7c044ca66c61d6f403f10d9ae074a0e360e
  md5: 3bc29a967fee57e193ce51f51c598bca
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 2578210
  timestamp: 1706819085946
- kind: conda
  name: libgcc-ng
  version: 13.2.0
  build: h807b86a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-13.2.0-h807b86a_5.conda
  sha256: d32f78bfaac282cfe5205f46d558704ad737b8dbf71f9227788a5ca80facaba4
  md5: d4ff227c46917d3b4565302a2bbb276b
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 13.2.0 h807b86a_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 770506
  timestamp: 1706819192021
- kind: conda
  name: libgd
  version: 2.3.3
  build: h119a65a_9
  build_number: 9
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgd-2.3.3-h119a65a_9.conda
  sha256: b74f95a6e1f3b31a74741b39cba83ed99fc82d17243c0fd3b5ab16ddd48ab89d
  md5: cfebc557e54905dadc355c0e9f003004
  depends:
  - expat
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=73.2,<74.0a0
  - libexpat >=2.5.0,<3.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.39,<1.7.0a0
  - libtiff >=4.6.0,<4.7.0a0
  - libwebp
  - libwebp-base >=1.3.2,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - zlib
  license: GD
  license_family: BSD
  size: 224448
  timestamp: 1696160785971
- kind: conda
  name: libgfortran-ng
  version: 13.2.0
  build: h69a702a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-ng-13.2.0-h69a702a_5.conda
  sha256: 238c16c84124d58307376715839aa152bd4a1bf5a043052938ad6c3137d30245
  md5: e73e9cfd1191783392131e6238bdb3e9
  depends:
  - libgfortran5 13.2.0 ha4646dd_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 23829
  timestamp: 1706819413770
- kind: conda
  name: libgfortran5
  version: 13.2.0
  build: ha4646dd_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-13.2.0-ha4646dd_5.conda
  sha256: ba8d94e8493222ce155bb264d9de4200e41498a458e866fedf444de809bde8b6
  md5: 7a6bd7a12a4bd359e2afe6c0fa1acace
  depends:
  - libgcc-ng >=13.2.0
  constrains:
  - libgfortran-ng 13.2.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 1442769
  timestamp: 1706819209473
- kind: conda
  name: libglib
  version: 2.78.4
  build: h783c2da_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.78.4-h783c2da_0.conda
  sha256: 3a03a5254d2fd29c1e0ffda7250e22991dfbf2c854301fd56c408d97a647cfbd
  md5: d86baf8740d1a906b9716f2a0bac2f2d
  depends:
  - gettext >=0.21.1,<1.0a0
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libiconv >=1.17,<2.0a0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - pcre2 >=10.42,<10.43.0a0
  constrains:
  - glib 2.78.4 *_0
  license: LGPL-2.1-or-later
  size: 2692079
  timestamp: 1708284870228
- kind: conda
  name: libgomp
  version: 13.2.0
  build: h807b86a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgomp-13.2.0-h807b86a_5.conda
  sha256: 0d3d4b1b0134283ea02d58e8eb5accf3655464cf7159abf098cc694002f8d34e
  md5: d211c42b9ce49aee3734fdc828731689
  depends:
  - _libgcc_mutex 0.1 conda_forge
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 419751
  timestamp: 1706819107383
- kind: conda
  name: libgoogle-cloud
  version: 2.22.0
  build: h9be4e54_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.22.0-h9be4e54_1.conda
  sha256: b9980209438b22113f4352df2b260bf43b2eb63a7b6325192ec5ae3a562872ed
  md5: 4b4e36a91e7dabf7345b82d85767a7c3
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libcurl >=8.5.0,<9.0a0
  - libgcc-ng >=12
  - libgrpc >=1.62.0,<1.63.0a0
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - openssl >=3.2.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.22.0 *_1
  license: Apache-2.0
  license_family: Apache
  size: 1209816
  timestamp: 1709737846418
- kind: conda
  name: libgoogle-cloud-storage
  version: 2.22.0
  build: hc7a4891_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.22.0-hc7a4891_1.conda
  sha256: 0e00e1ca2a981db1c96071edf266bc29fd6f13ac484225de1736fc4dac5c64a8
  md5: 7811f043944e010e54640918ea82cecd
  depends:
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc-ng >=12
  - libgoogle-cloud 2.22.0 h9be4e54_1
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  size: 748818
  timestamp: 1709738181078
- kind: conda
  name: libgrpc
  version: 1.62.1
  build: h15f2491_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.62.1-h15f2491_0.conda
  sha256: 1d4ece94dfef73d904dcba0fd9d56098796f5fdc62ea5f9edff60c71be7a3d63
  md5: 564517a8cbd095cff75eb996d33d2b7e
  depends:
  - c-ares >=1.27.0,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libre2-11 >=2023.9.1,<2024.0a0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.62.1
  license: Apache-2.0
  license_family: APACHE
  size: 7667664
  timestamp: 1709938059287
- kind: conda
  name: libiconv
  version: '1.17'
  build: hd590300_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.17-hd590300_2.conda
  sha256: 8ac2f6a9f186e76539439e50505d98581472fedb347a20e7d1f36429849f05c9
  md5: d66573916ffcf376178462f1b61c941e
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  size: 705775
  timestamp: 1702682170569
- kind: conda
  name: libjpeg-turbo
  version: 3.0.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.0.0-hd590300_1.conda
  sha256: b954e09b7e49c2f2433d6f3bb73868eda5e378278b0f8c1dd10a7ef090e14f2f
  md5: ea25936bb4080d843790b586850f82b8
  depends:
  - libgcc-ng >=12
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 618575
  timestamp: 1694474974816
- kind: conda
  name: liblapack
  version: 3.9.0
  build: 21_linux64_openblas
  build_number: 21
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-21_linux64_openblas.conda
  sha256: 64b5c35dce00dd6f9f53178b2fe87116282e00967970bd6551a5a42923806ded
  md5: 1a42f305615c3867684e049e85927531
  depends:
  - libblas 3.9.0 21_linux64_openblas
  constrains:
  - liblapacke 3.9.0 21_linux64_openblas
  - libcblas 3.9.0 21_linux64_openblas
  - blas * openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 14599
  timestamp: 1705979579648
- kind: conda
  name: libllvm16
  version: 16.0.6
  build: hb3ce162_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libllvm16-16.0.6-hb3ce162_3.conda
  sha256: 624fa4012397bc5a8c9269247bf9baa7d907eb59079aefc6f6fa6a40f10fd0ba
  md5: a4d48c40dd5c60edbab7fd69c9a88967
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxml2 >=2.12.1,<3.0.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 35359734
  timestamp: 1701375139881
- kind: conda
  name: libnghttp2
  version: 1.58.0
  build: h47da74e_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.58.0-h47da74e_1.conda
  sha256: 1910c5306c6aa5bcbd623c3c930c440e9c77a5a019008e1487810e3c1d3716cb
  md5: 700ac6ea6d53d5510591c4344d5c989a
  depends:
  - c-ares >=1.23.0,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.0,<4.0a0
  license: MIT
  license_family: MIT
  size: 631936
  timestamp: 1702130036271
- kind: conda
  name: libnl
  version: 3.9.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.9.0-hd590300_0.conda
  sha256: aae03117811e704c3f3666e8374dd2e632f1d78bef0c27330e7298b24004819e
  md5: d27c451db4f1d3c983c78167d2fdabc2
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 732866
  timestamp: 1702657849946
- kind: conda
  name: libnsl
  version: 2.0.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  size: 33408
  timestamp: 1697359010159
- kind: conda
  name: libopenblas
  version: 0.3.26
  build: pthreads_h413a1c8_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.26-pthreads_h413a1c8_0.conda
  sha256: b626954b5a1113dafec8df89fa8bf18ce9b4701464d9f084ddd7fc9fac404bbd
  md5: 760ae35415f5ba8b15d09df5afe8b23a
  depends:
  - libgcc-ng >=12
  - libgfortran-ng
  - libgfortran5 >=12.3.0
  constrains:
  - openblas >=0.3.26,<0.3.27.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 5578031
  timestamp: 1704950143521
- kind: conda
  name: libparquet
  version: 15.0.2
  build: h352af49_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libparquet-15.0.2-h352af49_0_cpu.conda
  sha256: 7c4b921d2e6c8f6c51c3b2b3348362907c58d5d01684ed988937ac5177b3c27a
  md5: ef28915350dafd7d1e06ce67fb7c1c6a
  depends:
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libthrift >=0.19.0,<0.19.1.0a0
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 1180231
  timestamp: 1710809315201
- kind: conda
  name: libpng
  version: 1.6.43
  build: h2797004_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.43-h2797004_0.conda
  sha256: 502f6ff148ac2777cc55ae4ade01a8fc3543b4ffab25c4e0eaa15f94e90dd997
  md5: 009981dd9cfcaa4dbfa25ffaed86bcae
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: zlib-acknowledgement
  size: 288221
  timestamp: 1708780443939
- kind: conda
  name: libprotobuf
  version: 4.25.3
  build: h08a7969_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-4.25.3-h08a7969_0.conda
  sha256: 70e0eef046033af2e8d21251a785563ad738ed5281c74e21c31c457780845dcd
  md5: 6945825cebd2aeb16af4c69d97c32c13
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2811207
  timestamp: 1709514552541
- kind: conda
  name: libre2-11
  version: 2023.09.01
  build: h5a48ba9_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2023.09.01-h5a48ba9_2.conda
  sha256: 3f3c65fe0e9e328b4c1ebc2b622727cef3e5b81b18228cfa6cf0955bc1ed8eff
  md5: 41c69fba59d495e8cf5ffda48a607e35
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  constrains:
  - re2 2023.09.01.*
  license: BSD-3-Clause
  license_family: BSD
  size: 232603
  timestamp: 1708946763521
- kind: conda
  name: librsvg
  version: 2.56.3
  build: he3f83f7_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.56.3-he3f83f7_1.conda
  sha256: b82d0c60376da88a2bf15d35d17c176aa923917ad7de4bc62ddef6d02f3518fb
  md5: 03bd1ddcc942867a19528877143b9852
  depends:
  - cairo >=1.18.0,<2.0a0
  - gdk-pixbuf >=2.42.10,<3.0a0
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libglib >=2.78.1,<3.0a0
  - libxml2 >=2.12.1,<3.0.0a0
  - pango >=1.50.14,<2.0a0
  license: LGPL-2.1-or-later
  size: 5897732
  timestamp: 1701546864628
- kind: conda
  name: libsanitizer
  version: 13.2.0
  build: h7e041cc_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsanitizer-13.2.0-h7e041cc_5.conda
  sha256: 97ecdab7e4e96400d712c2d6ba2b7c30a97278e9f4470ea0ff36bf4f1447b3b9
  md5: 3f686300a92604d1bdff9a29dd4a6639
  depends:
  - libgcc-ng >=13.2.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 4114208
  timestamp: 1706819228913
- kind: conda
  name: libsqlite
  version: 3.45.2
  build: h2797004_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.45.2-h2797004_0.conda
  sha256: 8cdbeb7902729e319510a82d7c642402981818702b58812af265ef55d1315473
  md5: 866983a220e27a80cb75e85cb30466a1
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: Unlicense
  size: 857489
  timestamp: 1710254744982
- kind: conda
  name: libssh2
  version: 1.11.0
  build: h0841786_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.0-h0841786_0.conda
  sha256: 50e47fd9c4f7bf841a11647ae7486f65220cfc988ec422a4475fe8d5a823824d
  md5: 1f5a58e686b13bcfde88b93f547d23fe
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 271133
  timestamp: 1685837707056
- kind: conda
  name: libstdcxx-devel_linux-64
  version: 13.2.0
  build: ha9c7c90_105
  build_number: 105
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/libstdcxx-devel_linux-64-13.2.0-ha9c7c90_105.conda
  sha256: 67e999ee56481844ca4ce2e61132c5c16f3f00a05daa1d0ea4b2c684eea5de5a
  md5: 66383205c2e1bdf013df52fa9e3e6763
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 13020920
  timestamp: 1706819128553
- kind: conda
  name: libstdcxx-ng
  version: 13.2.0
  build: h7e041cc_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-13.2.0-h7e041cc_5.conda
  sha256: a56c5b11f1e73a86e120e6141a42d9e935a99a2098491ac9e15347a1476ce777
  md5: f6f6600d18a4047b54f803cf708b868a
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 3834139
  timestamp: 1706819252496
- kind: conda
  name: libthrift
  version: 0.19.0
  build: hb90f79a_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.19.0-hb90f79a_1.conda
  sha256: 719add2cf20d144ef9962c57cd0f77178259bdb3aae1cded2e2b2b7c646092f5
  md5: 8cdb7d41faa0260875ba92414c487e2d
  depends:
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.3,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 409409
  timestamp: 1695958011498
- kind: conda
  name: libtiff
  version: 4.6.0
  build: ha9c0a0a_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.6.0-ha9c0a0a_2.conda
  sha256: 45158f5fbee7ee3e257e6b9f51b9f1c919ed5518a94a9973fe7fa4764330473e
  md5: 55ed21669b2015f77c180feb1dd41930
  depends:
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.19,<1.20.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libstdcxx-ng >=12
  - libwebp-base >=1.3.2,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - xz >=5.2.6,<6.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: HPND
  size: 283198
  timestamp: 1695661593314
- kind: conda
  name: libutf8proc
  version: 2.8.0
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.8.0-h166bdaf_0.tar.bz2
  sha256: 49082ee8d01339b225f7f8c60f32a2a2c05fe3b16f31b554b4fb2c1dea237d1c
  md5: ede4266dc02e875fe1ea77b25dd43747
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 101070
  timestamp: 1667316029302
- kind: conda
  name: libuuid
  version: 2.38.1
  build: h0b41bf4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- kind: conda
  name: libwebp
  version: 1.3.2
  build: h658648e_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libwebp-1.3.2-h658648e_1.conda
  sha256: cc5e55531d8067ea379b145861aea8c749a545912bc016372f5e3c69cc925efd
  md5: 0ebb65e8d86843865796c7c95a941f34
  depends:
  - giflib >=5.2.1,<5.3.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.39,<1.7.0a0
  - libtiff >=4.6.0,<4.7.0a0
  - libwebp-base 1.3.2.*
  - libwebp-base >=1.3.2,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 84938
  timestamp: 1696116247718
- kind: conda
  name: libwebp-base
  version: 1.3.2
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.3.2-hd590300_0.conda
  sha256: 68764a760fa81ef35dacb067fe8ace452bbb41476536a4a147a1051df29525f0
  md5: 30de3fd9b3b602f7473f30e684eeea8c
  depends:
  - libgcc-ng >=12
  constrains:
  - libwebp 1.3.2
  license: BSD-3-Clause
  license_family: BSD
  size: 401830
  timestamp: 1694709121323
- kind: conda
  name: libxcb
  version: '1.15'
  build: h0b41bf4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.15-h0b41bf4_0.conda
  sha256: a670902f0a3173a466c058d2ac22ca1dd0df0453d3a80e0212815c20a16b0485
  md5: 33277193f5b92bad9fdd230eb700929c
  depends:
  - libgcc-ng >=12
  - pthread-stubs
  - xorg-libxau
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 384238
  timestamp: 1682082368177
- kind: conda
  name: libxcrypt
  version: 4.4.36
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 100393
  timestamp: 1702724383534
- kind: conda
  name: libxml2
  version: 2.12.6
  build: h232c23b_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.12.6-h232c23b_0.conda
  sha256: 4646ae14fb226080d2bfeb89510147abebd603bab1c80bb6b3c02a01c10c6ee5
  md5: d86653ff5ccb88bf7f13833fdd8789e0
  depends:
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libiconv >=1.17,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - xz >=5.2.6,<6.0a0
  license: MIT
  license_family: MIT
  size: 704019
  timestamp: 1710715057008
- kind: conda
  name: libzlib
  version: 1.2.13
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.2.13-hd590300_5.conda
  sha256: 370c7c5893b737596fd6ca0d9190c9715d89d888b8c88537ae1ef168c25e82e4
  md5: f36c115f1ee199da648e0597ec2047ad
  depends:
  - libgcc-ng >=12
  constrains:
  - zlib 1.2.13 *_5
  license: Zlib
  license_family: Other
  size: 61588
  timestamp: 1686575217516
- kind: conda
  name: lz4-c
  version: 1.9.4
  build: hcb278e6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.9.4-hcb278e6_0.conda
  sha256: 1b4c105a887f9b2041219d57036f72c4739ab9e9fe5a1486f094e58c76b31f5f
  md5: 318b08df404f9c9be5712aaa5a6f0bb0
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 143402
  timestamp: 1674727076728
- kind: conda
  name: make
  version: '4.3'
  build: hd18ef5c_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/make-4.3-hd18ef5c_1.tar.bz2
  sha256: 4a5fe7c80bb0de0015328e2d3fc8db1736f528cb1fd53cd0d5527e24269a4f7c
  md5: 4049ebfd3190b580dffe76daed26155a
  depends:
  - libgcc-ng >=7.5.0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 518896
  timestamp: 1602706451788
- kind: conda
  name: markdown-it-py
  version: 3.0.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_0.conda
  sha256: c041b0eaf7a6af3344d5dd452815cdc148d6284fec25a4fa3f4263b3a021e962
  md5: 93a8e71256479c62074356ef6ebf501b
  depends:
  - mdurl >=0.1,<1
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 64356
  timestamp: 1686175179621
- kind: conda
  name: markupsafe
  version: 2.1.5
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-2.1.5-py311h459d7ec_0.conda
  sha256: 14912e557a6576e03f65991be89e9d289c6e301921b6ecfb4e7186ba974f453d
  md5: a322b4185121935c871d201ae00ac143
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 27502
  timestamp: 1706900084436
- kind: conda
  name: mdurl
  version: 0.1.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_0.conda
  sha256: 64073dfb6bb429d52fff30891877b48c7ec0f89625b1bf844905b66a81cce6e1
  md5: 776a8dd9e824f77abac30e6ef43a8f7a
  depends:
  - python >=3.6
  license: MIT
  license_family: MIT
  size: 14680
  timestamp: 1704317789138
- kind: conda
  name: multidict
  version: 6.0.5
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.0.5-py311h459d7ec_0.conda
  sha256: aa20fb2d8ecb16099126ec5607fc12082de4111b5e4882e944f4b6cd846178d9
  md5: 4288ea5cbe686d1b18fc3efb36c009a5
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: APACHE
  size: 61944
  timestamp: 1707040860316
- kind: conda
  name: multipledispatch
  version: 0.6.0
  build: py_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/multipledispatch-0.6.0-py_0.tar.bz2
  sha256: 6d5839f75780475ad4dffe018026d493e26076f95862550a48424b4d7f6689d8
  md5: 1073dc92c8f247d94ac14dd79ca0bbec
  depends:
  - python
  - six
  license: BSD 3-Clause
  license_family: BSD
  size: 12299
  timestamp: 1534825527617
- kind: conda
  name: ncurses
  version: 6.4.20240210
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.4.20240210-h59595ed_0.conda
  sha256: aa0f005b6727aac6507317ed490f0904430584fa8ca722657e7f0fb94741de81
  md5: 97da8860a0da5413c7c98a3b3838a645
  depends:
  - libgcc-ng >=12
  license: X11 AND BSD-3-Clause
  size: 895669
  timestamp: 1710866638986
- kind: conda
  name: numpy
  version: 1.26.4
  build: py311h64a7726_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/numpy-1.26.4-py311h64a7726_0.conda
  sha256: 3f4365e11b28e244c95ba8579942b0802761ba7bb31c026f50d1a9ea9c728149
  md5: a502d7aad449a1206efb366d6a12c52d
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 8065890
  timestamp: 1707225944355
- kind: conda
  name: oauthlib
  version: 3.2.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/oauthlib-3.2.2-pyhd8ed1ab_0.tar.bz2
  sha256: 0cfd5146a91d3974f4abfc2a45de890371d510a77238fe553e036ec8c031dc5b
  md5: 8f882b197fd9c4941a787926baea4868
  depends:
  - blinker
  - cryptography
  - pyjwt >=1.0.0
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  size: 91937
  timestamp: 1666056461148
- kind: conda
  name: openssl
  version: 3.2.1
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.2.1-hd590300_1.conda
  sha256: 2c689444ed19a603be457284cf2115ee728a3fafb7527326e96054dee7cdc1a7
  md5: 9d731343cff6ee2e5a25c4a091bf8e2a
  depends:
  - ca-certificates
  - libgcc-ng >=12
  constrains:
  - pyopenssl >=22.1
  license: Apache-2.0
  license_family: Apache
  size: 2865379
  timestamp: 1710793235846
- kind: conda
  name: orc
  version: 2.0.0
  build: h1e5e2c1_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/orc-2.0.0-h1e5e2c1_0.conda
  sha256: ed8cfe1f35e8ef703e540e7731e77fade1410bba406e17727a10dee08c37d5b4
  md5: 53e8f030579d34e1a36a735d527c021f
  depends:
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - lz4-c >=1.9.3,<1.10.0a0
  - snappy >=1.1.10,<2.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  size: 1028974
  timestamp: 1710232781925
- kind: conda
  name: packaging
  version: '24.0'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/packaging-24.0-pyhd8ed1ab_0.conda
  sha256: a390182d74c31dfd713c16db888c92c277feeb6d1fe96ff9d9c105f9564be48a
  md5: 248f521b64ce055e7feae3105e7abeb8
  depends:
  - python >=3.8
  license: Apache-2.0
  license_family: APACHE
  size: 49832
  timestamp: 1710076089469
- kind: conda
  name: pandas
  version: 2.2.1
  build: py311h320fe9a_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.2.1-py311h320fe9a_0.conda
  sha256: ce9e6dab534466e04c5d09cc341a5e2ee6b0ef8eaa05052b22484582919cd38c
  md5: aac8d7137fedc2fd5f8320bf50e4204c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - numpy >=1.23.5,<2.0a0
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.8.1
  - python-tzdata >=2022a
  - python_abi 3.11.* *_cp311
  - pytz >=2020.1
  license: BSD-3-Clause
  license_family: BSD
  size: 15775689
  timestamp: 1708709340605
- kind: conda
  name: pandoc
  version: ********
  build: ha770c72_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pandoc-********-ha770c72_0.conda
  sha256: 26bfcda675fbddd059a8861dc75b9e497980ec6c679ec2a27e7d74042c4b295b
  md5: cdea66892b19a454f939487318b6c517
  license: GPL-2.0-or-later
  license_family: GPL
  size: 21002590
  timestamp: 1710766932698
- kind: conda
  name: pango
  version: 1.52.1
  build: ha41ecd1_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pango-1.52.1-ha41ecd1_0.conda
  sha256: 53d3442fb39eb9f0ac36646769469f2f825afaeda984719002460efd7c3d354f
  md5: 5c0cc002bf4eaa56448b0729efd6e96c
  depends:
  - cairo >=1.18.0,<2.0a0
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=8.3.0,<9.0a0
  - libgcc-ng >=12
  - libglib >=2.78.4,<3.0a0
  - libpng >=1.6.43,<1.7.0a0
  license: LGPL-2.1-or-later
  size: 444188
  timestamp: 1709762011295
- kind: conda
  name: parsy
  version: '2.1'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/parsy-2.1-pyhd8ed1ab_0.conda
  sha256: 0eabc374ac44066578c25172e7b4d33317b1a6bbdee3d0a8ded6927544e9cafc
  md5: 0423aa726dfb35b9a236ea3d572b8b74
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 14481
  timestamp: 1677097346745
- kind: conda
  name: pcre2
  version: '10.42'
  build: hcad00b1_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.42-hcad00b1_0.conda
  sha256: 3ca54ff0abcda964af7d4724d389ae20d931159ae1881cfe57ad4b0ab9e6a380
  md5: 679c8961826aa4b50653bce17ee52abe
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1017235
  timestamp: 1698610864983
- kind: conda
  name: pins
  version: 0.8.4
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pins-0.8.4-pyhd8ed1ab_0.conda
  sha256: 225ccb8e589d39efd31b5ce384f2e6cdbd5b5cd681495e6c25153b7e14181734
  md5: 727681e40831100ac9dd56ba5ddc41d8
  depends:
  - appdirs <2.0.0
  - fsspec >=2022.2.0
  - humanize >=1.0.0
  - importlib-metadata >=4.4
  - importlib-resources >=1.3
  - jinja2 >=2.10.0
  - joblib >=0.12.0
  - pandas >=0.23.0
  - python >=3.8
  - python-xxhash >=1.0.0
  - pyyaml >=3.13
  - requests
  license: MIT
  license_family: MIT
  size: 79936
  timestamp: 1704342438654
- kind: conda
  name: pixman
  version: 0.43.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.43.2-h59595ed_0.conda
  sha256: 366d28e2a0a191d6c535e234741e0cd1d94d713f76073d8af4a5ccb2a266121e
  md5: 71004cbf7924e19c02746ccde9fd7123
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 386826
  timestamp: 1706549500138
- kind: conda
  name: proto-plus
  version: 1.23.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/proto-plus-1.23.0-pyhd8ed1ab_0.conda
  sha256: 2c9ca8233672032fb372792b1e4c2a556205e631dc375c2c606eab478f32349d
  md5: 26c043ffe1c027eaed894d70ea04a18d
  depends:
  - protobuf >=3.19.0,<5.0.0dev
  - python >=3.6
  license: Apache-2.0
  license_family: APACHE
  size: 41525
  timestamp: 1702003481862
- kind: conda
  name: protobuf
  version: 4.25.3
  build: py311h7b78aeb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/protobuf-4.25.3-py311h7b78aeb_0.conda
  sha256: 90eccef0b175777de1d179fc66e47af47ad0ae2bb9a949a08cc1d42b8b1ec57f
  md5: fe6c263e6bd0ec098995b7cd176b0f95
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - setuptools
  license: BSD-3-Clause
  license_family: BSD
  size: 399460
  timestamp: 1709685919243
- kind: conda
  name: pthread-stubs
  version: '0.4'
  build: h36c2ea0_1001
  build_number: 1001
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-h36c2ea0_1001.tar.bz2
  sha256: 67c84822f87b641d89df09758da498b2d4558d47b920fd1d3fe6d3a871e000ff
  md5: 22dad4df6e8630e8dff2428f6f6a7036
  depends:
  - libgcc-ng >=7.5.0
  license: MIT
  license_family: MIT
  size: 5625
  timestamp: 1606147468727
- kind: conda
  name: pyarrow
  version: 15.0.2
  build: py311h39c9aba_0_cpu
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-15.0.2-py311h39c9aba_0_cpu.conda
  sha256: 5edeaf418cf773b0b6eb3e347427fac3daf2a5785d2829c2f6ac9e602e638223
  md5: ad6ed6be2cc0a3ab1b50f1cfe40533e3
  depends:
  - libarrow 15.0.2 h6bfc85a_0_cpu
  - libarrow-acero 15.0.2 h59595ed_0_cpu
  - libarrow-dataset 15.0.2 h59595ed_0_cpu
  - libarrow-flight 15.0.2 hc6145d9_0_cpu
  - libarrow-flight-sql 15.0.2 h757c851_0_cpu
  - libarrow-gandiva 15.0.2 hb016d2e_0_cpu
  - libarrow-substrait 15.0.2 h757c851_0_cpu
  - libgcc-ng >=12
  - libparquet 15.0.2 h352af49_0_cpu
  - libstdcxx-ng >=12
  - numpy >=1.23.5,<2.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - apache-arrow-proc =*=cpu
  license: Apache-2.0
  license_family: APACHE
  size: 4566833
  timestamp: 1710812365538
- kind: conda
  name: pyarrow-hotfix
  version: '0.6'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pyarrow-hotfix-0.6-pyhd8ed1ab_0.conda
  sha256: 9b767969d059c106aac6596438a7e7ebd3aa1e2ff6553d4b7e05126dfebf4bd6
  md5: ccc06e6ef2064ae129fab3286299abda
  depends:
  - pyarrow >=0.14
  - python >=3.5
  license: Apache-2.0
  license_family: APACHE
  size: 13567
  timestamp: 1700596511761
- kind: conda
  name: pyasn1
  version: 0.5.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pyasn1-0.5.1-pyhd8ed1ab_0.conda
  sha256: 8b116da9acbb471e107203c11acaffcb259aca2367aa7e83e796e43ed5d381b3
  md5: fb1a800972b072aa4d16450983c81418
  depends:
  - python !=3.0,!=3.1,!=3.2,!=3.3,!=3.4,!=3.5
  license: BSD-2-Clause
  license_family: BSD
  size: 63586
  timestamp: 1701287134208
- kind: conda
  name: pyasn1-modules
  version: 0.3.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pyasn1-modules-0.3.0-pyhd8ed1ab_0.conda
  sha256: 7867ba43b6ef1e66054ca6b70f59bbef4cdb0cc761f0be3b66d79d15bd43143b
  md5: 26db749166cdca55e5ef1ffdc7767d0e
  depends:
  - pyasn1 >=0.4.6,<0.6.0
  - python >=3.6
  license: BSD-2-Clause
  license_family: BSD
  size: 95605
  timestamp: 1695108003656
- kind: conda
  name: pycparser
  version: '2.21'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.21-pyhd8ed1ab_0.tar.bz2
  sha256: 74c63fd03f1f1ea2b54e8bc529fd1a600aaafb24027b738d0db87909ee3a33dc
  md5: 076becd9e05608f8dc72757d5f3a91ff
  depends:
  - python ==2.7.*|>=3.4
  license: BSD-3-Clause
  license_family: BSD
  size: 102747
  timestamp: 1636257201998
- kind: conda
  name: pygments
  version: 2.17.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pygments-2.17.2-pyhd8ed1ab_0.conda
  sha256: af5f8867450dc292f98ea387d4d8945fc574284677c8f60eaa9846ede7387257
  md5: 140a7f159396547e9799aa98f9f0742e
  depends:
  - python >=3.7
  license: BSD-2-Clause
  license_family: BSD
  size: 860425
  timestamp: 1700608076927
- kind: conda
  name: pyjwt
  version: 2.8.0
  build: pyhd8ed1ab_1
  build_number: 1
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pyjwt-2.8.0-pyhd8ed1ab_1.conda
  sha256: d7cb7fbafd767e938db10820c76a9c16d91faf5a081842159cc185787879eb07
  md5: 74f76d4868dbba5870f2cf1d9b12d8f3
  depends:
  - python >=3.7
  constrains:
  - cryptography >=3.3.1
  license: MIT
  license_family: MIT
  size: 24906
  timestamp: 1706895211122
- kind: conda
  name: pyopenssl
  version: 24.0.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pyopenssl-24.0.0-pyhd8ed1ab_0.conda
  sha256: bacd1d38585f447e2809e7621283661da7c97cfa20f545edb0ac5838356ed87b
  md5: b50aec2c744a5c493c09cce9e2e7533e
  depends:
  - cryptography >=41.0.5,<43
  - python >=3.7
  license: Apache-2.0
  license_family: Apache
  size: 127070
  timestamp: 1706660212326
- kind: conda
  name: pysocks
  version: 1.7.1
  build: pyha2e5f31_6
  build_number: 6
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha2e5f31_6.tar.bz2
  sha256: a42f826e958a8d22e65b3394f437af7332610e43ee313393d1cf143f0a2d274b
  md5: 2a7de29fb590ca14b5243c4c812c8025
  depends:
  - __unix
  - python >=3.8
  license: BSD-3-Clause
  license_family: BSD
  size: 18981
  timestamp: 1661604969727
- kind: conda
  name: python
  version: 3.11.8
  build: hab00c5b_0_cpython
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.8-hab00c5b_0_cpython.conda
  sha256: f33559d7127b6a892854bc3b2b4be1406c3be9537d658cb13edae57c8c0b5a11
  md5: 2fdc314ee058eda0114738a9309d3683
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.5.0,<3.0a0
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.45.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.2.13,<1.3.0a0
  - ncurses >=6.4,<7.0a0
  - openssl >=3.2.1,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  - xz >=5.2.6,<6.0a0
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  size: 30754113
  timestamp: 1708118457486
- kind: conda
  name: python-dateutil
  version: 2.9.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0-pyhd8ed1ab_0.conda
  sha256: f3ceef02ac164a8d3a080d0d32f8e2ebe10dd29e3a685d240e38b3599e146320
  md5: 2cf4264fffb9e6eff6031c5b6884d61c
  depends:
  - python >=3.7
  - six >=1.5
  license: Apache-2.0
  license_family: APACHE
  size: 222742
  timestamp: 1709299922152
- kind: conda
  name: python-duckdb
  version: 0.10.1
  build: py311hb755f60_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python-duckdb-0.10.1-py311hb755f60_1.conda
  sha256: e34a9fadbbe9e0fa2190a17d8b1d05c68688c58c17afd060e0cf2fdb4557c65a
  md5: e19af67ea4d7672631d1fa5eebfbb4eb
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  size: 22427974
  timestamp: 1710936797886
- kind: conda
  name: python-graphviz
  version: 0.20.3
  build: pyh717bed2_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/python-graphviz-0.20.3-pyh717bed2_0.conda
  sha256: 71365b1f6b7eca79af010bfc184fa00ad05bb86eec3c20aec4ae98b411e056ab
  md5: 031c005eb6d4513013d99ed163dd5f59
  depends:
  - graphviz >=2.46.1
  - python >=3
  license: MIT
  license_family: MIT
  size: 38226
  timestamp: 1711016613215
- kind: conda
  name: python-tzdata
  version: '2024.1'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2024.1-pyhd8ed1ab_0.conda
  sha256: 9da9a849d53705dee450b83507df1ca8ffea5f83bd21a215202221f1c492f8ad
  md5: 98206ea9954216ee7540f0c773f2104d
  depends:
  - python >=3.6
  license: Apache-2.0
  license_family: APACHE
  size: 144024
  timestamp: 1707747742930
- kind: conda
  name: python-xxhash
  version: 3.4.1
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.4.1-py311h459d7ec_0.conda
  sha256: 91293b2ca0f36ac580f2be4b9c0858cdaec52eff95473841231dcd044acd2e12
  md5: 60b5332b3989fda37884b92c7afd6a91
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - xxhash >=0.8.2,<0.8.3.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 23061
  timestamp: 1696486515680
- kind: conda
  name: python_abi
  version: '3.11'
  build: 4_cp311
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.11-4_cp311.conda
  sha256: 0be3ac1bf852d64f553220c7e6457e9c047dfb7412da9d22fbaa67e60858b3cf
  md5: d786502c97404c94d7d58d258a445a65
  constrains:
  - python 3.11.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  size: 6385
  timestamp: 1695147338551
- kind: conda
  name: pytz
  version: '2024.1'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pytz-2024.1-pyhd8ed1ab_0.conda
  sha256: 1a7d6b233f7e6e3bbcbad054c8fd51e690a67b129a899a056a5e45dd9f00cb41
  md5: 3eeeeb9e4827ace8c0c1419c85d590ad
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 188538
  timestamp: 1706886944988
- kind: conda
  name: pyu2f
  version: 0.1.5
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pyu2f-0.1.5-pyhd8ed1ab_0.tar.bz2
  sha256: 667a5a30b65a60b15f38fa4cb09efd6d2762b5a0a9563acd9555eaa5e0b953a2
  md5: caabbeaa83928d0c3e3949261daa18eb
  depends:
  - python >=2.7
  - six
  license: Apache-2.0
  license_family: APACHE
  size: 31876
  timestamp: 1604249020971
- kind: conda
  name: pyyaml
  version: 6.0.1
  build: py311h459d7ec_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.1-py311h459d7ec_1.conda
  sha256: 28729ef1ffa7f6f9dfd54345a47c7faac5d34296d66a2b9891fb147f4efe1348
  md5: 52719a74ad130de8fb5d047dc91f247a
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 200626
  timestamp: 1695373818537
- kind: conda
  name: r-askpass
  version: 1.2.0
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-askpass-1.2.0-r43h57805ef_0.conda
  sha256: c471a4a1f3848496d252ffe1e423e2da7b7794cc828b557b4714a6568d62ba51
  md5: ee095f1d72a1d80864816e44623ba392
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-sys >=2.1
  license: MIT
  license_family: MIT
  size: 31533
  timestamp: 1693780748028
- kind: conda
  name: r-assertthat
  version: 0.2.1
  build: r43hc72bb7e_4
  build_number: 4
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-assertthat-0.2.1-r43hc72bb7e_4.conda
  sha256: f5095a26e49d1dd177dd0202ae68740e8ed87bd09ce9728b9eee0811de11e65b
  md5: ad9918e6b9dcfc83f32ef9fb0776c7aa
  depends:
  - r-base >=4.3,<4.4.0a0
  license: GPL-3
  license_family: GPL3
  size: 71594
  timestamp: 1686752206847
- kind: conda
  name: r-backports
  version: 1.4.1
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-backports-1.4.1-r43h57805ef_2.conda
  sha256: cf3ddffe6b1e94496210ce5b8636d4b66140ef46b79e2b336329595055143a66
  md5: 83adc11bcf92f4d3a565cd9c744c088f
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 109246
  timestamp: 1686679636094
- kind: conda
  name: r-base
  version: 4.3.3
  build: hb8ee39d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-base-4.3.3-hb8ee39d_0.conda
  sha256: 76fa1db75a4591780f46481203fb5e8bc96e7a2194fa4167991e4bb6b6b5145c
  md5: 261cd84cebc431aede55893365f1b178
  depends:
  - _openmp_mutex >=4.5
  - _r-mutex 1.* anacondar_1
  - bwidget
  - bzip2 >=1.0.8,<2.0a0
  - cairo >=1.18.0,<2.0a0
  - curl
  - gcc_impl_linux-64 >=10
  - gfortran_impl_linux-64
  - gxx_impl_linux-64 >=10
  - icu >=73.2,<74.0a0
  - libblas >=3.9.0,<4.0a0
  - libcurl >=8.5.0,<9.0a0
  - libgcc-ng >=12
  - libgfortran-ng
  - libgfortran5 >=10.4.0
  - libglib >=2.78.4,<3.0a0
  - libiconv >=1.17,<2.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libstdcxx-ng >=12
  - libtiff >=4.6.0,<4.7.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - make
  - pango >=1.50.14,<2.0a0
  - pcre2 >=10.42,<10.43.0a0
  - readline >=8.2,<9.0a0
  - sed
  - tk >=8.6.13,<8.7.0a0
  - tktable
  - xorg-libxt
  - xz >=5.2.6,<6.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 25696906
  timestamp: 1709218850032
- kind: conda
  name: r-base64enc
  version: 0.1_3
  build: r43h57805ef_1006
  build_number: 1006
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-base64enc-0.1_3-r43h57805ef_1006.conda
  sha256: ccb2a0d1d48a71f5b1c086f5e9aacf2e381a6c6d14c5f7d972e8787c91e0e04d
  md5: 3cb46d4c0d2096491f9934da0e1555ee
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 45406
  timestamp: 1686703536561
- kind: conda
  name: r-bit
  version: 4.0.5
  build: r43h57805ef_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-bit-4.0.5-r43h57805ef_1.conda
  sha256: b92636ade82f4ba904f815ed025300a6d21ef0d67eb2f04d64f8cd854dc94baf
  md5: 4f116496456867ec75d961cfbca92d69
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 1091075
  timestamp: 1686753403155
- kind: conda
  name: r-bit64
  version: 4.0.5
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-bit64-4.0.5-r43h57805ef_2.conda
  sha256: ea32e3d4d0488de9e816aaa4dcae3dfb99eb63e8f184540dd456709ce2c185ce
  md5: 695aa60fd56064ec2cead86ca641c5f0
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-bit >=4.0.0
  license: GPL-2.0-only
  license_family: GPL2
  size: 485981
  timestamp: 1686764262592
- kind: conda
  name: r-blob
  version: 1.2.4
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-blob-1.2.4-r43hc72bb7e_1.conda
  sha256: 77696ce5f4c85529c3b77611a7be5d4f771e55d4b3cd137bd95413084b7003c4
  md5: 69f3c52f7fc4cf298ba3a45423f7380a
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-rlang
  - r-vctrs >=0.2.1
  license: GPL-3.0-only
  license_family: GPL3
  size: 65758
  timestamp: 1686770504459
- kind: conda
  name: r-broom
  version: 1.0.5
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-broom-1.0.5-r43hc72bb7e_1.conda
  sha256: df6340e8988208a6af567f0b78702398f86c8d2192f2edcb20b3be1247758857
  md5: 9e6d2088231c682265e6840af6ebec29
  depends:
  - r-backports
  - r-base >=4.3,<4.4.0a0
  - r-dplyr >=1.0.0
  - r-ellipsis
  - r-generics >=0.0.2
  - r-ggplot2
  - r-glue
  - r-purrr
  - r-rlang
  - r-stringr
  - r-tibble >=3.0.0
  - r-tidyr >=1.0.0
  license: MIT
  license_family: MIT
  size: 1798091
  timestamp: 1686803328184
- kind: conda
  name: r-bslib
  version: 0.6.1
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-bslib-0.6.1-r43hc72bb7e_0.conda
  sha256: 4f647cc87d97f1a6a5c898f4c11953822ed722657d7553bed1e567fa8c9c055c
  md5: d1af2cc78ea60d9dd3b294677859f80c
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-base64enc
  - r-cachem
  - r-htmltools >=0.5.7
  - r-jquerylib >=0.1.3
  - r-jsonlite
  - r-lifecycle
  - r-memoise >=2.0.1
  - r-mime
  - r-rlang
  - r-sass >=0.4.0
  license: MIT
  license_family: MIT
  size: 5384083
  timestamp: 1701221146992
- kind: conda
  name: r-cachem
  version: 1.0.8
  build: r43h57805ef_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-cachem-1.0.8-r43h57805ef_1.conda
  sha256: d0e3af8ed57fe14c7585120e254deb21e791092834932ae52e9d028c05f5aff3
  md5: 2422888da1907b4f6b2e573ac5e81240
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-fastmap
  - r-rlang
  license: MIT
  license_family: MIT
  size: 75150
  timestamp: 1686752959100
- kind: conda
  name: r-callr
  version: 3.7.5
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-callr-3.7.5-r43hc72bb7e_0.conda
  sha256: e15f1153e06f5982e915516316bb73953911754ce0e3b9092000b139a6f19b17
  md5: c37c171bf006f3e2bf716609c636a9e7
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-processx >=3.4.0
  - r-r6
  license: MIT
  license_family: MIT
  size: 420599
  timestamp: 1708384815205
- kind: conda
  name: r-cellranger
  version: 1.1.0
  build: r43hc72bb7e_1006
  build_number: 1006
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-cellranger-1.1.0-r43hc72bb7e_1006.conda
  sha256: a3d33104541a753f4d1aefaa9b62a69be9b660a00e50adee5355353c324b6d33
  md5: 10e112086717b77f8ce2bb18fd9bf13f
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-rematch
  - r-tibble
  license: MIT
  license_family: MIT
  size: 108323
  timestamp: 1686769818129
- kind: conda
  name: r-cli
  version: 3.6.2
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-cli-3.6.2-r43ha503ecb_0.conda
  sha256: 1e5502e8d54506a6aa841b55227a58059a7a5fbd89eaabc8b753ea9506ec684c
  md5: 7a325f68b1354c1a5261688b5e54d008
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 1260440
  timestamp: 1702292004990
- kind: conda
  name: r-clipr
  version: 0.8.0
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-clipr-0.8.0-r43hc72bb7e_2.conda
  sha256: c5bef1c2579e58b4ecd53573dd2f8ce0bff53412ec1a3b3804044340b9e40897
  md5: 978856edcd203d1c029dbdd4c7252072
  depends:
  - r-base >=4.3,<4.4.0a0
  license: GPL-3.0-only
  license_family: GPL3
  size: 69699
  timestamp: 1686752587937
- kind: conda
  name: r-colorspace
  version: 2.1_0
  build: r43h57805ef_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-colorspace-2.1_0-r43h57805ef_1.conda
  sha256: f62ae6d8e17bad76da8462520ae559db5c0d35ab5eebdd4781b7c7c4f64949ef
  md5: 22117ffc4b380dba0732166eec62d8cb
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2498949
  timestamp: 1686753142134
- kind: conda
  name: r-conflicted
  version: 1.2.0
  build: r43h785f33e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-conflicted-1.2.0-r43h785f33e_1.conda
  sha256: 39e9980f998116fe3025835567a6e0f84a8fe7084c22fd90085780de8c24ebcb
  md5: 423e22e6af80f03aa8ad4722d182e59a
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.4.0
  - r-memoise
  - r-rlang >=1.0.0
  license: MIT
  license_family: MIT
  size: 63184
  timestamp: 1686844763048
- kind: conda
  name: r-cpp11
  version: 0.4.7
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-cpp11-0.4.7-r43hc72bb7e_0.conda
  sha256: addaa7972c59c5b07a77ee9dc41456a442b1ea93d9e7e20e28c05c0a4a01d976
  md5: c9ec548d90ba0c4de83a14dab499d015
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 229308
  timestamp: 1701530319903
- kind: conda
  name: r-crayon
  version: 1.5.2
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-crayon-1.5.2-r43hc72bb7e_2.conda
  sha256: 9852e4e8986cda03b614e98755c494682bd448b069a6597a57777289bc62028b
  md5: f13cc355add93ff41ef3d7aeefee247a
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 164125
  timestamp: 1686659186013
- kind: conda
  name: r-curl
  version: 5.1.0
  build: r43hf9611b0_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-curl-5.1.0-r43hf9611b0_0.conda
  sha256: eef8fb515b516ae11fb2823ab3173a6e9df255ede8baae609b9040360092f27d
  md5: d2a3cc302656e5ff30fc635fd9ef3ae6
  depends:
  - libcurl >=8.3.0,<9.0a0
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 451141
  timestamp: 1696266666351
- kind: conda
  name: r-data.table
  version: 1.15.2
  build: r43h029312a_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-data.table-1.15.2-r43h029312a_0.conda
  sha256: 21a852a836d15afdf13698fee662374a7a0587962a29f9518ab58501b347b0ae
  md5: 833eb6b299c52ab8b31320e1d15ebfa8
  depends:
  - _openmp_mutex >=4.5
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - r-base >=4.3,<4.4.0a0
  license: MPL-2.0
  license_family: OTHER
  size: 2027848
  timestamp: 1709400226772
- kind: conda
  name: r-dbi
  version: 1.2.2
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-dbi-1.2.2-r43hc72bb7e_0.conda
  sha256: f5529b27c2d84564efa7712fa72957bd74ca39ca29be84e4ac2995dc1769154f
  md5: 138a15a75d10f2d69cf5c2003695c8aa
  depends:
  - r-base >=4.3,<4.4.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 851241
  timestamp: 1708092832183
- kind: conda
  name: r-dbplyr
  version: 2.4.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-dbplyr-2.4.0-r43hc72bb7e_0.conda
  sha256: e07af3c63c7c3b840ee97aa66bc6d20b9683b677d9387f83796aeb719e3413ac
  md5: 4a1431ec98cf195b625949cf4219be50
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-blob >=1.2.0
  - r-cli >=3.4.1
  - r-dbi >=1.0.0
  - r-dplyr >=1.1.0
  - r-glue >=1.2.0
  - r-lifecycle >=1.0.3
  - r-magrittr
  - r-pillar >=1.5.0
  - r-purrr >=1.0.1
  - r-r6 >=2.2.2
  - r-rlang >=1.0.6
  - r-tibble >=1.4.2
  - r-tidyr >=1.3.0
  - r-tidyselect >=1.2.0
  - r-vctrs >=0.5.0
  - r-withr
  license: MIT
  license_family: MIT
  size: 1177428
  timestamp: 1698318295159
- kind: conda
  name: r-digest
  version: 0.6.35
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-digest-0.6.35-r43ha503ecb_0.conda
  sha256: 0db1fcf6e35c8296deb0b5a622ce23d6938016cd2be3cf58bd80306cdf30f250
  md5: d28a7db9b060ceaf683f0b036780b962
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 210652
  timestamp: 1710174638983
- kind: conda
  name: r-dplyr
  version: 1.1.4
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-dplyr-1.1.4-r43ha503ecb_0.conda
  sha256: b05bebb0db77912f1481366c666f65a4a01a7a3d8adffb614910deaf6372d4dd
  md5: 7ca8b417326d45b2e4bbf7429a0f1a7b
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-ellipsis
  - r-generics
  - r-glue >=1.3.2
  - r-lifecycle >=1.0.0
  - r-magrittr >=1.5
  - r-pillar >=1.5.1
  - r-r6
  - r-rlang >=0.4.10
  - r-tibble >=2.1.3
  - r-tidyselect >=1.1.0
  - r-vctrs >=0.3.5
  license: MIT
  license_family: MIT
  size: 1403428
  timestamp: 1700248632004
- kind: conda
  name: r-dtplyr
  version: 1.3.1
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-dtplyr-1.3.1-r43hc72bb7e_1.conda
  sha256: 18082ee04ee01a2575ac70a126416da628c875ae1117796ef38447a38eb44e32
  md5: 85ba0e234af279259478526f5cdf952f
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-crayon
  - r-data.table >=1.13.0
  - r-dplyr >=1.0.3
  - r-ellipsis
  - r-glue
  - r-lifecycle
  - r-rlang
  - r-tibble
  - r-tidyselect
  - r-vctrs
  license: MIT
  license_family: MIT
  size: 354463
  timestamp: 1686837793590
- kind: conda
  name: r-ellipsis
  version: 0.3.2
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-ellipsis-0.3.2-r43h57805ef_2.conda
  sha256: 578875dfba72215a96dc154cc1ebd4f99df777551575db3ad36c29d09289346b
  md5: 10c5892991072cfa3a61489d2ac6ad66
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-rlang >=0.3.0
  license: MIT
  license_family: MIT
  size: 42664
  timestamp: 1686666677363
- kind: conda
  name: r-evaluate
  version: '0.23'
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-evaluate-0.23-r43hc72bb7e_0.conda
  sha256: 3fbb9f6fbad75bb85d6e95fdd6bc90e64a7be6e436e6c13e9b707e2a441921a8
  md5: eb2b6777e078c6db2f553ee052c742f3
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 90871
  timestamp: 1698858227414
- kind: conda
  name: r-fansi
  version: 1.0.6
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-fansi-1.0.6-r43h57805ef_0.conda
  sha256: 8435c0a30e202225c3895c106b7f0bc7c7c80431ac3ec035cd93f167be906d92
  md5: c94b12c76504df3548707218409b678f
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 316298
  timestamp: 1702019988259
- kind: conda
  name: r-farver
  version: 2.1.1
  build: r43ha503ecb_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-farver-2.1.1-r43ha503ecb_2.conda
  sha256: 7a8a09609847fee711571dbfee3702cb7ae00f90191ee5a16b5dda76b4380161
  md5: e831143bafe7770b86f3f5c3b5d40dc0
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 1421973
  timestamp: 1686753839676
- kind: conda
  name: r-fastmap
  version: 1.1.1
  build: r43ha503ecb_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-fastmap-1.1.1-r43ha503ecb_1.conda
  sha256: 65eb5b0d106783ecf1da0692cbb7acdd19c22c36846b6acfbba7e23730598653
  md5: dbab70e36b815015fc4a81ac2216aa52
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 72285
  timestamp: 1686703491164
- kind: conda
  name: r-fontawesome
  version: 0.5.2
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-fontawesome-0.5.2-r43hc72bb7e_0.conda
  sha256: 14ac9b7171339b1d424e96a5dbb3a1968614123530888a606b2bf2df7bcc0cdc
  md5: d07f215759462959de39d7232386f1b9
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-htmltools >=*******
  - r-rlang >=0.4.10
  license: MIT
  license_family: MIT
  size: 1308560
  timestamp: 1692429705494
- kind: conda
  name: r-forcats
  version: 1.0.0
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-forcats-1.0.0-r43hc72bb7e_1.conda
  sha256: 643246263d59255d5c9fca19f655663b199e809a54763771bcc8bb8ee36f7ff1
  md5: 9adaad649e10d95039e2bf3d420936ec
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-ellipsis
  - r-glue
  - r-lifecycle
  - r-magrittr
  - r-rlang >=1.0.0
  - r-tibble
  - r-withr
  license: MIT
  license_family: MIT
  size: 422433
  timestamp: 1686757079783
- kind: conda
  name: r-fs
  version: 1.6.3
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-fs-1.6.3-r43ha503ecb_0.conda
  sha256: 3998fcb057d521bf1f964fb2e7a9f30a4c304e74af608d3b6511c0d090263ed1
  md5: 6c2bea06e633bdba57a7513e0d48819b
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 501366
  timestamp: 1689855862380
- kind: conda
  name: r-gargle
  version: 1.5.2
  build: r43h785f33e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-gargle-1.5.2-r43h785f33e_0.conda
  sha256: ec4eb7accfe5a917e08585e776d6b20e815d6020d0a4b5930493992e5606f7c3
  md5: 1e225b4aa916f720473ff4faf8da909f
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.0.0
  - r-fs >=1.3.1
  - r-glue >=1.3.0
  - r-httr >=1.4.0
  - r-jsonlite
  - r-lifecycle
  - r-openssl
  - r-rappdirs
  - r-rlang >=1.0.0
  - r-rstudioapi
  - r-withr
  license: MIT
  license_family: MIT
  size: 705950
  timestamp: 1689882775096
- kind: conda
  name: r-generics
  version: 0.1.3
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-generics-0.1.3-r43hc72bb7e_2.conda
  sha256: 6dc37eda524923a1f55abc722f4ac3799a0b3234b7c050c0fcc29ce8485c2477
  md5: bf424d7238a8a02bd3970346112de34f
  depends:
  - r-base >=4.3,<4.4.0a0
  license: GPL-2
  license_family: GPL2
  size: 90660
  timestamp: 1686752044826
- kind: conda
  name: r-ggplot2
  version: 3.5.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-ggplot2-3.5.0-r43hc72bb7e_0.conda
  sha256: daaf62360853267b564d013a103c682e8643579076f24e7b61a7de062334ccc0
  md5: f5fc39155735cfc3c0c680620301dfa8
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-glue
  - r-gtable >=0.1.1
  - r-isoband
  - r-lifecycle >=1.0.1
  - r-mass
  - r-mgcv
  - r-rlang >=1.0.0
  - r-scales >=1.2.0
  - r-tibble
  - r-vctrs >=0.5.0
  - r-withr >=2.5.0
  license: MIT
  license_family: MIT
  size: 4623639
  timestamp: 1708691201696
- kind: conda
  name: r-glue
  version: 1.7.0
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-glue-1.7.0-r43h57805ef_0.conda
  sha256: 732b60b5ed34b0e4196d07c096728271a6d0206341487d5806c2d9e01d36a9ca
  md5: 20e2cb7c0d27bccf8fe21ec5c3897751
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 155467
  timestamp: 1704853350257
- kind: conda
  name: r-googledrive
  version: 2.1.1
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-googledrive-2.1.1-r43hc72bb7e_1.conda
  sha256: a36e6a29d1fd0c697f5aced197dd6f0c9369868044c3e0a300ecce0b350dafff
  md5: d9210dab882da3bff6327c5b90e7abcf
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-curl >=2.8.1
  - r-gargle >=0.3.1
  - r-glue >=1.2.0
  - r-httr
  - r-jsonlite
  - r-magrittr
  - r-purrr >=0.2.3
  - r-rlang >=0.3.1
  - r-tibble >=2.0.0
  - r-uuid
  license: MIT
  license_family: MIT
  size: 1201306
  timestamp: 1686837961503
- kind: conda
  name: r-googlesheets4
  version: 1.1.1
  build: r43h785f33e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-googlesheets4-1.1.1-r43h785f33e_1.conda
  sha256: b3eb51b8333a889ba40cb8278d71e8a1ba8652ea1b16d5f2e4dfa4c376109821
  md5: 670d1b0ae79909f256ec8decf53283de
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cellranger
  - r-cli >=3.0.0
  - r-curl
  - r-gargle >=1.2.0
  - r-glue >=1.3.0
  - r-googledrive >=2.0.0
  - r-httr
  - r-ids
  - r-magrittr
  - r-purrr
  - r-rematch2
  - r-rlang >=0.4.11
  - r-tibble >=2.1.1
  - r-vctrs >=0.2.3
  license: MIT
  license_family: MIT
  size: 512716
  timestamp: 1686851008614
- kind: conda
  name: r-gtable
  version: 0.3.4
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-gtable-0.3.4-r43hc72bb7e_0.conda
  sha256: ea93666d1db89f24ffa9988064200fe125f93edaaffc903eb9ef8118adf6e981
  md5: 11c5e5e3bd2b41005177c28c1ba46dc3
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-glue
  - r-lifecycle
  - r-rlang
  license: MIT
  license_family: MIT
  size: 222304
  timestamp: 1692625214419
- kind: conda
  name: r-haven
  version: 2.5.4
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-haven-2.5.4-r43ha503ecb_0.conda
  sha256: c34f06667bfd1426f01296dc22377c6493b6caae9f0da96aed07266fcc220997
  md5: 74e6d209acb827345323a9787c569aec
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.0.0
  - r-cpp11
  - r-forcats >=0.2.0
  - r-hms
  - r-lifecycle
  - r-readr >=0.1.0
  - r-rlang >=0.4.0
  - r-tibble
  - r-tidyselect
  - r-vctrs >=0.3.0
  license: MIT
  license_family: MIT
  size: 368345
  timestamp: 1701367026736
- kind: conda
  name: r-highr
  version: '0.10'
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-highr-0.10-r43hc72bb7e_1.conda
  sha256: d5214ae94bae2119fd8dcdf3ab18c7df7dd6e7e62969a5ef661e86101c274ca6
  md5: 323965ffc1eb89aab3f39096b3104869
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-xfun >=0.18
  license: GPL-2.0-or-later
  license_family: GPL
  size: 57536
  timestamp: 1686720508050
- kind: conda
  name: r-hms
  version: 1.1.3
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-hms-1.1.3-r43hc72bb7e_1.conda
  sha256: 1daba924c23fb570576995885dc868cf3b5e0e3b854ec835c8ceb27b46e13c43
  md5: 6f5f6a698ebdd81d5b6f6ec519c5db3b
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-ellipsis
  - r-lifecycle
  - r-pkgconfig
  - r-rlang
  - r-vctrs >=0.2.1
  license: MIT
  license_family: MIT
  size: 107205
  timestamp: 1686752523778
- kind: conda
  name: r-htmltools
  version: 0.5.7
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-htmltools-0.5.7-r43ha503ecb_0.conda
  sha256: 2ab36338c74a05cc274a5280b20e33b7fc37ec16df6a5c6c33fba4c049bd8f21
  md5: a28c36e4d7082cd10965211d036c283d
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-base64enc
  - r-digest
  - r-ellipsis
  - r-fastmap >=1.1.0
  - r-rlang >=0.4.10
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 361672
  timestamp: 1699039035481
- kind: conda
  name: r-httr
  version: 1.4.7
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-httr-1.4.7-r43hc72bb7e_0.conda
  sha256: 68d38f420ca3907ca5ab5ad41e0b28fba07c2e0feaed35605eace25d4a5fc9db
  md5: a1e874d45427efec0c8f1118945acac5
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-curl >=0.9.1
  - r-jsonlite
  - r-mime
  - r-openssl >=0.8
  - r-r6
  license: MIT
  license_family: MIT
  size: 468274
  timestamp: 1692133838126
- kind: conda
  name: r-ids
  version: 1.0.1
  build: r43hc72bb7e_3
  build_number: 3
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-ids-1.0.1-r43hc72bb7e_3.conda
  sha256: a952f9ed15274c980642bda4b06e79bb6576300b2cb0b86126a0568e23dd8e7c
  md5: 9f46f0fd2b5f28f4adc8cbc88cafd847
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-openssl
  - r-uuid
  license: MIT
  license_family: MIT
  size: 126854
  timestamp: 1686837639070
- kind: conda
  name: r-isoband
  version: 0.2.7
  build: r43ha503ecb_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-isoband-0.2.7-r43ha503ecb_2.conda
  sha256: 4c53b162ef9f33dab99a0eba9b224312ab64ae26025b3f436bc12d04bd091441
  md5: 6db3b5f97166c8b0fd8c995fbe32c89d
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 1623060
  timestamp: 1686753330387
- kind: conda
  name: r-janitor
  version: 2.2.0
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-janitor-2.2.0-r43hc72bb7e_1.conda
  sha256: 54d8152226689fb66c9ecbd2937be11c689d7bf21ac4ee6d626bdbad27b4890f
  md5: b6148a843adcda84cbc97b80fbabf6c0
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-dplyr >=1.0.0
  - r-hms
  - r-lifecycle
  - r-lubridate
  - r-magrittr
  - r-purrr
  - r-rlang
  - r-snakecase >=0.9.2
  - r-stringi
  - r-stringr
  - r-tidyr >=0.7.0
  - r-tidyselect >=1.0.0
  license: MIT
  license_family: MIT
  size: 273916
  timestamp: 1686837611699
- kind: conda
  name: r-jquerylib
  version: 0.1.4
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-jquerylib-0.1.4-r43hc72bb7e_2.conda
  sha256: 11c7a3587942a6418b1da9c8e553b2ae942b958364c4c993f3eea2c690465d02
  md5: 49a3fad710cbccec9139b36587842f88
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-htmltools
  license: MIT
  license_family: MIT
  size: 305453
  timestamp: 1686752171197
- kind: conda
  name: r-jsonlite
  version: 1.8.8
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-jsonlite-1.8.8-r43h57805ef_0.conda
  sha256: b80e49e3e1317ce75406f49eba8c8b9f2fb28df8bc612ba915c511c154c1408e
  md5: fb01953d78a87de6211b1ebccafed273
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 634383
  timestamp: 1701709858707
- kind: conda
  name: r-knitr
  version: '1.45'
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-knitr-1.45-r43hc72bb7e_1.conda
  sha256: 43ac2226f9887ca2884284304126db7b2e8a9fa89fcb897acc6fc057fb737ed1
  md5: 22aef90cf219ad54cbe075fc70c29ffe
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-evaluate >=0.15
  - r-highr
  - r-xfun >=0.39
  - r-yaml >=2.1.19
  license: GPL-2.0-or-later
  license_family: GPL
  size: 1310703
  timestamp: 1709077460272
- kind: conda
  name: r-labeling
  version: 0.4.3
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-labeling-0.4.3-r43hc72bb7e_0.conda
  sha256: c4ddb67ad8f15badf290267580ae0646377337ec6dfdaa10ccf88babf4cb9278
  md5: 8d5f7d07433437c48c1c497ec8c77aec
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 68977
  timestamp: 1693351823058
- kind: conda
  name: r-lattice
  version: 0.22_6
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-lattice-0.22_6-r43h57805ef_0.conda
  sha256: f589c94021fa3ce736fa845040d8a39bbee5cbc50b2ca28df8de710c13cdfa8e
  md5: 748c702a5c6c89f9794d148bdcbbdfbc
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 1356482
  timestamp: 1710927304412
- kind: conda
  name: r-lifecycle
  version: 1.0.4
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-lifecycle-1.0.4-r43hc72bb7e_0.conda
  sha256: f6e9c52d478aa1041412ff04d84c9fd99a6eefd54e70fa371070f709af232c19
  md5: 291d2fb68ea1b181644b40d6743ae000
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.4.0
  - r-glue
  - r-rlang >=1.0.6
  license: MIT
  license_family: GPL3
  size: 122157
  timestamp: 1699356816906
- kind: conda
  name: r-lubridate
  version: 1.9.3
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-lubridate-1.9.3-r43h57805ef_0.conda
  sha256: 549e7c7b034cf6e66323cd1a02af57bbade1478bcaf5744e03d56b2ddad9e9a7
  md5: 48f220862d7b0ac7d1397f8c6601fb70
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-generics
  - r-timechange >=0.1.1
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 984301
  timestamp: 1695816555177
- kind: conda
  name: r-magrittr
  version: 2.0.3
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-magrittr-2.0.3-r43h57805ef_2.conda
  sha256: ff4e44161e83ae638a4645a9a66354215770c1f2cdc11b7337a4f48141f3ca94
  md5: 7b04cb810f47eb12efc2e9be5f8ca1f1
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 208890
  timestamp: 1686653917395
- kind: conda
  name: r-mass
  version: 7.3_60
  build: r43h57805ef_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-mass-7.3_60-r43h57805ef_1.conda
  sha256: 860d460774b9a4ca5a78fd9183bad1d6fdb773ff8d5509fd63502c9a95b7a166
  md5: be049620c8ceefdb69a24db0c1919c85
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 1141894
  timestamp: 1686752867356
- kind: conda
  name: r-matrix
  version: 1.6_5
  build: r43h316c678_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-matrix-1.6_5-r43h316c678_0.conda
  sha256: 16b9f6164ae564cec3cfbfc414e3882ad4b9cd39ec4c2fbf8bd0083af3d0e8c4
  md5: 8146ef1b3c54e1a484822085d39357b0
  depends:
  - libblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - r-base >=4.3,<4.4.0a0
  - r-lattice
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 3927776
  timestamp: 1705017785837
- kind: conda
  name: r-memoise
  version: 2.0.1
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-memoise-2.0.1-r43hc72bb7e_2.conda
  sha256: a563bd856ffac75d634eed2d28a9ad5b13c55379a2afd054390d76b2910ef03c
  md5: 6520452e207d8f9bab9f21492f88c48b
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cachem
  - r-rlang >=0.4.10
  license: MIT
  license_family: MIT
  size: 55865
  timestamp: 1686763251123
- kind: conda
  name: r-mgcv
  version: 1.9_1
  build: r43h316c678_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-mgcv-1.9_1-r43h316c678_0.conda
  sha256: 073bb14ab6eff8ba2e283ea64e56ec7ef3a9549fec976fc1e1af837bc53f58df
  md5: c73d9ed0dc98182d712cbea33a3e5d59
  depends:
  - _openmp_mutex >=4.5
  - libblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - r-base >=4.3,<4.4.0a0
  - r-matrix
  - r-nlme >=3.1_64
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 3275118
  timestamp: 1703132523502
- kind: conda
  name: r-mime
  version: '0.12'
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-mime-0.12-r43h57805ef_2.conda
  sha256: ff7a9f3a2ef4c935c7ff074053ea3e781882b0bde0ec9bb53cabd7dc9fc60738
  md5: 348f6adefce6912321b2a2ab0528ae89
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 52868
  timestamp: 1686691371799
- kind: conda
  name: r-modelr
  version: 0.1.11
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-modelr-0.1.11-r43hc72bb7e_1.conda
  sha256: 11ca72e335d1b8ba9ea6fe2ebc7303e61a3b18d17e340288d9449ccde888c5e0
  md5: 992e04a0df956aa1efbf195bf9078bcc
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-broom
  - r-dplyr
  - r-magrittr
  - r-purrr >=0.2.2
  - r-rlang >=0.2.0
  - r-tibble
  - r-tidyr >=0.8.0
  license: GPL-3
  license_family: GPL3
  size: 220633
  timestamp: 1686832369912
- kind: conda
  name: r-munsell
  version: 0.5.0
  build: r43hc72bb7e_1006
  build_number: 1006
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-munsell-0.5.0-r43hc72bb7e_1006.conda
  sha256: e4e17f910b58b2af42d2020a14d8c688780ea47de20fb9b01d08191d237c94fe
  md5: 22f529a11473af44bbe5d481bef9008d
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-colorspace
  license: MIT
  license_family: MIT
  size: 244110
  timestamp: 1686763207612
- kind: conda
  name: r-nlme
  version: 3.1_164
  build: r43h61816a4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-nlme-3.1_164-r43h61816a4_0.conda
  sha256: e8c5c03c0c65ef668745dea377c006e34b4149aeb0ef90435bb726d7aa48a36e
  md5: 7f44f44332abf00df114c685b1d6054c
  depends:
  - libgcc-ng >=12
  - libgfortran-ng
  - libgfortran5 >=12.3.0
  - r-base >=4.3,<4.4.0a0
  - r-lattice
  license: GPL-2.0-or-later
  license_family: GPL3
  size: 2298001
  timestamp: 1701104851357
- kind: conda
  name: r-openssl
  version: 2.1.1
  build: r43hb353fa6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-openssl-2.1.1-r43hb353fa6_0.conda
  sha256: 5d3c25bd9b2d121f2dd1aebdfaf1485794665c1c725e7e4535af647b8c85b764
  md5: d034eaa40df595f8e77dfe6d716b36b4
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.3,<4.0a0
  - r-askpass
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 684729
  timestamp: 1695681778388
- kind: conda
  name: r-palmerpenguins
  version: 0.1.1
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-palmerpenguins-0.1.1-r43hc72bb7e_2.conda
  sha256: 0651fc22adc99e36f8f0e16c5a004adb656ffa4bed2531b55fcf2bce8183e144
  md5: c5190e2aaddd109c90e394c829bf2161
  depends:
  - r-base >=4.3,<4.4.0a0
  license: CC0-1.0
  license_family: CC
  size: 2979082
  timestamp: 1686811141185
- kind: conda
  name: r-pillar
  version: 1.9.0
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-pillar-1.9.0-r43hc72bb7e_1.conda
  sha256: ef5c27756bfb49dde7902711b762ae7409aee7321c7f58d37704c439536a7637
  md5: 7cff01456566a69381d3907d520c10b2
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-crayon >=1.3.4
  - r-ellipsis
  - r-fansi
  - r-lifecycle
  - r-rlang >=0.3.0
  - r-utf8 >=1.1.0
  - r-vctrs >=0.2.0
  license: GPL-3.0-only
  license_family: GPL3
  size: 617409
  timestamp: 1686696529220
- kind: conda
  name: r-pkgconfig
  version: 2.0.3
  build: r43hc72bb7e_3
  build_number: 3
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-pkgconfig-2.0.3-r43hc72bb7e_3.conda
  sha256: 1386c1a64451fd3220333f7cdad77c8baa797800cec3d9819e8c4b715559c979
  md5: 8978eef741fffaa48be070693a17f42f
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 26427
  timestamp: 1686665887924
- kind: conda
  name: r-prettyunits
  version: 1.2.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-prettyunits-1.2.0-r43hc72bb7e_0.conda
  sha256: 9c0bde508a001a583f93efc26e603f39238b3bac6c5e54be65e27ee83dc60fae
  md5: 7dc33db3703c9c4caa81cf1aea02b6bf
  depends:
  - r-assertthat
  - r-base >=4.3,<4.4.0a0
  - r-magrittr
  license: MIT
  license_family: MIT
  size: 162633
  timestamp: 1695597056560
- kind: conda
  name: r-processx
  version: 3.8.4
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-processx-3.8.4-r43h57805ef_0.conda
  sha256: 0ba201dfa292c308376a53b0241d4746908831793873421b3066d6dbdeab4b2d
  md5: 660d4e9ff8beef35dbda645484d17a25
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-ps >=1.2.0
  - r-r6
  license: MIT
  license_family: MIT
  size: 323338
  timestamp: 1710605292516
- kind: conda
  name: r-progress
  version: 1.2.3
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-progress-1.2.3-r43hc72bb7e_0.conda
  sha256: 749f9cf424c0c2617a33d438a124285d5ddb80859eb0bb399b4afc42ce5a418c
  md5: 49c52ccafe27439ac6b69a74c626b3a8
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-crayon
  - r-hms
  - r-prettyunits
  - r-r6
  license: MIT
  license_family: MIT
  size: 93780
  timestamp: 1701866827813
- kind: conda
  name: r-ps
  version: 1.7.6
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-ps-1.7.6-r43h57805ef_0.conda
  sha256: b50254dbdd626c88500b4f5deb7df13842a7d226daf84d62f9f51cb10e001944
  md5: 48f9e73215aa6de4545ec3ec36e5e534
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 312814
  timestamp: 1705580453386
- kind: conda
  name: r-purrr
  version: 1.0.2
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-purrr-1.0.2-r43h57805ef_0.conda
  sha256: cedb31d051295a0ea781d42b9e25a54dbd1175f40e96728277cd6557dbcd14d7
  md5: 713053b11419075641f09df086ef58d9
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.4
  - r-lifecycle >=1.0.3
  - r-magrittr >=1.5
  - r-rlang >=0.4.10
  - r-vctrs >=0.5
  license: MIT
  license_family: MIT
  size: 484162
  timestamp: 1691665029487
- kind: conda
  name: r-r6
  version: 2.5.1
  build: r43hc72bb7e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-r6-2.5.1-r43hc72bb7e_2.conda
  sha256: 79a88c5993a2697d572542649a876b23ac77a199ec5b54cfa7315ece183faf65
  md5: 79f167972554dc6b65aa099cd85c22a1
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 90427
  timestamp: 1686659044793
- kind: conda
  name: r-ragg
  version: 1.3.0
  build: r43h73ae6e3_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-ragg-1.3.0-r43h73ae6e3_0.conda
  sha256: 8de95a00e59ed801ee1e4a33b2e851f2df7cd2bb4f700b3b44a2e7fdc1475396
  md5: 60ca605f53eed2dce12787bf7c1d5d3b
  depends:
  - freetype >=2.12.1,<3.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libstdcxx-ng >=12
  - libtiff >=4.6.0,<4.7.0a0
  - r-base >=4.3,<4.4.0a0
  - r-systemfonts >=1.0.3
  - r-textshaping >=0.3.0
  license: MIT
  license_family: MIT
  size: 527314
  timestamp: 1710378668442
- kind: conda
  name: r-rappdirs
  version: 0.3.3
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-rappdirs-0.3.3-r43h57805ef_2.conda
  sha256: f0a4afd713406f9f61ce01282c928df6045ab5d408c2cff4746f72b8bc7d8b85
  md5: 788ac4f3dcfa79520bfc9eeadf50c2eb
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 52086
  timestamp: 1686753064601
- kind: conda
  name: r-rcolorbrewer
  version: 1.1_3
  build: r43h785f33e_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-rcolorbrewer-1.1_3-r43h785f33e_2.conda
  sha256: a6af4f8d47ac08ca13b85cb891797e93126df1f441a068704df6138966790bcd
  md5: d998f26d6a55b83dab1c25b583e1463a
  depends:
  - r-base >=4.3,<4.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 68277
  timestamp: 1686751835593
- kind: conda
  name: r-readr
  version: 2.1.5
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-readr-2.1.5-r43ha503ecb_0.conda
  sha256: 8fab2db4250b754778b6dd9759ed4410fce39c3e5ccb0a2745921e8d61d5acdc
  md5: 5cc6df81c86e75fbbcb66e51f26c76c6
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-clipr
  - r-cpp11
  - r-crayon
  - r-hms >=0.4.1
  - r-lifecycle >=0.2.0
  - r-r6
  - r-rlang
  - r-tibble
  - r-tzdb >=0.1.1
  - r-vroom >=1.5.4
  license: MIT
  license_family: MIT
  size: 788003
  timestamp: 1704938567062
- kind: conda
  name: r-readxl
  version: 1.4.3
  build: r43ha5c9fba_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-readxl-1.4.3-r43ha5c9fba_0.conda
  sha256: a1803a847a096113ba2683f4cb29f654214b206998050c5f699aa9fb706030fd
  md5: 3362d7b8ce966285343066437d42b1d8
  depends:
  - libgcc-ng >=12
  - libiconv >=1.17,<2.0a0
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cellranger
  - r-cpp11 >=0.4.0
  - r-progress
  - r-tibble >=2.0.1
  license: MIT
  license_family: MIT
  size: 758443
  timestamp: 1688690796747
- kind: conda
  name: r-rematch
  version: 2.0.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-rematch-2.0.0-r43hc72bb7e_0.conda
  sha256: cb0995b7ae6668c20089fb56d12439542fc137c3b5d6bf0205d8edb7218cb8d6
  md5: 40fc94fe6c0d3f991a5e9428e3123263
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 25051
  timestamp: 1693420148358
- kind: conda
  name: r-rematch2
  version: 2.1.2
  build: r43hc72bb7e_3
  build_number: 3
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-rematch2-2.1.2-r43hc72bb7e_3.conda
  sha256: c8ff369bf25b00a607312024f0544a369ec9f4d1c558ab47665e89c6c04747b7
  md5: d1f849bc6b24f93c392f4177f65578b4
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-tibble
  license: MIT
  license_family: MIT
  size: 54151
  timestamp: 1686720491134
- kind: conda
  name: r-reprex
  version: 2.1.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-reprex-2.1.0-r43hc72bb7e_0.conda
  sha256: 3c4a7086cc7d80f99529e73cc7a7213de92f72b48ea4c5b14495eeb715b24a64
  md5: 120a96e725aed940830d1a2d0b127f53
  depends:
  - pandoc >=2.0
  - r-base >=4.3,<4.4.0a0
  - r-callr >=3.6.0
  - r-cli >=3.2.0
  - r-clipr >=0.4.0
  - r-fs
  - r-glue
  - r-knitr >=1.23
  - r-lifecycle
  - r-rlang >=1.0.0
  - r-rmarkdown
  - r-rstudioapi
  - r-withr >=2.3.0
  license: MIT
  license_family: MIT
  size: 499495
  timestamp: 1704964959432
- kind: conda
  name: r-rlang
  version: 1.1.3
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-rlang-1.1.3-r43ha503ecb_0.conda
  sha256: c5d527e0172ffda7b0525915a8da3a9486e9e90849e453f344a5b320c2143fab
  md5: 0d71deb3b174a2876004ddcb6a4c7cfc
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: GPL-3.0-only
  license_family: GPL3
  size: 1522934
  timestamp: 1704897926139
- kind: conda
  name: r-rmarkdown
  version: '2.26'
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-rmarkdown-2.26-r43hc72bb7e_0.conda
  sha256: f9e4c70f6fb897383f6bba71debd3f2c56d17c91cefb3a993a8aaf56c8c196ee
  md5: 1548c0b6100c17b8f919106130be4619
  depends:
  - pandoc >=1.14
  - r-base >=4.3,<4.4.0a0
  - r-bslib >=0.2.5.1
  - r-evaluate >=0.13
  - r-fontawesome >=0.5.0
  - r-htmltools >=0.5.1
  - r-jquerylib
  - r-jsonlite
  - r-knitr >=1.22
  - r-stringr >=1.2.0
  - r-tinytex >=0.31
  - r-xfun >=0.36
  - r-yaml >=2.1.19
  license: GPL-3.0-only
  license_family: GPL3
  size: 2073162
  timestamp: 1709669054160
- kind: conda
  name: r-rstudioapi
  version: 0.15.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-rstudioapi-0.15.0-r43hc72bb7e_0.conda
  sha256: c683156fcb6929ebf095aca0fea8d6e0473550c345416353cb32cdd383cc86ce
  md5: 1bde3fa27383b477b899a0a2bc830cc5
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 295482
  timestamp: 1688762041989
- kind: conda
  name: r-rvest
  version: 1.0.4
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-rvest-1.0.4-r43hc72bb7e_0.conda
  sha256: 777f5b3fb851c6972be49af7acd529a73b6a933e11189c49c1014fbdecc5d141
  md5: 01ff5b7fd4bfafd75c0b91e6d3a1d443
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-glue
  - r-httr >=0.5
  - r-lifecycle >=1.0.0
  - r-magrittr
  - r-rlang >=1.0.0
  - r-selectr
  - r-tibble
  - r-withr
  - r-xml2 >=1.3
  license: MIT
  license_family: MIT
  size: 298246
  timestamp: 1707774631803
- kind: conda
  name: r-sass
  version: 0.4.9
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-sass-0.4.9-r43ha503ecb_0.conda
  sha256: 33c4053e0fcdb5153c473b9aa271e389f51373fa10926394948e59e3c3aac21b
  md5: 58e32ad6d979828aa0fc3da59e1b4d78
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-digest
  - r-fs
  - r-htmltools
  - r-r6
  - r-rappdirs
  - r-rlang
  license: MIT
  license_family: MIT
  size: 2283208
  timestamp: 1710592439276
- kind: conda
  name: r-scales
  version: 1.3.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-scales-1.3.0-r43hc72bb7e_0.conda
  sha256: 0921faa74d7864e3e33164e42d5591729141b4fe59e078cceaea33356d8a6684
  md5: 508360956e18c2b0cc18968cdb786c78
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-farver >=2.0.0
  - r-labeling
  - r-lifecycle
  - r-munsell >=0.5
  - r-r6
  - r-rcolorbrewer
  - r-viridislite
  license: MIT
  license_family: MIT
  size: 656613
  timestamp: 1701168188885
- kind: conda
  name: r-selectr
  version: 0.4_2
  build: r43hc72bb7e_3
  build_number: 3
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-selectr-0.4_2-r43hc72bb7e_3.conda
  sha256: 42a05db6957682319548e7273af01da4c6a729e8e6281581b87d5f083f184913
  md5: 735d0eb02604113a4ba423df2f14c574
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-r6
  - r-stringr
  license: BSD-3-Clause
  license_family: BSD
  size: 421086
  timestamp: 1686770073488
- kind: conda
  name: r-snakecase
  version: 0.11.1
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-snakecase-0.11.1-r43hc72bb7e_0.conda
  sha256: 61bfed1e2c3c068b9a3bd6c16ffd70801cc4416a05b8215e4f77a71d0039af8c
  md5: 554ddb1bc123413590b94dfd9a6ba223
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-stringi
  - r-stringr
  license: GPL-3.0-only
  license_family: GPL3
  size: 176612
  timestamp: 1693181609293
- kind: conda
  name: r-stringi
  version: 1.8.3
  build: r43h9facbd6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-stringi-1.8.3-r43h9facbd6_0.conda
  sha256: 644881609fee9d821b0e6493dff51e68504ba38e9648c1ff1bd5dfbb2099a2da
  md5: b39b29e195407633de68288a3c0ca54d
  depends:
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: FOSS
  license_family: OTHER
  size: 897913
  timestamp: 1702352983505
- kind: conda
  name: r-stringr
  version: 1.5.1
  build: r43h785f33e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-stringr-1.5.1-r43h785f33e_0.conda
  sha256: 4738dd2ae5bbd46b775b4ca02d3257835da37888fe1056985329204120e38332
  md5: 3451a0eb438bf0f5efb463b15f5e5ddf
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-glue >=1.6.1
  - r-lifecycle >=1.0.3
  - r-magrittr
  - r-rlang >=1.0.0
  - r-stringi >=1.5.3
  - r-vctrs
  license: MIT
  license_family: MIT
  size: 295981
  timestamp: 1700017145863
- kind: conda
  name: r-sys
  version: 3.4.2
  build: r43h57805ef_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-sys-3.4.2-r43h57805ef_1.conda
  sha256: 131dbd54bc2f75f2cb5ca604671ffb19b6eff128b7426216530258e2ad588325
  md5: ae6e4dd935d9c91064cbca07d2769d75
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 49040
  timestamp: 1686715861578
- kind: conda
  name: r-systemfonts
  version: 1.0.5
  build: r43haf97adc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-systemfonts-1.0.5-r43haf97adc_0.conda
  sha256: 46b3460f91f0c838f7fa1c5f4795ac117bee760d0a3529c288e75a39656b5377
  md5: dd9ca83d35c99cbc1b17076c50dc1730
  depends:
  - freetype >=2.12.1,<3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cpp11
  license: MIT
  license_family: MIT
  size: 238133
  timestamp: 1696878638320
- kind: conda
  name: r-textshaping
  version: 0.3.7
  build: r43hd87b9d6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-textshaping-0.3.7-r43hd87b9d6_0.conda
  sha256: 86b3798a86f77d60681caf7508dba04264380a98d483cf020e64ead91c843f1f
  md5: 9d61125e9753fd82f23462892446bb55
  depends:
  - freetype >=2.12.1,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=8.2.1,<9.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cpp11 >=0.2.1
  - r-systemfonts >=1.0.0
  license: MIT
  license_family: MIT
  size: 105091
  timestamp: 1696870142343
- kind: conda
  name: r-tibble
  version: 3.2.1
  build: r43h57805ef_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-tibble-3.2.1-r43h57805ef_2.conda
  sha256: ecb1c7a1fd41f5a8fc4d89bdaef49b986507dd2186f4c94c574ef192a9fdee17
  md5: afa7d3f21fbc5a2fbaa48cb9bacb7bce
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-fansi >=0.4.0
  - r-lifecycle >=1.0.0
  - r-magrittr
  - r-pillar >=1.8.1
  - r-pkgconfig
  - r-rlang >=1.0.2
  - r-vctrs >=0.4.2
  license: MIT
  license_family: MIT
  size: 612435
  timestamp: 1686709282135
- kind: conda
  name: r-tidyr
  version: 1.3.1
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-tidyr-1.3.1-r43ha503ecb_0.conda
  sha256: f6c61bfb64c666c70b353bb4fbad1c50604befdcd922176177d50d74273f1336
  md5: df5c985da1fa471f0efd7afe3d19d7fb
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.4.1
  - r-dplyr >=1.0.10
  - r-glue
  - r-lifecycle >=1.0.3
  - r-magrittr
  - r-purrr >=1.0.1
  - r-rlang >=1.0.4
  - r-stringr >=1.5.0
  - r-tibble >=2.1.1
  - r-tidyselect >=1.2.0
  - r-vctrs >=0.5.2
  license: MIT
  license_family: MIT
  size: 1127737
  timestamp: 1706115017786
- kind: conda
  name: r-tidyselect
  version: 1.2.0
  build: r43hc72bb7e_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-tidyselect-1.2.0-r43hc72bb7e_1.conda
  sha256: 1205ac608f6215f238e42b199a7258d6461cb0491affcbe463db39091af88160
  md5: a77f9f1f0fc62348d3cb8b97c8bca7c0
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.3.0
  - r-glue >=1.3.0
  - r-lifecycle >=1.0.3
  - r-rlang >=1.0.4
  - r-vctrs >=0.4.1
  - r-withr
  license: MIT
  license_family: MIT
  size: 215351
  timestamp: 1686753209284
- kind: conda
  name: r-tidyverse
  version: 2.0.0
  build: r43h785f33e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-tidyverse-2.0.0-r43h785f33e_1.conda
  sha256: b2173d75f88792adcec5dae295e1a2339020d74824a811b0952f693c855858ed
  md5: 390310ae8a44cbd35c8d27f5efa362cb
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-broom >=1.0.3
  - r-cli >=3.6.0
  - r-conflicted >=1.2.0
  - r-dbplyr >=2.3.0
  - r-dplyr >=1.1.0
  - r-dtplyr >=1.2.2
  - r-forcats >=1.0.0
  - r-ggplot2 >=3.4.1
  - r-googledrive >=2.0.0
  - r-googlesheets4 >=1.0.1
  - r-haven >=2.5.1
  - r-hms >=1.1.2
  - r-httr >=1.4.4
  - r-jsonlite >=1.8.4
  - r-lubridate >=1.9.2
  - r-magrittr >=2.0.3
  - r-modelr >=0.1.10
  - r-pillar >=1.8.1
  - r-purrr >=1.0.1
  - r-ragg >=1.2.5
  - r-readr >=2.1.4
  - r-readxl >=1.4.2
  - r-reprex >=2.0.2
  - r-rlang >=1.0.6
  - r-rstudioapi >=0.14
  - r-rvest >=1.0.3
  - r-stringr >=1.5.0
  - r-tibble >=3.1.8
  - r-tidyr >=1.3.0
  - r-xml2 >=1.3.3
  license: MIT
  license_family: MIT
  size: 425211
  timestamp: 1686929953187
- kind: conda
  name: r-timechange
  version: 0.3.0
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-timechange-0.3.0-r43ha503ecb_0.conda
  sha256: ef598979db9abf7163bd68c1ba8ab60430a57655cb0ff7f88dd94a95f4301829
  md5: 9502bf10997943c1f3d6abeced14b792
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cpp11 >=0.2.7
  license: GPL-3.0-only AND Apache-2.0
  license_family: GPL3
  size: 191179
  timestamp: 1705580349419
- kind: conda
  name: r-tinytex
  version: '0.50'
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-tinytex-0.50-r43hc72bb7e_0.conda
  sha256: 827b8e9112d280a88d2389a0e8efe240488f358bda7ec19b919893160f1fe631
  md5: 13a98fe1b23b211ced808d4ea8ed4eb0
  depends:
  - r-base >=4.3,<4.4.0a0
  - r-xfun >=0.5
  license: MIT
  license_family: MIT
  size: 147579
  timestamp: 1710599981556
- kind: conda
  name: r-tzdb
  version: 0.4.0
  build: r43ha503ecb_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-tzdb-0.4.0-r43ha503ecb_1.conda
  sha256: 64718ef62082b95117baa7d0e93f8f77d58373c4d178c9c0e5bd27575234610b
  md5: 8005d3bb90f7ee5070c59ce0c3e85757
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cpp11 >=0.4.2
  license: MIT
  license_family: MIT
  size: 537122
  timestamp: 1686771361669
- kind: conda
  name: r-utf8
  version: 1.2.4
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-utf8-1.2.4-r43h57805ef_0.conda
  sha256: 36b048d470b988a06d1ba73203ce5f8811f6d29720b5422a4f9de24c9f7e4a69
  md5: d228498392dce22d3fe6266554498493
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 142541
  timestamp: 1698018550446
- kind: conda
  name: r-uuid
  version: 1.2_0
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-uuid-1.2_0-r43h57805ef_0.conda
  sha256: cd6d3d925ef1a5b7718326b5d5c8ac31caf950dddb4d272c4c361a9ef93f6017
  md5: 88a02431c8ab8be38eed2b01f9d44ab4
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 55217
  timestamp: 1705256272072
- kind: conda
  name: r-vctrs
  version: 0.6.5
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-vctrs-0.6.5-r43ha503ecb_0.conda
  sha256: 30f4d5cb37eb153795d998376e245354a3baab94bb47b6cf175eb3570978551a
  md5: e398bd0451e6350a876a8561f8e90682
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-cli >=3.4.0
  - r-glue
  - r-lifecycle >=1.0.3
  - r-rlang >=1.0.6
  license: MIT
  license_family: MIT
  size: 1236265
  timestamp: 1701482569240
- kind: conda
  name: r-viridislite
  version: 0.4.2
  build: r43hc72bb7e_1
  build_number: 1
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-viridislite-0.4.2-r43hc72bb7e_1.conda
  sha256: 29687450dd51361c7255b0ac1006290845a343598412f494cffde2166e1edfd0
  md5: 307f6c8262afed91d2999568e2268ed6
  depends:
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 1303426
  timestamp: 1686752039531
- kind: conda
  name: r-vroom
  version: 1.6.5
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-vroom-1.6.5-r43ha503ecb_0.conda
  sha256: dd849b688839f72ff8579c089c876bed192b18cd7bce2cc3a5f0f9fcc0825519
  md5: 8d0ccab02e71131d508d3abe6a99bbc7
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  - r-bit64
  - r-cli
  - r-cpp11 >=0.2.0
  - r-crayon
  - r-glue
  - r-hms
  - r-lifecycle
  - r-progress >=1.2.1
  - r-rlang >=0.4.2
  - r-tibble >=2.0.0
  - r-tidyselect
  - r-tzdb >=0.1.1
  - r-vctrs >=0.2.0
  - r-withr
  license: MIT
  license_family: MIT
  size: 865440
  timestamp: 1701824886956
- kind: conda
  name: r-withr
  version: 3.0.0
  build: r43hc72bb7e_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/r-withr-3.0.0-r43hc72bb7e_0.conda
  sha256: f4f2c4107738882305e3a6776fc15ecf3dc8a6ae9b87479ac9dda337edb238b2
  md5: 7380a50d9daae38b40777f7630b8c3ba
  depends:
  - r-base >=4.3,<4.4.0a0
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 249182
  timestamp: 1705431516517
- kind: conda
  name: r-xfun
  version: '0.42'
  build: r43ha503ecb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-xfun-0.42-r43ha503ecb_0.conda
  sha256: c5778bf6229a6feae0c8c092260b30b8f435829ac9a816ebce50d7d1a36f4000
  md5: d7a7f9bd1552d11d284fd150b3a3a2e9
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: MIT
  license_family: MIT
  size: 466774
  timestamp: 1707425738165
- kind: conda
  name: r-xml2
  version: 1.3.6
  build: r43hbfba7a4_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-xml2-1.3.6-r43hbfba7a4_1.conda
  sha256: 97ae036d21c306865aa07177791c26d6b5a0c76a704d34e3cd89ed77a651ec01
  md5: 545f26a602ff99102e7fd70f425ead32
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxml2 >=2.12.3,<3.0.0a0
  - r-base >=4.3,<4.4.0a0
  - r-cli
  - r-rlang >=1.1.0
  license: GPL-2.0-or-later
  license_family: GPL2
  size: 346208
  timestamp: 1703433078733
- kind: conda
  name: r-yaml
  version: 2.3.8
  build: r43h57805ef_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/r-yaml-2.3.8-r43h57805ef_0.conda
  sha256: 0a71cad7158d5785e0e3dff6cbb726dc5391c1d232c68cf3a590202af32b3330
  md5: 41db1ee9d0073339e681379b92e7dfb6
  depends:
  - libgcc-ng >=12
  - r-base >=4.3,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 118280
  timestamp: 1702332127010
- kind: conda
  name: rdma-core
  version: '50.0'
  build: hd3aeb46_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-50.0-hd3aeb46_1.conda
  sha256: 85e38508eb4921e53cf1cb97435f9c9408ea2ddc582c6588ec50f3f3ec3abdc0
  md5: f462219598fcf46c0cdfb985c3482b4f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libnl >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  license: Linux-OpenIB
  license_family: BSD
  size: 4713842
  timestamp: 1710157799992
- kind: conda
  name: re2
  version: 2023.09.01
  build: h7f4b329_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/re2-2023.09.01-h7f4b329_2.conda
  sha256: f0f520f57e6b58313e8c41abc7dfa48742a05f1681f05654558127b667c769a8
  md5: 8f70e36268dea8eb666ef14c29bd3cda
  depends:
  - libre2-11 2023.09.01 h5a48ba9_2
  license: BSD-3-Clause
  license_family: BSD
  size: 26617
  timestamp: 1708946796423
- kind: conda
  name: readline
  version: '8.2'
  build: h8228510_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
  sha256: 5435cf39d039387fbdc977b0a762357ea909a7694d9528ab40f005e9208744d7
  md5: 47d31b792659ce70f470b5c82fdfb7a4
  depends:
  - libgcc-ng >=12
  - ncurses >=6.3,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 281456
  timestamp: 1679532220005
- kind: conda
  name: regex
  version: 2023.12.25
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/regex-2023.12.25-py311h459d7ec_0.conda
  sha256: 30791fca4461b858bbf4058eba60cc151c38fbd4f883dea72d1d1c5ee4c1ef0a
  md5: 90c12714214e3028d32a19d31af30744
  depends:
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  license_family: PSF
  size: 404531
  timestamp: 1703393675553
- kind: conda
  name: requests
  version: 2.31.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/requests-2.31.0-pyhd8ed1ab_0.conda
  sha256: 9f629d6fd3c8ac5f2a198639fe7af87c4db2ac9235279164bfe0fcb49d8c4bad
  md5: a30144e4156cdbb236f99ebb49828f8b
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.7
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  size: 56690
  timestamp: 1684774408600
- kind: conda
  name: requests-oauthlib
  version: 1.4.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/requests-oauthlib-1.4.0-pyhd8ed1ab_0.conda
  sha256: 909ec1510bbb6fad9276534352025f428050a4deeea86e68d61c8c580938ac82
  md5: a55b220de8970208f583e38639cfbecc
  depends:
  - oauthlib >=3.0.0
  - python >=3.4
  - requests >=2.0.0
  license: ISC
  size: 25757
  timestamp: 1710149693493
- kind: conda
  name: rich
  version: 13.7.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/rich-13.7.1-pyhd8ed1ab_0.conda
  sha256: 2b26d58aa59e46f933c3126367348651b0dab6e0bf88014e857415bb184a4667
  md5: ba445bf767ae6f0d959ff2b40c20912b
  depends:
  - markdown-it-py >=2.2.0
  - pygments >=2.13.0,<3.0.0
  - python >=3.7.0
  - typing_extensions >=4.0.0,<5.0.0
  license: MIT
  license_family: MIT
  size: 184347
  timestamp: 1709150578093
- kind: conda
  name: rsa
  version: '4.9'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/rsa-4.9-pyhd8ed1ab_0.tar.bz2
  sha256: 23214cdc15a41d14136754857fd9cd46ca3c55a7e751da3b3a48c673f0ee2a57
  md5: 03bf410858b2cefc267316408a77c436
  depends:
  - pyasn1 >=0.1.3
  - python >=3.6
  license: Apache-2.0
  license_family: APACHE
  size: 29863
  timestamp: 1658329024970
- kind: conda
  name: s2n
  version: 1.4.7
  build: h06160fa_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.4.7-h06160fa_0.conda
  sha256: c46f77d6280f2f22e7c63b160c9177627278056742370f8aec7aeb1b3c5393a9
  md5: bd39dff72c2daebd9b38c5a27b3ad207
  depends:
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 339411
  timestamp: 1710451148936
- kind: conda
  name: sed
  version: '4.8'
  build: he412f7d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/sed-4.8-he412f7d_0.tar.bz2
  sha256: 7c1f391789f3928ef688a348be998e31b8aa3cfb58a1854733c2552ef5c5a2fd
  md5: 7362f0042e95681f5d371c46c83ebd08
  depends:
  - libgcc-ng >=7.5.0
  license: GPL-3
  size: 270762
  timestamp: 1605307395873
- kind: conda
  name: setuptools
  version: 69.2.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.2.0-pyhd8ed1ab_0.conda
  sha256: 78a75c75a5dacda6de5f4056c9c990141bdaf4f64245673a590594d00bc63713
  md5: da214ecd521a720a9d521c68047682dc
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 471183
  timestamp: 1710344615844
- kind: conda
  name: six
  version: 1.16.0
  build: pyh6c4a22f_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/six-1.16.0-pyh6c4a22f_0.tar.bz2
  sha256: a85c38227b446f42c5b90d9b642f2c0567880c15d72492d8da074a59c8f91dd6
  md5: e5f25f8dbc060e9a8d912e432202afc2
  depends:
  - python
  license: MIT
  license_family: MIT
  size: 14259
  timestamp: 1620240338595
- kind: conda
  name: snappy
  version: 1.1.10
  build: h9fff704_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.1.10-h9fff704_0.conda
  sha256: 02219f2382b4fe39250627dade087a4412d811936a5a445636b7260477164eac
  md5: e6d228cd0bb74a51dd18f5bfce0b4115
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 38865
  timestamp: 1678534590321
- kind: conda
  name: sqlalchemy
  version: 2.0.28
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/sqlalchemy-2.0.28-py311h459d7ec_0.conda
  sha256: 90c311acf00da24689d633819f3a11fd5b26938176e30cf41fd544c519a81647
  md5: a333a705ca2bacd3a215fbddbfb15d4e
  depends:
  - greenlet !=0.4.17
  - libgcc-ng >=12
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - typing-extensions >=4.6.0
  license: MIT
  license_family: MIT
  size: 3491760
  timestamp: 1709646451561
- kind: conda
  name: sqlalchemy-views
  version: 0.3.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/sqlalchemy-views-0.3.2-pyhd8ed1ab_0.conda
  sha256: 075c11d3a75e3c082f21de01d5cc1d92aae5db19b343addb38f158e46cbe42e9
  md5: 8a0884a535e9ef935bbd977e575e8115
  depends:
  - python >=3.7
  - sqlalchemy >=1.0.0
  license: MIT
  license_family: MIT
  size: 11084
  timestamp: 1679473171457
- kind: conda
  name: sqlglot
  version: 20.11.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/sqlglot-20.11.0-pyhd8ed1ab_0.conda
  sha256: 3b2412ae1aa56bdfb6d6439c3870b13bb21a272f9babcc3cce725724107c098d
  md5: 1e784de34c2c9dda899ec170e7afc7be
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 1840964
  timestamp: 1707318538976
- kind: conda
  name: sysroot_linux-64
  version: '2.12'
  build: he073ed8_17
  build_number: 17
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.12-he073ed8_17.conda
  sha256: b4e4d685e41cb36cfb16f0cb15d2c61f8f94f56fab38987a44eff95d8a673fb5
  md5: 595db67e32b276298ff3d94d07d47fbf
  depends:
  - kernel-headers_linux-64 2.6.32 he073ed8_17
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later AND MPL-2.0
  license_family: GPL
  size: 15127123
  timestamp: 1708000843849
- kind: conda
  name: tk
  version: 8.6.13
  build: noxft_h4845f30_101
  build_number: 101
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: TCL
  license_family: BSD
  size: 3318875
  timestamp: 1699202167581
- kind: conda
  name: tktable
  version: '2.10'
  build: h0c5db8f_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/tktable-2.10-h0c5db8f_5.conda
  sha256: 5d9285ea9b2d20b9ade8adaa823774431799afa20d4b7135cf2ce1a8b99da2e6
  md5: 9464044754ea25557a9c93f0327d90a6
  depends:
  - libgcc-ng >=12
  - tk >=8.6.13,<8.7.0a0
  license: TCL
  size: 89770
  timestamp: 1695716117065
- kind: conda
  name: toolz
  version: 0.12.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/toolz-0.12.1-pyhd8ed1ab_0.conda
  sha256: 22b0a9790317526e08609d5dfdd828210ae89e6d444a9e954855fc29012e90c6
  md5: 2fcb582444635e2c402e8569bb94e039
  depends:
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  size: 52358
  timestamp: 1706112720607
- kind: conda
  name: tqdm
  version: 4.66.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.66.2-pyhd8ed1ab_0.conda
  sha256: 416d1d9318f3267325ad7e2b8a575df20ff9031197b30c0222c3d3b023877260
  md5: 2b8dfb969f984497f3f98409a9545776
  depends:
  - colorama
  - python >=3.7
  license: MPL-2.0 or MIT
  size: 89567
  timestamp: 1707598746354
- kind: conda
  name: typing-extensions
  version: 4.10.0
  build: hd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.10.0-hd8ed1ab_0.conda
  sha256: 0698fe2c4e555fb44c27c60f7a21fa0eea7f5bf8186ad109543c5b056e27f96a
  md5: 091683b9150d2ebaa62fd7e2c86433da
  depends:
  - typing_extensions 4.10.0 pyha770c72_0
  license: PSF-2.0
  license_family: PSF
  size: 10181
  timestamp: 1708904805365
- kind: conda
  name: typing_extensions
  version: 4.10.0
  build: pyha770c72_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.10.0-pyha770c72_0.conda
  sha256: 4be24d557897b2f6609f5d5f7c437833c62f4d4a96581e39530067e96a2d0451
  md5: 16ae769069b380646c47142d719ef466
  depends:
  - python >=3.8
  license: PSF-2.0
  license_family: PSF
  size: 37018
  timestamp: 1708904796013
- kind: conda
  name: tzdata
  version: 2024a
  build: h0c530f3_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
  sha256: 7b2b69c54ec62a243eb6fba2391b5e443421608c3ae5dbff938ad33ca8db5122
  md5: 161081fc7cec0bfda0d86d7cb595f8d8
  license: LicenseRef-Public-Domain
  size: 119815
  timestamp: 1706886945727
- kind: conda
  name: ucx
  version: 1.15.0
  build: h11edf95_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ucx-1.15.0-h11edf95_7.conda
  sha256: 3e381ec5918045a43e0f349214a4d38e53990897ba07a6abf025f9e0156acaf2
  md5: 20a94f617ad76922f8737ad1fe317f4d
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - rdma-core >=50.0
  constrains:
  - cuda-version >=11.2,<12
  license: BSD-3-Clause
  license_family: BSD
  size: 6847943
  timestamp: 1710357262334
- kind: conda
  name: urllib3
  version: 2.2.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.2.1-pyhd8ed1ab_0.conda
  sha256: d4009dcc9327684d6409706ce17656afbeae690d8522d3c9bc4df57649a352cd
  md5: 08807a87fa7af10754d46f63b368e016
  depends:
  - brotli-python >=1.0.9
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.7
  license: MIT
  license_family: MIT
  size: 94669
  timestamp: 1708239595549
- kind: conda
  name: xorg-kbproto
  version: 1.0.7
  build: h7f98852_1002
  build_number: 1002
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-kbproto-1.0.7-h7f98852_1002.tar.bz2
  sha256: e90b0a6a5d41776f11add74aa030f789faf4efd3875c31964d6f9cfa63a10dd1
  md5: 4b230e8381279d76131116660f5a241a
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 27338
  timestamp: 1610027759842
- kind: conda
  name: xorg-libice
  version: 1.1.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.1-hd590300_0.conda
  sha256: 5aa9b3682285bb2bf1a8adc064cb63aff76ef9178769740d855abb42b0d24236
  md5: b462a33c0be1421532f28bfe8f4a7514
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 58469
  timestamp: 1685307573114
- kind: conda
  name: xorg-libsm
  version: 1.2.4
  build: h7391055_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.4-h7391055_0.conda
  sha256: 089ad5f0453c604e18985480218a84b27009e9e6de9a0fa5f4a20b8778ede1f1
  md5: 93ee23f12bc2e684548181256edd2cf6
  depends:
  - libgcc-ng >=12
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 27433
  timestamp: 1685453649160
- kind: conda
  name: xorg-libx11
  version: 1.8.7
  build: h8ee46fc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.7-h8ee46fc_0.conda
  sha256: 7a02a7beac472ae2759498550b5fc5261bf5be7a9a2b4648a3f67818a7bfefcf
  md5: 49e482d882669206653b095f5206c05b
  depends:
  - libgcc-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - xorg-kbproto
  - xorg-xextproto >=7.3.0,<8.0a0
  - xorg-xproto
  license: MIT
  license_family: MIT
  size: 828692
  timestamp: 1697056910935
- kind: conda
  name: xorg-libxau
  version: 1.0.11
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.11-hd590300_0.conda
  sha256: 309751371d525ce50af7c87811b435c176915239fc9e132b99a25d5e1703f2d4
  md5: 2c80dc38fface310c9bd81b17037fee5
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 14468
  timestamp: 1684637984591
- kind: conda
  name: xorg-libxdmcp
  version: 1.1.3
  build: h7f98852_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.3-h7f98852_0.tar.bz2
  sha256: 4df7c5ee11b8686d3453e7f3f4aa20ceef441262b49860733066c52cfd0e4a77
  md5: be93aabceefa2fac576e971aef407908
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 19126
  timestamp: 1610071769228
- kind: conda
  name: xorg-libxext
  version: 1.3.4
  build: h0b41bf4_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.4-h0b41bf4_2.conda
  sha256: 73e5cfbdff41ef8a844441f884412aa5a585a0f0632ec901da035a03e1fe1249
  md5: 82b6df12252e6f32402b96dacc656fec
  depends:
  - libgcc-ng >=12
  - xorg-libx11 >=1.7.2,<2.0a0
  - xorg-xextproto
  license: MIT
  license_family: MIT
  size: 50143
  timestamp: 1677036907815
- kind: conda
  name: xorg-libxrender
  version: 0.9.11
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.11-hd590300_0.conda
  sha256: 26da4d1911473c965c32ce2b4ff7572349719eaacb88a066db8d968a4132c3f7
  md5: ed67c36f215b310412b2af935bf3e530
  depends:
  - libgcc-ng >=12
  - xorg-libx11 >=1.8.6,<2.0a0
  - xorg-renderproto
  license: MIT
  license_family: MIT
  size: 37770
  timestamp: 1688300707994
- kind: conda
  name: xorg-libxt
  version: 1.3.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxt-1.3.0-hd590300_1.conda
  sha256: e7648d1efe2e858c4bc63ccf4a637c841dc971b37ded85a01be97a5e240fecfa
  md5: ae92aab42726eb29d16488924f7312cb
  depends:
  - libgcc-ng >=12
  - xorg-kbproto
  - xorg-libice >=1.1.1,<2.0a0
  - xorg-libsm >=1.2.4,<2.0a0
  - xorg-libx11 >=1.8.6,<2.0a0
  - xorg-xproto
  license: MIT
  license_family: MIT
  size: 379256
  timestamp: 1690288540492
- kind: conda
  name: xorg-renderproto
  version: 0.11.1
  build: h7f98852_1002
  build_number: 1002
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-renderproto-0.11.1-h7f98852_1002.tar.bz2
  sha256: 38942930f233d1898594dd9edf4b0c0786f3dbc12065a0c308634c37fd936034
  md5: 06feff3d2634e3097ce2fe681474b534
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 9621
  timestamp: 1614866326326
- kind: conda
  name: xorg-xextproto
  version: 7.3.0
  build: h0b41bf4_1003
  build_number: 1003
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-xextproto-7.3.0-h0b41bf4_1003.conda
  sha256: b8dda3b560e8a7830fe23be1c58cc41f407b2e20ae2f3b6901eb5842ba62b743
  md5: bce9f945da8ad2ae9b1d7165a64d0f87
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 30270
  timestamp: 1677036833037
- kind: conda
  name: xorg-xproto
  version: 7.0.31
  build: h7f98852_1007
  build_number: 1007
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-xproto-7.0.31-h7f98852_1007.tar.bz2
  sha256: f197bb742a17c78234c24605ad1fe2d88b1d25f332b75d73e5ba8cf8fbc2a10d
  md5: b4a4381d54784606820704f7b5f05a15
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 74922
  timestamp: 1607291557628
- kind: conda
  name: xxhash
  version: 0.8.2
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.2-hd590300_0.conda
  sha256: 6fe74a8fd84ab0dc25e4dc3e0c22388dd8accb212897a208b14fe5d4fbb8fc2f
  md5: f08fb5c89edfc4aadee1c81d4cfb1fa1
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 97691
  timestamp: 1689951608120
- kind: conda
  name: xz
  version: 5.2.6
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xz-5.2.6-h166bdaf_0.tar.bz2
  sha256: 03a6d28ded42af8a347345f82f3eebdd6807a08526d47899a42d62d319609162
  md5: 2161070d867d1b1204ea749c8eec4ef0
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1 and GPL-2.0
  size: 418368
  timestamp: 1660346797927
- kind: conda
  name: yaml
  version: 0.2.5
  build: h7f98852_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 89141
  timestamp: 1641346969816
- kind: conda
  name: yarl
  version: 1.9.4
  build: py311h459d7ec_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.9.4-py311h459d7ec_0.conda
  sha256: 673e4a626e9e7d661154e5609f696c0c8a9247087f5c8b7744cfbb4fe0872713
  md5: fff0f2058e9d86c8bf5848ee93917a8d
  depends:
  - idna >=2.0
  - libgcc-ng >=12
  - multidict >=4.0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  size: 122372
  timestamp: 1705508480013
- kind: conda
  name: zipp
  version: 3.17.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/zipp-3.17.0-pyhd8ed1ab_0.conda
  sha256: bced1423fdbf77bca0a735187d05d9b9812d2163f60ab426fc10f11f92ecbe26
  md5: 2e4d6bc0b14e10f895fc6791a7d9b26a
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  size: 18954
  timestamp: 1695255262261
- kind: conda
  name: zlib
  version: 1.2.13
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.2.13-hd590300_5.conda
  sha256: 9887a04d7e7cb14bd2b52fa01858f05a6d7f002c890f618d9fcd864adbfecb1b
  md5: 68c34ec6149623be41a1933ab996a209
  depends:
  - libgcc-ng >=12
  - libzlib 1.2.13 hd590300_5
  license: Zlib
  license_family: Other
  size: 92825
  timestamp: 1686575231103
- kind: conda
  name: zstd
  version: 1.5.5
  build: hfc55251_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.5-hfc55251_0.conda
  sha256: 607cbeb1a533be98ba96cf5cdf0ddbb101c78019f1fda063261871dad6248609
  md5: 04b88013080254850d6c01ed54810589
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 545199
  timestamp: 1693151163452
