r0 := UnboundTable: alltypes
  a int8
  b int16
  c int32
  d int64
  e float32
  f float64
  g string
  h boolean
  i timestamp
  j date
  k time

r1 := MyRelation[r0]
  kind:
    foo
  schema:
    a int8
    b int16
    c int32
    d int64
    e float32
    f float64
    g string
    h boolean
    i timestamp
    j date
    k time

Project[r1]
  a:  r1.a
  b:  r1.b
  c:  r1.c
  d:  r1.d
  e:  r1.e
  f:  r1.f
  g:  r1.g
  h:  r1.h
  i:  r1.i
  j:  r1.j
  k:  r1.k
  a2: r1.a