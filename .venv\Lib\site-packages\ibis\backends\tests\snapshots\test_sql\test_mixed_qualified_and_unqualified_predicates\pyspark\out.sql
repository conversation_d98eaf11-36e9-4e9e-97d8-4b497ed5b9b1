SELECT
  `x`,
  `y`
FROM (
  SELECT
    `t1`.`x`,
    `t1`.`y`,
    AV<PERSON>(`t1`.`x`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND <PERSON>BOUNDED FOLLOWING) AS _w,
    AVG(`t1`.`x`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS _w_2
  FROM (
    SELECT
      `t0`.`x`,
      SUM(`t0`.`x`) OVER (ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS `y`
    FROM `t` AS `t0`
  ) AS `t1`
  WHERE
    `t1`.`y` <= 37
) AS _t
WHERE
  _w IS NOT NULL AND NOT ISNAN(_w_2)