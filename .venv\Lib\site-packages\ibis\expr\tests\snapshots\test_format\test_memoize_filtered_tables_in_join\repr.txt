r0 := UnboundTable: purchases
  region string
  kind   string
  user   int64
  amount float64

r1 := Aggregate[r0]
  groups:
    region: r0.region
    kind:   r0.kind
  metrics:
    total: Sum(r0.amount)

r2 := Filter[r1]
  r1.kind == 'foo'

r3 := Filter[r1]
  r1.kind == 'bar'

<PERSON><PERSON><PERSON><PERSON><PERSON>[r2]
  JoinLink[inner, r3]
    r2.region == r3.region
  values:
    region:      r2.region
    kind:        r2.kind
    total:       r2.total
    right_total: r3.total