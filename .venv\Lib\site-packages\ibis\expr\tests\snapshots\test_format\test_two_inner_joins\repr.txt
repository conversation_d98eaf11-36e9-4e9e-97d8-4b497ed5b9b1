r0 := UnboundTable: left
  time1 int32
  value float64
  a     string

r1 := UnboundTable: right
  time2  int32
  value2 float64
  b      string

r2 := SelfReference[r1]

<PERSON><PERSON><PERSON><PERSON><PERSON>[r0]
  JoinLink[inner, r1]
    r0.a == r1.b
  JoinLink[inner, r2]
    r0.value == r2.value2
  values:
    time1:        r0.time1
    value:        r0.value
    a:            r0.a
    time2:        r1.time2
    value2:       r1.value2
    b:            r1.b
    time2_right:  r2.time2
    value2_right: r2.value2
    b_right:      r2.b