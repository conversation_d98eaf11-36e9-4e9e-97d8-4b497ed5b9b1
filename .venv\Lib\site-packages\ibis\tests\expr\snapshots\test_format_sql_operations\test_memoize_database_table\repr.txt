r0 := DatabaseTable: test2
  key   string
  value float64

r1 := DatabaseTable: test1
  c int32
  f float64
  g string

r2 := Filter[r1]
  r1.f > 0

r3 := <PERSON><PERSON><PERSON><PERSON><PERSON>[r0]
  JoinLink[inner, r2]
    r2.g == r0.key
  values:
    key:   r0.key
    value: r0.value
    c:     r2.c
    f:     r2.f
    g:     r2.g

Aggregate[r3]
  groups:
    g:   r3.g
    key: r3.key
  metrics:
    foo: Mean(r3.f - r3.value)
    bar: Sum(r3.f)