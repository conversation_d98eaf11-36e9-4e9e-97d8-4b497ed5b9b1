ibis/__init__.py,sha256=OeITK28U3At8mtf9jgo6YzKcYqEiWPXXelIhP-iM4pY,4465
ibis/__pycache__/__init__.cpython-312.pyc,,
ibis/__pycache__/config.cpython-312.pyc,,
ibis/__pycache__/conftest.cpython-312.pyc,,
ibis/__pycache__/interactive.cpython-312.pyc,,
ibis/__pycache__/selectors.cpython-312.pyc,,
ibis/__pycache__/util.cpython-312.pyc,,
ibis/backends/__init__.py,sha256=opP0ljHL9fItXx4VaoHzTNDlnwkI2ddvcJDeuJ5ZnNM,57685
ibis/backends/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/athena/__init__.py,sha256=nJ5po4FaGRX6yXyW6TSlO-Au-zr4CPZrjWDACt8vSxY,19970
ibis/backends/athena/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/athena/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/athena/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/athena/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/athena/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/athena/tests/conftest.py,sha256=Fv4E1xK168LEALtUXmxp6tIp3gSLtMBTHmrdeXELt30,3583
ibis/backends/athena/tests/test_client.py,sha256=6ArXhdllzNeMZCrvwPpg9m9iZgkIRo-HLURcZ7WX8_I,1673
ibis/backends/bigquery/__init__.py,sha256=mglyV4UBXHFTzdL2TbLLSiVmpcv0YImvjo4F1oO4DCE,44495
ibis/backends/bigquery/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/bigquery/__pycache__/client.cpython-312.pyc,,
ibis/backends/bigquery/__pycache__/converter.cpython-312.pyc,,
ibis/backends/bigquery/__pycache__/datatypes.cpython-312.pyc,,
ibis/backends/bigquery/client.py,sha256=PTitQcXBVFbvulhYiJQa-JE-6yWVRoSf2eXxtuAIz-U,5587
ibis/backends/bigquery/converter.py,sha256=LbWoQY5e0U-q5CQqLOv2XjCYkdOZBiOuyhNL2slyD7w,397
ibis/backends/bigquery/datatypes.py,sha256=mCD5OBwtt7FSkG4XZoBFUZZF3YpOVBSjvVKeOWyPYBI,2473
ibis/backends/bigquery/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/bigquery/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/bigquery/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/bigquery/tests/conftest.py,sha256=rApocuntkVdWEACX0bGNLqJeZ_wpg3dfQpD0WmZfB9Y,2943
ibis/backends/bigquery/tests/system/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/bigquery/tests/system/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/bigquery/tests/system/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/bigquery/tests/system/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/bigquery/tests/system/__pycache__/test_connect.cpython-312.pyc,,
ibis/backends/bigquery/tests/system/conftest.py,sha256=Sak1fit-XJ7P6GaW2ItPaILuCRt6udU8cv3-bHQk1Ao,3538
ibis/backends/bigquery/tests/system/test_client.py,sha256=ubaAPZok9b-5XGAYesiE6aGv0TxV1fyD28LodC7zIcU,20356
ibis/backends/bigquery/tests/system/test_connect.py,sha256=o1xVldjXMxatAsqccsdG-FvI7obdI2jeNfg8YZOS3FI,8644
ibis/backends/bigquery/tests/system/udf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/bigquery/tests/system/udf/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/bigquery/tests/system/udf/__pycache__/test_udf_execute.cpython-312.pyc,,
ibis/backends/bigquery/tests/system/udf/test_udf_execute.py,sha256=Np2cm3f7jJjgT54K4uG3WVVNigVrVDolV_MonJxVFrM,4976
ibis/backends/bigquery/tests/unit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/bigquery/tests/unit/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/__pycache__/test___init__.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/__pycache__/test_compiler.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx/filter-approx_median/out.sql,sha256=y5ZtqQzKgysauLq4kLHtVzjK6EYa4K945aaQl7stqK4,194
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx/filter-approx_nunique/out.sql,sha256=pzTat43-RoNjE2nRLQDrUR15LnXqfE-Gj1LzyLJy-X4,164
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx/no_filter-approx_median/out.sql,sha256=26A-VaNTeORMt2TCEEtZ3T2sOMDiFr-6gx21On0oXVk,122
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx/no_filter-approx_nunique/out.sql,sha256=68Sut4a2feOvg_NhP0ES8WtDwdxyvlK8mJfufm69ra8,120
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx_quantiles/array/out.sql,sha256=t9lXOst5l46AWwIpU19_lTGiYYZjIIIhm7NEZFw6gbE,236
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx_quantiles/complete-array/out.sql,sha256=CGn8Q0_z9t3mCVnayQx5kVgd1JdOvrkwAsJgnbEosiE,103
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx_quantiles/scalar/out.sql,sha256=3uhhuP41k5exNU5wDywUGGwofZdEf3WoqGjbKfXTEwQ,106
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx_quantiles/shuffled-array/out.sql,sha256=cWQ9Uh1rT5nRN7iXFQsqCiYBHIsgl172R0NEm37pDjQ,236
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_approx_quantiles/tricky-scalar/out.sql,sha256=58smHs_2DxGMsQihmriS4YhsrktgvkeYxPSi_T5wTpk,115
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_binary/out.sql,sha256=2FkBY2PomKMakzHv5y30_STVKs7bhbc_ghIVwkZhk0Y,76
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bit/filter-bit_and/out.sql,sha256=GuDZYmTPd7uEPYhJal4nyGPlRzT4m3fTOuGpRcGo8rc,141
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bit/filter-bit_or/out.sql,sha256=THtFMjIq5vm8r6qZ77_9kGCVC3ExoJHMDSwMjuxVvTA,139
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bit/filter-bit_xor/out.sql,sha256=4MEskQDWPz1nmoNrxauoKDTCmnxrcsyYzm3_guapGtA,141
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bit/no_filter-bit_and/out.sql,sha256=5PdSjTFP4FwI0bR_2Slk4Re0dG11h3hAQugB5sqtz1A,87
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bit/no_filter-bit_or/out.sql,sha256=HCDceq31yNSQv6By1KhGdoaN6b047DP6uF_BwEvsAYw,85
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bit/no_filter-bit_xor/out.sql,sha256=XTY33ybH8i2AX6qQveDdW_zUdiEjNJqPhhrWmSEASGQ,87
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bool_reducers/mean/out.sql,sha256=dwMz_8C5GVsP2XgBJ4ReZSLYlysQuVz6yP0tSnXgQIA,98
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bool_reducers/sum/out.sql,sha256=WOnUX-Imcizk5GMG4GbD3MsOKciBLHb79LEYmdjDPp8,97
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bool_reducers_where_conj/out.sql,sha256=U_bw59ov0bRPxgag-8bXzJgsTDRBcyIgQ1U4seSf910,249
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bool_reducers_where_simple/out.sql,sha256=6s0AYMk6JvGg6t4nHClo-bjbVzbhuFs9Pbxv2CXCRR8,142
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_bucket/out.sql,sha256=vsFllcLjPgQOxluJxTliurzwQM4U-NYSlsqw6fYc97w,234
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_cast_float_to_int/out.sql,sha256=uTO7AOP5ll2YWOa4QRUqxTBqzrNZ5L8kLlYeDXIbKdY,110
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_compile_toplevel/out.sql,sha256=OfAEWvZEBw-B5U0DJWSY4lapK5Va1t5vbG9ERxvDIp8,55
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_cov/pop/out.sql,sha256=3qkqK1LPQXnyEqLVXxVlS8wgzqgKfPbtL-mBVmgctHs,129
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_cov/sample/out.sql,sha256=WZfAb1_ytxNdW-tP1AGwoyXg7dsmLaW7moLu-4V1A3U,130
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/date/index.sql,sha256=B678UnwvMzpJRv9xfJIv2ebnjtcu4Hp5OSTkyNyyjAQ,104
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/date/name.sql,sha256=qn9zZn_IrYpk42WrDHkbN23jLHVzX50_FJyLQJ52-ag,105
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/datetime/index.sql,sha256=qR4JIPvjsX912roFNvRU0fS81E_Jet7H6IIHxYsp8No,131
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/datetime/name.sql,sha256=0bAULpWgCAIaYkh_yaVygfb4Cu4sKOO_xvaFFCC42qE,132
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/string_date/index.sql,sha256=B678UnwvMzpJRv9xfJIv2ebnjtcu4Hp5OSTkyNyyjAQ,104
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/string_date/name.sql,sha256=qn9zZn_IrYpk42WrDHkbN23jLHVzX50_FJyLQJ52-ag,105
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/string_timestamp/index.sql,sha256=qR4JIPvjsX912roFNvRU0fS81E_Jet7H6IIHxYsp8No,131
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/string_timestamp/name.sql,sha256=0bAULpWgCAIaYkh_yaVygfb4Cu4sKOO_xvaFFCC42qE,132
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/timestamp/index.sql,sha256=qR4JIPvjsX912roFNvRU0fS81E_Jet7H6IIHxYsp8No,131
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/timestamp/name.sql,sha256=0bAULpWgCAIaYkh_yaVygfb4Cu4sKOO_xvaFFCC42qE,132
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/timestamp_date/index.sql,sha256=B678UnwvMzpJRv9xfJIv2ebnjtcu4Hp5OSTkyNyyjAQ,104
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_day_of_week/timestamp_date/name.sql,sha256=qn9zZn_IrYpk42WrDHkbN23jLHVzX50_FJyLQJ52-ag,105
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_divide_by_zero/floordiv/out.sql,sha256=iP_5zxJXZrGpdgrSl4XP39rRGSmQLaagZjusBnUXkt8,129
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_divide_by_zero/truediv/out.sql,sha256=sjdizLy21lLwtZZvnhp48kzVAkMX8XvR49-ESGuZ_Zk,102
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_extract_temporal_from_timestamp/date/out.sql,sha256=jDMlNo3w2srd7w4XFPRok_4lmWKfWV68MFPG1jYoajY,50
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_extract_temporal_from_timestamp/time/out.sql,sha256=YX8_pK5BLeuubNFZ0YP2jnih1EJOK257L98wnIu3b6w,50
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_azimuth/out.sql,sha256=p7R8u146T3GbhIRwbQ_GNB6lXy-UyqDmg0JiiVbSZCo,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/contains/out.sql,sha256=Yqq_juzDeA2-dMPtJG0w9sLrrUblG3Ritj-3pawez3o,74
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/covered_by/out.sql,sha256=zbZ4sDWKjAs6LogLuS_NX2FynyEPF4rsxD7CLqbMoQ4,75
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/covers/out.sql,sha256=ZunR1JoyK01UF8AU_1quGLYybZRAXBqIJ9ym8a-EGww,72
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/d_within/out.sql,sha256=Ro9_Q3ZnQq68BvjNYxZPQN_qroiepNTRpJzuSQw7dmc,78
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/difference/out.sql,sha256=46f89b-uq5aXeZ7AFtjwHuKMoZaSQ7KbcXvAyfcnQoU,76
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/disjoint/out.sql,sha256=Lg2MazATe31vqlZ4pp024lf44YUUGJx8eT0tntsK2yw,74
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/distance/out.sql,sha256=ysgJutsFVWHtAmcyytB9v9NqptW4kkdLSOjbmVzeTsQ,74
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/geo_equals/out.sql,sha256=dXOpOq1w7u3VF4x3h3Y4AXwjkexkoEyXvv0GAJwjBYE,72
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/intersection/out.sql,sha256=pk1yaNdK1uFIuQgIoqxR4jukinnlyjng12YRbfSEMu0,78
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/intersects/out.sql,sha256=whT2BWhgAI_p0evW0w6isYjpbcgK5tc5ixMjHScyDOQ,76
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/max_distance/out.sql,sha256=C4eZ7mxZzZJwsl-YrOtSdKZ31roomywvdR4KNEj9jwY,77
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/touches/out.sql,sha256=11xcp1d8vr-ykwO0eJpgPT20gj5dAbtHbTJioe6IFzk,73
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/union/out.sql,sha256=JTADEK6PizhAQ7qpMYsPNf73MgoW6sKW1NSzIR5_evc,71
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_binary/within/out.sql,sha256=gjFj-py4r7uQaRDdEBw0WPR83G2hCfwQzRWENaVYS4Y,72
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_minmax/x_max/out.sql,sha256=3G8mKUaUsXpRZxdyUzQpf7arvl9k8X1MeofmqJvupH8,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_minmax/x_min/out.sql,sha256=Jiu6voLt1I8FzEKwNcfxiEF1qxKc11MyJ18nOLeZbRM,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_minmax/y_max/out.sql,sha256=LuY8DVujOo7lFMNS5bddD35tn1I11NTMrNkFTFqoUWY,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_minmax/y_min/out.sql,sha256=Tz7WvRZoCQSYHZQExH7Lm0VjyBUasf3jVgbdWozefzY,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_point/out.sql,sha256=EcQ_7ATCRoLRgUMvtm-xHVr5o3fInrlfM3zWW7UGHY4,71
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_simplify/out.sql,sha256=TvGTZnTBTpgJLKGHfnhX5jjvOOUPrhl798lzUYjIYzM,64
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/area/out.sql,sha256=cYZrjNJblUWLTZJdEjD5g4LsQ4kQ_E0ux66SSazR3gQ,55
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/aread/out.sql,sha256=cYZrjNJblUWLTZJdEjD5g4LsQ4kQ_E0ux66SSazR3gQ,55
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/as_binary/out.sql,sha256=9YdnCE3pvAARwFRLX2wEcuxBioFOqDQWaGWOpH2MjtA,59
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/as_text/out.sql,sha256=B2jBLguQC5po74pDyOquH1-rsvlSfbXrJmFi13cl7WE,57
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/buffer/out.sql,sha256=QtjVIkcvK7eee7v58w4lhPiQ4t8Wfvyap00Kc8x_JXE,62
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/centroid/out.sql,sha256=fi5vqpunXayrd1cbVVJxhuc_8-7PN0jg-qVOJ0cNgSQ,59
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/end_point/out.sql,sha256=6OYtVEynZwJNDY_DV71H662Sk1WJn-b2fuxg3C3BN24,59
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/geometry_type/out.sql,sha256=Bn1K3Lijz4nFR19ZusDC96irm4EJtkALbInP8qTKchk,63
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/length/out.sql,sha256=9fFcH_sNL4TCoxeMaqNtthppiei9mCfO6R-vEhRtOVw,57
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/n_points/out.sql,sha256=R_wUE_uPn08VnPx1KsZZpGX9ZGHDc32QRznWdYo35eI,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/npoints/out.sql,sha256=R_wUE_uPn08VnPx1KsZZpGX9ZGHDc32QRznWdYo35eI,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/perimeter/out.sql,sha256=iymshwF9ui7qc8IzumQgUGAkGlwpcOxMUNaZSShnGaw,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/point_n/out.sql,sha256=bNScdfLqaGUjfZDvqcC5LZAgNuDvP_iJYRKjnaqCQSY,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary/start_point/out.sql,sha256=o-sUVLWmrDvl9C5Cxxvcw--gq8qYnu9WUD37-i-T66M,61
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary_positional_only/buffer/out.sql,sha256=QtjVIkcvK7eee7v58w4lhPiQ4t8Wfvyap00Kc8x_JXE,62
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary_positional_only/point_n/out.sql,sha256=bNScdfLqaGUjfZDvqcC5LZAgNuDvP_iJYRKjnaqCQSY,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_unary_union/out.sql,sha256=WiSdYXeLzWOA_aDfHrF8RFKJFvHf5sG8U7-_7q0bl3c,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_xy/x/out.sql,sha256=Sf25xa6hzags-yJF_1K8RH1rijp35lAwhaNTnDRG5ng,50
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_geospatial_xy/y/out.sql,sha256=d942hSEUnwiyx25ezPFXYVFZjhJqmRe3xMQxvkCR8rY,50
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hash/binary/out.sql,sha256=LozWkBsr43OI1u89m_i8_LWiKXAemyoVGvplaGCJHRg,107
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hash/string/out.sql,sha256=EALB16xZGuD4xOjumLeFEwaXiUOgXiyhq6ubhhigFkA,66
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/md5-test-binary/out.sql,sha256=n9gr3EsKE3Nas5hL1zokQu-KnFhpcNEQzafNZcfIyHA,61
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/md5-test-string/out.sql,sha256=emdEaGK8MqYLHSIOBZIs_7qteqffuK76fEWKf3tE4AI,29
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/sha1-test-binary/out.sql,sha256=EMp7otLezOlFGbf1iAuO1czuq7y_pXSOzzI5xI0Br9M,62
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/sha1-test-string/out.sql,sha256=dmjcQmdU2vtg3ILX-6QEoaF8M4NgbuiNFuILQOF65NI,30
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/sha256-test-binary/out.sql,sha256=WSejcvd79wvrTcUwBcRQUUaNUgNWvqi3UAjjPa5ut00,64
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/sha256-test-string/out.sql,sha256=j6S8XwteSoPA5BVGq5BCfx8WU2iDt8xjimSMkj-JjFw,32
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/sha512-test-binary/out.sql,sha256=M3DfQfVzvBfNIMKk0VmlqxYFVh3TvMc7Coznhs8MJ9I,64
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_hashbytes/sha512-test-string/out.sql,sha256=mH2yisPkwR4aqOPKyKxUnnlKW-sKfmqBDSYnE5eNhdg,32
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_identical_to/out.sql,sha256=92YKkI4IhgcjoX025UmcxtSyuJ5oVkKcmEng95YobA4,150
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_integer_to_timestamp/ms/out.sql,sha256=9yg0L2uq0ZFysyWUxx1M337_ToH6WUpSZ5tBkdWla_w,46
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_integer_to_timestamp/ns/out.sql,sha256=A7CKbMl-Qhe2JCA4-usEHMKqaa5ueUs6pG5qb2uOvjo,78
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_integer_to_timestamp/s/out.sql,sha256=LllUnGxy_A3raIt38oaZNV69hNXOm3yRZHmywbcnqs0,46
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_integer_to_timestamp/us/out.sql,sha256=vwqk-Xc5qn81gz_7iMHjI5RRowS-jzm-iqlTnsTM91c,45
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_string/escape_ascii_sequences/out.sql,sha256=oEQrcwoDvz1mCTRoJfkS8Jk67FJcCZcQJAUPYmA-4qY,71
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_string/escape_backslash/out.sql,sha256=74K4TLrdCjw7rRNIfQF0J5qDgTAtVZ9GmtJf54qj71A,31
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_string/escape_quote/out.sql,sha256=rhkZbkG2LxOtCfEwANgxqw-TXhiGZgRHht-8o-TWQjU,31
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_string/not_escape_special_characters/out.sql,sha256=G45vOR54k577KNOteQtmJwQhyDIA3aDnw_Gxivo4mi8,63
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_timestamp_or_time/datetime/out.sql,sha256=hdROtJDmjywYGtEerkfZpmX40Zq__yey82jvUb3g8Bo,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_timestamp_or_time/string_time/out.sql,sha256=WWzDoy4jrEPqU5WhVMgfnTPJSWyHTnWxhRIAivp3XPw,52
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_timestamp_or_time/string_timestamp/out.sql,sha256=hdROtJDmjywYGtEerkfZpmX40Zq__yey82jvUb3g8Bo,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_timestamp_or_time/time/out.sql,sha256=WWzDoy4jrEPqU5WhVMgfnTPJSWyHTnWxhRIAivp3XPw,52
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_timestamp_or_time/timestamp/out.sql,sha256=hdROtJDmjywYGtEerkfZpmX40Zq__yey82jvUb3g8Bo,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_year/date/out.sql,sha256=maAFTH5GUIbDpSeKk1tXbNAD_m4lu4fzWzPcEuyWdfE,84
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_year/datetime/out.sql,sha256=rCvTGo0kkKt-bRzFhu41iRKvEIUUfBQR4E9_wbsAKhg,111
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_year/string_date/out.sql,sha256=maAFTH5GUIbDpSeKk1tXbNAD_m4lu4fzWzPcEuyWdfE,84
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_year/string_timestamp/out.sql,sha256=rCvTGo0kkKt-bRzFhu41iRKvEIUUfBQR4E9_wbsAKhg,111
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_year/timestamp/out.sql,sha256=rCvTGo0kkKt-bRzFhu41iRKvEIUUfBQR4E9_wbsAKhg,111
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_literal_year/timestamp_date/out.sql,sha256=maAFTH5GUIbDpSeKk1tXbNAD_m4lu4fzWzPcEuyWdfE,84
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_now/out.sql,sha256=oMs0PcHVdcXYj7zMrhnK-QEt5VojgHBkBAIsSqJdo7k,46
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_projection_fusion_only_peeks_at_immediate_parent/out.sql,sha256=m-09QNvLlC8KxIn33Y-o81bWLRCxRyF_Ur8V3ImUGSw,417
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_range_window_function/prec_foll/out.sql,sha256=-9qT1KXcVTBvDQrl5bVtW9xrdnyfhGfJmWKKOT1B3_w,459
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_range_window_function/prec_prec/out.sql,sha256=2EnXuWiNdccpr_4_GP1wl48PtUAarE4BEFLOXxphfcY,467
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_set_operation/difference/out.sql,sha256=J-D7UFYi_OZEAeJbwSl1PhY6LY04T5lGuBfXGPYmNvE,73
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_set_operation/intersect/out.sql,sha256=ow88t8KOaMoIg9FjKgkAF2lcqbcrC4sFJIe_yX973Ds,76
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_set_operation/union_all/out.sql,sha256=t1AWDIjMZgnRn4X_zZS4kt0wj5le_GsJpu3OEuv9NPw,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_set_operation/union_distinct/out.sql,sha256=Z5oeXjkvtYRWWALXNFzg2ns95QmLUcmiRC8L7zXkX0w,72
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_subquery_scalar_params/out.sql,sha256=kGXv2kuLX_7MRDpGfPKZ0Jnv6xB8LKCEMsGC3CDjz1c,277
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_substring/out.sql,sha256=MP70hTPkBH0MA6Iy3hPHI5gUsfKo-R4eIwKSQovDpq8,124
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/day-date/out.sql,sha256=2Se5WbP-GFfyxQmYbq5hzxFhqFROjQu2r08vnrZL9TA,60
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/day-timestamp/out.sql,sha256=EMr0-VwitVy5TEB7fSoQNu_-whZGAkCeWAjs2Ok2UYE,65
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/hour-time/out.sql,sha256=tCaKHbpwKoqs4SWZIefA69oL61zMk-Q6RFjkyDa8bcY,61
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/hour-timestamp/out.sql,sha256=2WxY3fOEtS8KUHVf0g25qQtBEmAiKQBg8l6cSVuVspw,66
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/micros-time/out.sql,sha256=EO7zF3Z9xl8t0oqH0xJdErDm7PDOkxqlyWqA7ty9FK8,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/micros-timestamp/out.sql,sha256=R8kn4srwUw7LaenQgbhT4DExGG609BPu9J8b7WSftFY,73
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/millis-time/out.sql,sha256=BEmm0gmNH3IGoCffqcK8fx_kTDTNV8ljUdYUGixFJ7s,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/millis-timestamp/out.sql,sha256=DTKQUOuhEYB5Jp_icG2_c-caPq3q-JqboJClKDKUplw,73
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/minute-time/out.sql,sha256=aYb8EHRI_LcXpKZlIx8HOxAUA1-Su16BodBqY4XQLss,63
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/minute-timestamp/out.sql,sha256=sA5DuNd4gEi_PrySWCgkWSFbloc4BJMGWk4wDGVv6QQ,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/month-date/out.sql,sha256=Z2CTvYNfbltBb7JwjMzfL2CpLO1thyFFJiUfbXkGFS8,62
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/month-timestamp/out.sql,sha256=JfBKfMXFaahy1dPaTNr1WWoA7xlaeT3iuLxhak50rqM,67
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/quarter-date/out.sql,sha256=jXdP-VhB3y__A-8Bk20zsdwdPe1iJyeqmqQhZ0HseWI,64
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/quarter-timestamp/out.sql,sha256=gNXGehELegP-m-mSriSzDclQNZmRTfs95aBXPlGwC_w,69
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/second-time/out.sql,sha256=-70Y57OlwPWt38V6NokmCJYCXMFSHT9qCmSQLTGbqoI,63
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/second-timestamp/out.sql,sha256=_fOLDHjNUxQzVUJoSQSog0kgjJPQPuY2C8FtSS3PagQ,68
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/week-date/out.sql,sha256=fEw4hT5LrDhuVRcuPl3qSTKQYfDY0XFjaXC72RAXQ5o,69
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/week-timestamp/out.sql,sha256=wPZhMI7o6TahJR8vQeuPpfz5VoiMCfGPF6btt8vvp5U,74
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/year-date/out.sql,sha256=tYNZIq5Xks4_OROBpQ5xj6f_2z1VrmDPGrQe_h-H1p8,61
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_temporal_truncate/year-timestamp/out.sql,sha256=1QWwCMDhhvqTPxC8VoctZnNA7nCWQa3ujDF3Zjc2AqY,66
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_time_from_hms_with_micros/micros.sql,sha256=iIJ_WHMNV7p89Jb0l-l-LHeSBO0U6YwBdvacM5Up8ZU,99
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_time_from_hms_with_micros/no_micros.sql,sha256=pTE2kSqHEWP04dJFl3mC49F2KbWhwhdCvkAMAcgQo7k,53
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_to_timestamp_no_timezone/out.sql,sha256=3lGCN-EvtTGBtDlIJjIyqxS-3g6-4M2i_zNHd9HQ2f4,140
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_to_timestamp_timezone/out.sql,sha256=VzKBMmCD7smnczwBTtWZIAFAWIdcRgkUoj_LkmuvU4I,208
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/days/out.sql,sha256=hgu4RF9IKL-Rd9aQXqOv2dg8a4Kq_hdE8kNaoLL6Obw,460
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/five/out.sql,sha256=joEjWQTP9NzmphN0QURr1cfs073_C80uy3H97XWkG_E,420
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/hours/out.sql,sha256=XTdQj2tpj_lJdD6HlPquT8NYLpnv46Fingzu6r_ALrw,462
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/micros/out.sql,sha256=tvbgaYukynIbZgaBNt6TLAL0cIiJo2SXqnll2JuJ1rM,476
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/minutes/out.sql,sha256=UjPXlfH2FylM-6omm0HRJzRcg9AgE3p-vqsWbrOrnag,466
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/seconds/out.sql,sha256=epPdaaTM6i7gSeeABWPTzXd56_2IawAdzweJYR7hqwc,466
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/two_days/out.sql,sha256=fGgjp0vwgdffD89XoL1yoUkdp4O-Jii4WZrb8SUm4Pk,497
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_trailing_range_window/week/out.sql,sha256=Jes21ZxhsgaEtwNEFOuCxMW1nYLoG3qN_VfzOXtEsSU,462
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_union/False/out.sql,sha256=FQ1TuthTpHTYQQg1arPvc8pHiHSW9xIRHH99vg95STo,101
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_union/True/out.sql,sha256=2laJE0MXXObSkNQGgw01DsAaHtDORiSLVtYrIi1zpdE,106
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_union_cte/False-False/out.sql,sha256=EZ9-XzFriQvbq11O-1nh-xl-eTzKLzSfv0NjWcN813s,290
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_union_cte/False-True/out.sql,sha256=aiVtKe5z2wmrgNENWA_ImVJreX0f7D9BScTApvaNruE,295
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_union_cte/True-False/out.sql,sha256=N2JbT9ff03D3Omlz20S31vVs3nKcmVSgQrMkswtij0I,295
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_union_cte/True-True/out.sql,sha256=1flT5XxakN-7NUi9QVBRzo0wasggKwlkF-Mfwv7Rqd8,300
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_unnest/out_one_unnest.sql,sha256=VVeg_UytQ2ouhiWlVnb04RV8upTkW-cVGNIEcE_Xekc,490
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_unnest/out_two_unnests.sql,sha256=_4LknYO7DYg3ymneSjl9g68I3HcouwfivwPuMek1b6A,905
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_window_function/current_foll/out.sql,sha256=d3Z-Pp5i-rUpK1c4J62_DEEOQzLIsdi7JKXHnWBXW9o,460
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_window_function/prec_current/out.sql,sha256=n9AYx6qc27SET3SzRaUKLF2hNnf1JQyw4Dbs_alWgTw,460
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_window_function/prec_prec/out.sql,sha256=HAHRF8yU26SNXUiyyyeoX7Xj4Asqc_BViJl6Lo1sPcY,460
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_window_unbounded/following/out.sql,sha256=a3BZBEn3iZmRr9U-zp7RexJEXgeIvuW3w1uSWX4PhCg,104
ibis/backends/bigquery/tests/unit/snapshots/test_compiler/test_window_unbounded/preceding/out.sql,sha256=pZzXd0hWd0iNArQ1ehhFO0sDkydXJwIGPbvL8hC71_g,104
ibis/backends/bigquery/tests/unit/test___init__.py,sha256=TdkNrks4sNL1wwXqCto0FzJsv5APHeSD6Q3lBpvQmPI,4210
ibis/backends/bigquery/tests/unit/test_client.py,sha256=XzuywWYj4143i_OjZEfJi9600H0mYmyrc3IH0KLj5ZU,1793
ibis/backends/bigquery/tests/unit/test_compiler.py,sha256=ZqLIURw3NXXnG58H8ZWZtVGdi6UcTEDb_cz8k6XbCSY,22307
ibis/backends/bigquery/tests/unit/test_datatypes.py,sha256=abNRIqjPBLh4SM2KRMPymg09WyeUyztpd_c71j2e6gY,3124
ibis/backends/bigquery/tests/unit/udf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/bigquery/tests/unit/udf/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/udf/__pycache__/test_builtin.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/udf/__pycache__/test_core.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/udf/__pycache__/test_find.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/udf/__pycache__/test_usage.cpython-312.pyc,,
ibis/backends/bigquery/tests/unit/udf/snapshots/test_builtin/test_bqutil_fn_from_hex/out.sql,sha256=MKiU4nla2nNi5KvldGRYV05wPebqZlz4Nj8tB-K25Lo,58
ibis/backends/bigquery/tests/unit/udf/snapshots/test_builtin/test_farm_fingerprint/out.sql,sha256=eSOKsyLPEAzk5WWGxnOj9kEZOZcs_Oqqfk_ZT_gBtIU,123
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_assign/out.js,sha256=7OpFSAMEx9Z9UCGktop_jJIG9NQxsYW04Mlpr6xC7OA,76
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_binary_operators/add/out.js,sha256=-aEsjh1V8PNb_w-BWPPwwvhm-aoTJ0P1FQdtJCGIrcg,42
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_binary_operators/div/out.js,sha256=8-MUl8rcgYjMouoh7AzfN-grYkfCfV7-X89GqlNACTw,42
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_binary_operators/mul/out.js,sha256=ubD58DMXqvQShhCwWn3n52g7pbnhBAKbWwqhBsgsl7I,42
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_binary_operators/sub/out.js,sha256=g5DHdIZe1QveiG_0UFRG2OoAmKnOvfuAUhuIWZ33Pwg,42
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_class/out.js,sha256=gAxKLg9KnTJhpfMAl_RnScSjXfcX6whj1F-T3K2EhyI,178
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_class_with_properties/out.js,sha256=NZiFIp0naAi-UhQFSIR3G-Nzs5RBAq6WpaptW_b2SUo,125
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_continue/out.js,sha256=XHt9UcMObW1qYqL0yKj7nvGSHqc6WAIvfhSgbqFrTUE,139
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_delete/out.js,sha256=M9KZ6A9gi3crbJvFqg0cCjof2UnN1sKxhky8mbQe754,154
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_dict/out.js,sha256=FVbugD5gIjpO56aj89Dyh50HqYffzwBHbeY4Daqo6Mk,64
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_floordiv/out.js,sha256=l5McLBnYgkhsoV84Uze5Sfvf0EjVk1wwmikGBcla260,61
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_function_def/out.js,sha256=ahP3dwweb1j9dLzZLNU93Vjqb5AAoBJD7AB-cQ6ze64,40
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_lambda_with_splat/out.js,sha256=nguFj15Lg7Ha4r2_8sXcmUx6vEjJG_1WBSATbkfrraE,249
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_len_rewrite/out.js,sha256=OrXqCYl0toryTjLs26BCYBrVP-7bn5HPBvgC_aj9dtE,44
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_list_comp/out.js,sha256=kdLq74hJI7IbCGEZRwLT0xYnudLNgrQriS_YjQ_9PHQ,138
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_logical_not/out.js,sha256=tD_QJb3VnkqwipIocT6dS1FmVrgAB-t3bqx4Urif2QY,80
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_missing_vararg/out.js,sha256=vZ-CWBH-LAztSsm5DgTtYhs6szx-6WM4gFBPiYhDL9s,56
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_pow/out.js,sha256=Zy-oCU_5DbogpKjj47D1w4n0lkSukis3EziKI0WlrLs,58
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_scope_with_while/out.js,sha256=bIwa1TIJDgQ3mQa_ntQGqHNZ_TT3lsvPbUjHhi6k0cA,321
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_set_to_object/out.js,sha256=K_M3hJPSRfmvLjHVxcFoLL3E7Hl8cxh3smZq4axaDIY,85
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_setitem/out.js,sha256=6o3UWSOLnq8Ubqhnm1PH1cnhiF5uf1gJVlGKJxbGHD4,78
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_splat/out.js,sha256=OTImWx2nKu_NjRQo-Yvn5HUzYzaGVeZoMx-ZdBY18t0,134
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_str/out.js,sha256=N3lKQOgjy6ORhHgwX5AMc82avG_8TCitFWwSwGs92zg,49
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_true_false_none/out.js,sha256=IqyjWdxoK0vBJ0jBAAl7gQVnZchCwuqqF9LzfeRsCU0,106
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_tuple/out.js,sha256=eJAF5RccLpEet6UUVeZqnEkHa8lQwSGNw3gJCbsIbeI,59
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_unary_minus/out.js,sha256=tpgV9Pz2c3zDmBzrnsp2yzLz-crlYS7jCGnL3qstIt8,48
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_unary_plus/out.js,sha256=GKUltrXVED3dgof2bWyJwVIa6luLytWe1mWlfErr9Sw,48
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_varargs/out.js,sha256=u8x2Viy-fNvpNazqOiz5eCfy4F4FWJ8H_0VFQWzl76U,48
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_variable_declaration/out.js,sha256=5C47rcStG5G02VI3ktO1YhP9rDuDYnv1n2mCHqmpcLY,51
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_yield/out.js,sha256=9e8Ru3lIAN-d9r9086ZYxa850xKPc1B7FlGn0tIWZOs,40
ibis/backends/bigquery/tests/unit/udf/snapshots/test_core/test_yield_from/out.js,sha256=7lAGt3lQvbBivJ6qS4J_nGbfzuj0lNuVbS7H6Mn_K0A,40
ibis/backends/bigquery/tests/unit/udf/test_builtin.py,sha256=Jmb1Wav2sSv0lICGKQeu1BG5f729Rw34wSv39Hxi6yk,799
ibis/backends/bigquery/tests/unit/udf/test_core.py,sha256=lOOdWOHEUaSuBjtmuKcLsuv2fqSMC9SedENnJQfohWk,6572
ibis/backends/bigquery/tests/unit/udf/test_find.py,sha256=PTAjlOpg2p75FfcmwFZKPStleHF6eAt6o-fmw1TQ2pk,2020
ibis/backends/bigquery/tests/unit/udf/test_usage.py,sha256=2YYgar9MDYRUFMzRhoHcGmUpTWXfnG8EDZaR-SdVxc4,1927
ibis/backends/clickhouse/__init__.py,sha256=YHNUnhj76kLLBA31dNP-e2hsZHLa3VOlE5cOSQDf14g,25726
ibis/backends/clickhouse/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/clickhouse/__pycache__/converter.cpython-312.pyc,,
ibis/backends/clickhouse/converter.py,sha256=Wk2rt3nxwa4MBmR8RfcOUo1IsZ5KwVP1H1xRRqhw7lw,1649
ibis/backends/clickhouse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/clickhouse/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_aggregations.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_functions.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_literals.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_operators.cpython-312.pyc,,
ibis/backends/clickhouse/tests/__pycache__/test_select.cpython-312.pyc,,
ibis/backends/clickhouse/tests/conftest.py,sha256=uzqcq-XAe31tNrIOtJeKn3BZZyaFRQk17GiG_rNH-G8,6091
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/count/out.sql,sha256=n6v5Xcx8ZT5IduloPWheXZ0iSelKigxNI2Iiy3rPNlc,139
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/max/out.sql,sha256=SgzkG1muxjjspCKi98nBAmu55GqmnCPBhIWvH9j7IZc,135
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/mean/out.sql,sha256=UyOxdlakE5xupewFHJWH2B-uVRxAoFYpfTpyNnSQ_38,136
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/min/out.sql,sha256=dezFuR0I1tlGcqd_oN5_Btp_ciVMfuXsvraGDlVPaTg,135
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/std/out.sql,sha256=VplSIptsXP2lHZHfLv1Kz4gU92slA1vIdAI_E6FNnWw,150
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/sum/out.sql,sha256=oV5GvWITQseZb0uONP6Sum6iadpPR1NIV9aVBZicqQc,135
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_reduction_where/var/out.sql,sha256=-FQUWxc4cJyPPzsBsOH0n6R6OygloQR6qQjcn1Tqjys,144
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_std_var_pop/std/out.sql,sha256=oK0u8iffVSppBnc8B2a7p7uyiKg6dhbD1hRXD6_kc_E,149
ibis/backends/clickhouse/tests/snapshots/test_aggregations/test_std_var_pop/var/out.sql,sha256=E8E6oe06zH8zczVczFAe2mSPrkqMSTZShHQnTzlju-A,143
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_double_col/float/out.sql,sha256=LQNmb92IkxKoGpNRB2OwdoFP-P3ABN3sytV4_edEE78,77
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_double_col/float32/out.sql,sha256=YF24CH_7woDU6NpC2o-kHIXNAg2MHNNZ3UZMsEZdk3I,119
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_double_col/float64/out.sql,sha256=rktnpPh4nXni1vikaVyUiP9pCQ-Y04Uw9IcSVAR4-BY,110
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_double_col/int16/out.sql,sha256=pIjgjYAM9Fq56sEDJ_4X8w-_hC3MkbpxR4QVPOIMR0I,115
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_double_col/int8/out.sql,sha256=qcz0pqhCjgbStnBdYYz3QF-lrvFDntD7Gst54BfcngM,113
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/date/out.sql,sha256=4Gx7YCe0cffcqCjAzbE880C4wHpwRCQpJb3K4sQbmzg,113
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/int16/out.sql,sha256=QHroYDjIPqaHBakK8lQ6IbQnT6-9favi6_arsux3JpY,115
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/int8/out.sql,sha256=eRiwgpRh4oAm6MwPvZGEnbecoLRzxjPVxOFQz7OqrQM,113
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/mapstring_int64/out.sql,sha256=MSVDbNw0vOLFaTsEYsLZ4pylH7HLzd_0o8IFACpuaYM,142
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/string/out.sql,sha256=sGMTriuVDTzEPqRUzgiKvDMLD3VdCTM8WyV1QrfgxsM,108
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/structa_string_b_int64/out.sql,sha256=MtBEh8AGYqM65llk0zlGG5NtwtjueYWIWUz03Ly3YSo,171
ibis/backends/clickhouse/tests/snapshots/test_functions/test_cast_string_col/timestamp/out.sql,sha256=fUQPb1w1ve0PJaff9YOKFk_GUkFrDvWQJr_FBISN8Vo,122
ibis/backends/clickhouse/tests/snapshots/test_functions/test_column_regexp_extract/out.sql,sha256=_4kFHMnVIzpOuzla3Pwz4URk8JxDDcBShkqoN5ZJ91M,319
ibis/backends/clickhouse/tests/snapshots/test_functions/test_column_regexp_replace/out.sql,sha256=Jl4BtZB9vb4eoKOxKxC8mxmQmK2QGx1_hLZVFwK41BY,143
ibis/backends/clickhouse/tests/snapshots/test_functions/test_greatest_least/out1.sql,sha256=bVaUK6-Zd47MNaGsYifn0QKPZpvetABnD0yShLC0QsQ,101
ibis/backends/clickhouse/tests/snapshots/test_functions/test_greatest_least/out2.sql,sha256=VLhkk5XrOojXK2zBtgjpV-ITX5XEGMCa-jDB0iNjfgY,124
ibis/backends/clickhouse/tests/snapshots/test_functions/test_greatest_least/out3.sql,sha256=udViup_nPQrSMFwwGPfL9iqr4WyNY9meSwFGBs58V68,95
ibis/backends/clickhouse/tests/snapshots/test_functions/test_greatest_least/out4.sql,sha256=aSTfEKV7VTVkomNWsDB4OqVBGUuql9Aeb_ldOkYIVSc,118
ibis/backends/clickhouse/tests/snapshots/test_functions/test_group_concat/comma_none/out.sql,sha256=zQt-ekpsO3CkcrifIBuQe2lY0ZhP_vSVayjBCHM7Oc8,215
ibis/backends/clickhouse/tests/snapshots/test_functions/test_group_concat/comma_zero/out.sql,sha256=z8doJaSK8rWsSVMq17F1uu0HUVlef2J4UzNcZmJY6Hg,282
ibis/backends/clickhouse/tests/snapshots/test_functions/test_group_concat/minus_none/out.sql,sha256=IwldYpyIbcdLHhdrxn3Tnk7K0BCu4jegVIZ6A4iMONI,215
ibis/backends/clickhouse/tests/snapshots/test_functions/test_hash/out.sql,sha256=LXLP13-gSHml6vFCd8ATXi6QMTnpHzjEqxL6kDp9FBw,114
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/bigint_col/out.sql,sha256=FVCVcP7nAP4WPkc25cRUWxUCqlU1uAaprSAc52xE1tA,77
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/bool_col/out.sql,sha256=b9nQTsUcqNv7HpzcMeJm6Rvk3IKF2rO5BR8fE-N9Hxs,73
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/date_string_col/out.sql,sha256=GJP6M5Y8KXVI8PwMfcd3rucAkbJq_wqWLxHKDi90288,87
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/double_col/out.sql,sha256=LQNmb92IkxKoGpNRB2OwdoFP-P3ABN3sytV4_edEE78,77
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/float_col/out.sql,sha256=LkiWsEqhkYuM8RRXzVJCls2QlmsWXWhWxl2YmtyBGKM,75
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/id/out.sql,sha256=79P4acMklEjUPkHUuy4FGeSS3fGgeh8LGucm_WdxoRg,61
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/int_col/out.sql,sha256=LrjOXXL_yiZUdWBDoWni7tK0v0uwcAklugbnFrgb5g8,71
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/month/out.sql,sha256=xAlj-AIORwC7EKHhk3PbqjZ-DtX0Foyirn_HwuE6SMw,67
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/smallint_col/out.sql,sha256=4TQ63qdaZoGoYg_gZ9QHTNULKbqoZEXs1kxHK8zJICI,81
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/string_col/out.sql,sha256=r69dP_KlaNkMaKFGwEzAhivbKCvy1i5ocqS0xDiVFx0,77
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/timestamp_col/out.sql,sha256=lUgqJCi0P3EoINmHGfVJ2F_naxcw5TjUjX9OczxMJs4,83
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/tinyint_col/out.sql,sha256=iajMjLns6Nf_5QUqaui_Sd_m6iLdYa1AfNioaMg29eU,79
ibis/backends/clickhouse/tests/snapshots/test_functions/test_noop_cast/year/out.sql,sha256=8UvPeQiOpBXZYIO-vvzlTdmy4qzQOM8FbjjnfUeeDYU,65
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_find/out1.sql,sha256=oskHOLCk8cEVJb15-kXLyLyep6ZWZaWuQrHEJNcpRGE,113
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_find/out2.sql,sha256=SBm3Km-fQ5gbTI03jQFBCGylMs_AUlC_S9NTP_ydyOI,134
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_find_in_set/out.sql,sha256=YUgSF2iTmn5jDK_tpulS-8_Vp9jWkFV-vgF7kwuvx3A,135
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_like/out1.sql,sha256=Rh16QPN6u4y3TIfAUmdcnEOqnsGfHqpTzpJv6zDomEY,112
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_like/out2.sql,sha256=6qw_8baofSb9F1BmFeVUMzwUsRglPtLln4XCw5kLwc0,184
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_substring/out1.sql,sha256=aMAuTsGrglkyyA0j4zfm6YW1FGX2VSNUXFdtRDDcGG0,204
ibis/backends/clickhouse/tests/snapshots/test_functions/test_string_column_substring/out2.sql,sha256=f4ZAusyTggdf6U-ynZKfuGX_Wg59ojr3QpOse5jdDdI,214
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_cast/out1.sql,sha256=3xfFmUsHKbZObd7TdaPcGhjDtRcV9wERTPGQF1r6NoU,119
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_cast/out2.sql,sha256=BjCnbRx2N-hVcLTEE0EI88Za1r0bWbDFRU3E4U029Zk,107
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_from_integer/out.sql,sha256=aC6uFUkSjjCeA7a0KPouqlEK1X79dpi4WgTh5LzAVxc,110
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_now/out.sql,sha256=Hy6BAL3iIA4xuwXrU3UvFLHGiXfcWifCHFcB_P49e90,34
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_truncate/d/out.sql,sha256=WlEEaplraQ0OMxWXjpbvXTriGIOx0c8Y_ghhvCaE2Ds,169
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_truncate/h/out.sql,sha256=JH_2qZbzbsbjSFDAmd4ADdZud0KAwSjzQo11gvvjYMw,143
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_truncate/m/out.sql,sha256=8V9_-w5tIUSSOWkHcMBQ2u24mThQBTJCFsh4CneJS7g,147
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_truncate/minute/out.sql,sha256=8V9_-w5tIUSSOWkHcMBQ2u24mThQBTJCFsh4CneJS7g,147
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_truncate/w/out.sql,sha256=B5ImW5cNFbDmBRQtyt8ab99pe0RVDB9qKgHjMYnkFBM,166
ibis/backends/clickhouse/tests/snapshots/test_functions/test_timestamp_truncate/y/out.sql,sha256=TuA74hwG5SSk1Dromejek9YVBf8G6vyZOj2fXIa-d-g,171
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/abs/out.sql,sha256=YwT3tfPiAD9j11ZuHcPPx5R5KbbDAJZ8eyQ_yuY4wcM,87
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/ceil/out.sql,sha256=mcxXwwLs9f_LtbzUn2MDMgNgB-WQkeBHhsq2NtrvGtM,114
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/exp/out.sql,sha256=ZIhxl8dUqOhhsVSpFg14bEs7SiMEH9MqDkO-I3bHF-w,87
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/log/out.sql,sha256=i5utF7_JZD1-PG0qKKgX_Q4qQesvELe669-EDBXL82M,86
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/log10/out.sql,sha256=c1FqokMXYvEaUF9asoab7fVRO6GksRyqSDrv1hW0dBo,91
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/log2/out.sql,sha256=psPtkDd5VL0QBl3nFhYIh0W9eCmS0sczR_OSLaI4sBY,89
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/round/out.sql,sha256=wss3OLciwfRb4PZG696H3uwhTcXxDD3fZVuBZAENw8w,122
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/round_0/out.sql,sha256=wss3OLciwfRb4PZG696H3uwhTcXxDD3fZVuBZAENw8w,122
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/round_2/out.sql,sha256=wOTg4hxhJasdTlHJgDVeAGIrt9ESiQlXfjxF9yhv0Rs,124
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/sign/out.sql,sha256=tEBWFIRBdeuVngk2HB1NJZ9NGZe7-gHWD5QBxaPIBjw,121
ibis/backends/clickhouse/tests/snapshots/test_functions/test_translate_math_functions/sqrt/out.sql,sha256=3jsHkCZXBBcCy2HNFlbnEqsUqDFBNBEBV9CN8BwKfHg,89
ibis/backends/clickhouse/tests/snapshots/test_literals/test_fine_grained_timestamp_literals/micros/out.sql,sha256=HzNWrLHnSL9_Dgc-e64uc2RniiTJ8FaaNf5gB2jHsCY,122
ibis/backends/clickhouse/tests/snapshots/test_literals/test_fine_grained_timestamp_literals/micros_tz/out.sql,sha256=BSmroNY6S8f0K_KEm0NLO7quqm9tTvEMXx-Wqlo7vsY,151
ibis/backends/clickhouse/tests/snapshots/test_literals/test_fine_grained_timestamp_literals/millis/out.sql,sha256=i4NBEpA4IEHonKiEa6Yvt_cwrTF7FEtjiwCI521beKU,122
ibis/backends/clickhouse/tests/snapshots/test_literals/test_fine_grained_timestamp_literals/millis_tz/out.sql,sha256=SMdRpxHtq1ng0LaLXMWf9DizgSPXAk-DSvFg9xkDZM0,151
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/false/out.sql,sha256=KXxjDfsS14zGvao3Nr0PXVxnjDHIfBIjmiWVwP37MGc,25
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/float/out.sql,sha256=WU8Kt4glNy8WvnD5SmHroAwTs2bcXTDd-bDIcOViz-4,21
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/int/out.sql,sha256=QWUeN_C6RL-JdEAvNIeAmefymTnpIDZG4CvDL8u2qSo,17
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/nested_quote/out.sql,sha256=y1Dl83WLq05JrBayM0dG-xh_iyE3o3QGhgYvVZx2AhQ,36
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/nested_token/out.sql,sha256=t-XJOfLEHZdvACMW6kO-1KG_71z4PC1wEgNKXM3NzDs,43
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/simple/out.sql,sha256=qA3k5Meazpm9qSPLBW2RT5HLAm5aIOc6AllmgIfXO3Y,31
ibis/backends/clickhouse/tests/snapshots/test_literals/test_string_numeric_boolean_literals/true/out.sql,sha256=vAmNTnkOHQLIaR9TnzJXRr5-rvzognrQtVVy3bReQg8,23
ibis/backends/clickhouse/tests/snapshots/test_literals/test_timestamp_literals/expr0/out.sql,sha256=sN67ShtJ7q0bey4gFdgHdBMEdnk8D5wolEN46CxlEBk,102
ibis/backends/clickhouse/tests/snapshots/test_literals/test_timestamp_literals/expr1/out.sql,sha256=sN67ShtJ7q0bey4gFdgHdBMEdnk8D5wolEN46CxlEBk,102
ibis/backends/clickhouse/tests/snapshots/test_literals/test_timestamp_literals/expr2/out.sql,sha256=sN67ShtJ7q0bey4gFdgHdBMEdnk8D5wolEN46CxlEBk,102
ibis/backends/clickhouse/tests/snapshots/test_operators/test_between/out.sql,sha256=pl2NCt3Od2WXVGpsAJe6eCyYRN3qyPhRxGDVJIsHs1k,104
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/add/out.sql,sha256=kW0sBUyjesCNneSwJmL5iZ7_3cah0k09OBlfOM31y8M,110
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/eq/out.sql,sha256=Tsf0hB--6DKETWf7IXdVxGYGWVjrAQjBoskpBE2u24Y,113
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/ge/out.sql,sha256=5Xt1bu_36oHAXAe3NNW5Cq808YTQoT_LNcjfKpxiqW0,120
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/gt/out.sql,sha256=6IQA_8_-4V8wuc1Jk54xQlKUhXUYuw0La2YZHdlZCHo,114
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/le/out.sql,sha256=BwXoYCAhkv7TdHPG007jK9DlZpbptscEqKzo5004r3I,117
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/lt/out.sql,sha256=YS5_mNrvszjK_81IGyB-nKIjKxd9O8Ol23lFhMGHpxc,111
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/mul/out.sql,sha256=hxsMmKrjP_Zpc_y4DBt8_Tfqsbp0_9bp8PfW-OvheIg,115
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/ne/out.sql,sha256=Syck_qcm7-b8LDlGUbzFaoi1fwe8nYtUjTOShIV9iMc,117
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/pow/out.sql,sha256=64RKKl0jaV1SQzmUtkE-NMRp7IhvWgdUACufB7YYdlY,118
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/sub/out.sql,sha256=GuvL-BfyuN52_h7--szLaiaOIg1xJA8BaWSy8LyU2YQ,115
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_operators/truediv/out.sql,sha256=UgrfhpSZ5fBqAcCH1J9RjiDH0281raTJ2UEbhGHHDFs,113
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_parenthesization/lambda0/out.sql,sha256=UWpPKjpv9Bq9DzVdYXsWDLWPy0z40vNinaDMiuMcDRI,147
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_parenthesization/lambda1/out.sql,sha256=7p8gnJviKJvpPxBX8MTqj74hX4qnmqG0tlAg4_vQz54,117
ibis/backends/clickhouse/tests/snapshots/test_operators/test_binary_infix_parenthesization/lambda2/out.sql,sha256=E7vpdnM0ZydS67UqScMYwCpvO1bbHNG-BDyXH_BGCtM,166
ibis/backends/clickhouse/tests/snapshots/test_operators/test_negate/float_col/out.sql,sha256=tgdH_8igcMZfE8oWaRpiOdu-iaHA9nAOMf-SkQYIwLA,94
ibis/backends/clickhouse/tests/snapshots/test_operators/test_negate/int_col/out.sql,sha256=wm_LeLLSiFbUnwz7vs1yOtegv_w6TFENZzUrFNpLk-g,90
ibis/backends/clickhouse/tests/snapshots/test_operators/test_search_case/out.sql,sha256=E7Y7G7A9M4AYdc6C7c06kUK3YENHiVxz7vD-bzT9rTk,276
ibis/backends/clickhouse/tests/snapshots/test_operators/test_simple_case/out.sql,sha256=k6tqjhDwWCX74qPJNr3-QEeu4rDHTd4uf5_uvrM7nBY,200
ibis/backends/clickhouse/tests/snapshots/test_select/test_array_join_in_subquery/out.sql,sha256=dSHJrj6IJd1wShEIdi13rlRv1ezc2CMAzyWcQ8gGYC4,148
ibis/backends/clickhouse/tests/snapshots/test_select/test_complex_array_expr_projection/out.sql,sha256=HGu5ICikoloK5jINnQ0I9vL5OseMHvj4_3ACpc77wFA,243
ibis/backends/clickhouse/tests/snapshots/test_select/test_complex_join/out.sql,sha256=VMshbuIqSWv0bGN5CO7SfcA3D22JKcajzNe-_Ab-Egw,312
ibis/backends/clickhouse/tests/snapshots/test_select/test_count_name/out.sql,sha256=Nm_aNOXUmaEDi-IDOQxtapjUzktQlmBmjWAbPngDA-w,157
ibis/backends/clickhouse/tests/snapshots/test_select/test_ifelse_use_if/out.sql,sha256=KZahfqxUtmWJzcfXJxG400eC_uZ431XMXxKwmvmqHEs,177
ibis/backends/clickhouse/tests/snapshots/test_select/test_isin_notin_in_select/out1.sql,sha256=1WTw76olLpe30pPFlHYUlVhLXY2NftFNUWk3zfJ5uog,89
ibis/backends/clickhouse/tests/snapshots/test_select/test_isin_notin_in_select/out2.sql,sha256=Hjo8-G2rYjks0C9HTbrNtCnvhW59VW8TgW3q4JUg27k,103
ibis/backends/clickhouse/tests/snapshots/test_select/test_isnull_case_expr_rewrite_failure/out.sql,sha256=iEpcpNvuxmRfRjAIcrGF3bdMoBTwAj-8Bm7zWUD0vGQ,145
ibis/backends/clickhouse/tests/snapshots/test_select/test_join_self_reference/out.sql,sha256=cJQSsj_OC15a-Wg-VzKiPCkrwimaXxeeBJI2FZygkqM,561
ibis/backends/clickhouse/tests/snapshots/test_select/test_named_from_filter_groupby/out1.sql,sha256=BLkCtR55ciovUcrqn9goTzwx5yBtreC7Ui1BXDSUu8o,174
ibis/backends/clickhouse/tests/snapshots/test_select/test_named_from_filter_groupby/out2.sql,sha256=HgsSsW1cAfQWRspCztbYI4YUbgU08QDwAeNqzpdwZoM,174
ibis/backends/clickhouse/tests/snapshots/test_select/test_physical_table_reference_translate/out.sql,sha256=RHJt7N5r-Zw5DTzABz6CsvS0xUYlF8lXDTGjltZT-FY,37
ibis/backends/clickhouse/tests/snapshots/test_select/test_scalar_exprs_no_table_refs/add/out.sql,sha256=Hm1Pa5Yu0xqNtjQiRp4WTu38zLWxPxD9FY1oJLGN83A,29
ibis/backends/clickhouse/tests/snapshots/test_select/test_scalar_exprs_no_table_refs/now/out.sql,sha256=Hy6BAL3iIA4xuwXrU3UvFLHGiXfcWifCHFcB_P49e90,34
ibis/backends/clickhouse/tests/snapshots/test_select/test_self_reference_simple/out.sql,sha256=9fbbvNYtCZztryfs06H9cl8E9iCRIOodfJ1DFunxylw,45
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-awardID-any_inner_join/out.sql,sha256=rqR30LycpAu314iSd5LddYYAlnfoTw7cMk4LYta3blY,608
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-awardID-any_left_join/out.sql,sha256=ewbCpsH6WNIRY3zgQFx0FlvDlmYAkIQ9pXHYySKu1Q8,613
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-awardID-inner_join/out.sql,sha256=XSN2n_vmZrVq3zBwinRGFjlVfmNSi_5xnxPhOqoSJqA,610
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-awardID-left_join/out.sql,sha256=Z2vOAH2Yi1vrH0QoqEc9QjF_6XCUCXVW1b_0oGgaXFs,615
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-playerID-any_inner_join/out.sql,sha256=6vHYKJRVrJIpIOIPxCT50AHrmTAPBRx9XLHX5ijs4jI,609
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-playerID-any_left_join/out.sql,sha256=Me51aF35Ds_KFmP4RJ-IYbgRSwdda80y5gNkJgsPPHc,614
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-playerID-inner_join/out.sql,sha256=Vl9jHiyCPeHpGpNxJAjZtnBJdsQD5ZsLCsqfYliksVg,611
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_joins/playerID-playerID-left_join/out.sql,sha256=Y8DL6ww5gsUEfcsS1X5uiQuuhtqmDfJD05rNxIH56i0,616
ibis/backends/clickhouse/tests/snapshots/test_select/test_simple_scalar_aggregates/out.sql,sha256=zLOSQnE8l3pObfBsLQgql2xuOzU7qvtAwEL9DteUB_A,150
ibis/backends/clickhouse/tests/snapshots/test_select/test_table_column_unbox/out.sql,sha256=KAnQU5hRwXpQKgu7pWwQDsdOPinR0eSW0cDoUlON0O0,291
ibis/backends/clickhouse/tests/snapshots/test_select/test_timestamp_extract_field/out.sql,sha256=tblft0oQV4t17Qxn6ck7FiE8h9kuu5eyaHcAb_M9E4A,307
ibis/backends/clickhouse/tests/snapshots/test_select/test_where_simple_comparisons/out.sql,sha256=OeK7Sw_2qQ4JYEToOpiiB3dz7CfBDMtlX24_Zne8CMk,126
ibis/backends/clickhouse/tests/snapshots/test_select/test_where_with_between/out.sql,sha256=MwGMNyXFyskyFrI618GSYMUG0HRJcsCtSBaZVpUVcj0,109
ibis/backends/clickhouse/tests/snapshots/test_select/test_where_with_timestamp/out.sql,sha256=dad925aB7V4D-fj6Rxh-V82WrwCSpwhm1quVpv_llFs,129
ibis/backends/clickhouse/tests/test_aggregations.py,sha256=PksU6bK6mg3mGM7G3UFLpnwOeVA21lm--Ym9YTD0KdU,4940
ibis/backends/clickhouse/tests/test_client.py,sha256=5rT5CHdNZwjIluQSGZVHuOA0xLZrsocE2OXrV_WFjYI,14121
ibis/backends/clickhouse/tests/test_datatypes.py,sha256=MVYhuLxgP8yhEGKZjzX42aqSyxt_GYPS90NsYXgr0YU,10457
ibis/backends/clickhouse/tests/test_functions.py,sha256=wwvozsp6e-azpeBcP8gZ4nrS1Sz1tUEImv437Itk1Tc,14364
ibis/backends/clickhouse/tests/test_literals.py,sha256=eO-cc1p18sko6Jc_cYNW80QEOO2NkEfc4R3oN09NXgw,1812
ibis/backends/clickhouse/tests/test_operators.py,sha256=GYbLW28ETu_zsAUGzJ1MzA6kQ7jwTpQK3CW9VFCcpNA,7073
ibis/backends/clickhouse/tests/test_select.py,sha256=XKQV70PkTnE8hyboicFL3pZO0b230--loMSjBvVYjOk,10423
ibis/backends/conftest.py,sha256=LhkghoVlgOmZYMnZuOtScTDN02QvE3u5YP1HSpi0PO4,18475
ibis/backends/databricks/__init__.py,sha256=Jn1uO3peSHK0hzIviQIcaU-AQRkIZuilxbbb2YuWRkw,20282
ibis/backends/databricks/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/databricks/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/databricks/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/databricks/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/databricks/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/databricks/tests/__pycache__/test_json.cpython-312.pyc,,
ibis/backends/databricks/tests/conftest.py,sha256=Z1_j8Hkk9qiAaGYKUtsbyHFm8dvSsZ5CCjMZuVeHvas,2710
ibis/backends/databricks/tests/test_datatypes.py,sha256=1ibs5PPudGB-qlRoHVY8jJWKC5eLmy9PmA2_xe0bGAE,2675
ibis/backends/databricks/tests/test_json.py,sha256=njAO6XERhqIztAg_XYjAYdxjyhJxUvSUUqPN1ZBYjZY,543
ibis/backends/datafusion/__init__.py,sha256=ZaXTvB_a5MO33mQBR0FKKQNwHSMbnVfrjK6MEstNcPM,26595
ibis/backends/datafusion/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/datafusion/__pycache__/udfs.cpython-312.pyc,,
ibis/backends/datafusion/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/datafusion/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/test_connect.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/test_select.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/test_string.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/test_temporal.cpython-312.pyc,,
ibis/backends/datafusion/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/datafusion/tests/conftest.py,sha256=sp75k7csxSGZ625ScurCyfVfX_Mr55SGXu8Jeo6wLUc,2948
ibis/backends/datafusion/tests/test_connect.py,sha256=SdNBIEDof30HkHF8QsP5unK1WSEjxKBRa2epedYoSuA,1175
ibis/backends/datafusion/tests/test_datatypes.py,sha256=nA0_t1V2S-IertgJUqjXNUlRbeEKtjr7PXGUDZ8LUbM,652
ibis/backends/datafusion/tests/test_select.py,sha256=m4l8xVqQO_UYSJOV06gwWGiMlucWSeyBwG6ZeBD9hVs,663
ibis/backends/datafusion/tests/test_string.py,sha256=xioEdBn6HYWWuhTGpvTIfONbUOnGx5JNcnTbDa9JvN8,177
ibis/backends/datafusion/tests/test_temporal.py,sha256=_j9WRuSyfa8ZT4ICMICupMHmOHVguzujgcRhNes72tI,760
ibis/backends/datafusion/tests/test_udf.py,sha256=sL227iiswCfxWJuYGETDdyDHs0sFYW-HLKDgGghK9jE,2149
ibis/backends/datafusion/udfs.py,sha256=q88fFQIMXAwdswTU7MHfkJ5W8v-NScTvdrYhq0lurfE,3603
ibis/backends/druid/__init__.py,sha256=ZH5HyVE5sRyA9-fpOVS3FV2Ran0S9giDyLY37EAqS3c,7823
ibis/backends/druid/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/druid/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/druid/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/druid/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/druid/tests/conftest.py,sha256=ddyhcdhvW5G9A6xf_1FATyc_kiHUkslFvc6dFt09oXY,5206
ibis/backends/duckdb/__init__.py,sha256=gzTDVFW14M_kw4CNdXe2ejan9FRY87dIZikcJ4FYmkY,62042
ibis/backends/duckdb/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/duckdb/__pycache__/converter.cpython-312.pyc,,
ibis/backends/duckdb/converter.py,sha256=qVjYwn4xEFYOC0f86oTFSYKvXF2Gvh_rBz0UGTDj6j4,856
ibis/backends/duckdb/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/duckdb/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_catalog.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_decompile_tpch.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_geospatial.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_io.cpython-312.pyc,,
ibis/backends/duckdb/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/duckdb/tests/conftest.py,sha256=CPX-pveK8r-KBs2SDoJjdc9KeplGa_RXkJb7OlGJC2M,5565
ibis/backends/duckdb/tests/snapshots/test_client/test_to_other_sql/out.sql,sha256=RHJt7N5r-Zw5DTzABz6CsvS0xUYlF8lXDTGjltZT-FY,37
ibis/backends/duckdb/tests/snapshots/test_datatypes/test_cast_to_floating_point_type/float32/out.sql,sha256=ENTsSaSYYsSJuVofAF0yUCYVAg94UQbFa3Qawa8v84c,54
ibis/backends/duckdb/tests/snapshots/test_datatypes/test_cast_to_floating_point_type/float64/out.sql,sha256=KeM5eDwMyx6LINgj3bO-77S2QY60MShDvGbWGHxsN_U,56
ibis/backends/duckdb/tests/snapshots/test_datatypes/test_cast_uints/uint16/out.sql,sha256=lBDv7fYsF8MhUUMakPZRdcF9zpiAA1oWvorqZ-0JH9k,74
ibis/backends/duckdb/tests/snapshots/test_datatypes/test_cast_uints/uint32/out.sql,sha256=Z6wx2F3GCQgzPf8k3OdcV_fH4GmRfFdps6vTdKyQ4WI,73
ibis/backends/duckdb/tests/snapshots/test_datatypes/test_cast_uints/uint64/out.sql,sha256=APh-DrhzrHl0KwL6yOYC6sN8yQYQ0GNIwvdwNZE5Soc,72
ibis/backends/duckdb/tests/snapshots/test_datatypes/test_cast_uints/uint8/out.sql,sha256=lV133Nbrl0yPnUxRkphDVAr5FMt5XpogzdUC3dBw3WM,72
ibis/backends/duckdb/tests/snapshots/test_decompile_tpch/test_parse_sql_tpch/tpch01/__pycache__/out_tpch.cpython-312.pyc,,
ibis/backends/duckdb/tests/snapshots/test_decompile_tpch/test_parse_sql_tpch/tpch01/out_tpch.py,sha256=uYkD9ZUPYW_RdnayWhB0kJF-uv1VeLlnUlA2DZRg9bE,1368
ibis/backends/duckdb/tests/snapshots/test_decompile_tpch/test_parse_sql_tpch/tpch03/__pycache__/out_tpch.cpython-312.pyc,,
ibis/backends/duckdb/tests/snapshots/test_decompile_tpch/test_parse_sql_tpch/tpch03/out_tpch.py,sha256=9HnxJpO4VDv3F1b4mDHOcN049KYTFaFrLAR1TTQumBw,2998
ibis/backends/duckdb/tests/snapshots/test_geospatial/test_geospatial_dwithin/out.sql,sha256=bpUhmVU1kweQ8vX57ikgWVZ5cM8RkjpNyhdtdLmGVsk,76
ibis/backends/duckdb/tests/snapshots/test_geospatial/test_geospatial_unary_snapshot/as_text/out.sql,sha256=LkWdq5lDhDJNL2wBgkoxop-r9q57kmxHPelQwI8z_zc,57
ibis/backends/duckdb/tests/snapshots/test_geospatial/test_geospatial_unary_snapshot/n_points/out.sql,sha256=msvFnAJiBFvrz0kuj0KgTNr_yRsIZtlC9vYMqiad2TA,58
ibis/backends/duckdb/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr0/out.sql,sha256=8NSRjWHsyD9tcLEcViAiMGTexApQrznkaMfAXo-77bs,103
ibis/backends/duckdb/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr1/out.sql,sha256=8NSRjWHsyD9tcLEcViAiMGTexApQrznkaMfAXo-77bs,103
ibis/backends/duckdb/tests/test_catalog.py,sha256=Riut7Rfu0rvem1E24QNYzrmWyWQG85GzewwipMD_f38,2528
ibis/backends/duckdb/tests/test_client.py,sha256=7cxv28TH_Yx_mNI72-fad9sNZO36Q9_RejPu8ZJrx3Y,15144
ibis/backends/duckdb/tests/test_datatypes.py,sha256=fP2sy6b4LA5T3UvYTkGbe-5pUQonUatq7Z8svToSggU,5774
ibis/backends/duckdb/tests/test_decompile_tpch.py,sha256=yv2h6iCvSo8ZQPn0M3c4jRs74kQ4cjkZLOEmFVrra8M,2957
ibis/backends/duckdb/tests/test_geospatial.py,sha256=HOYSWCzyMQQiKcIUe-UHtYqVVYPDJDuX3Cdn7xHZJeQ,17924
ibis/backends/duckdb/tests/test_io.py,sha256=yWU3wOyTweeehEpOwte6IcmwomordAG623IbUascipk,14812
ibis/backends/duckdb/tests/test_udf.py,sha256=gm8KVo14dfxkF6NhdeLbvhG1tpm8_m3nq1hsA2GeioM,4581
ibis/backends/exasol/__init__.py,sha256=iScWWEJ64POYY2bPE92IqywkhVwWu2wq4PpdA_eRJ3U,16048
ibis/backends/exasol/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/exasol/__pycache__/converter.cpython-312.pyc,,
ibis/backends/exasol/converter.py,sha256=E6fG0Co0TMAmyuEaZTa5L8TXmYyVcYiQwz8efIFrTUE,1669
ibis/backends/exasol/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/exasol/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/exasol/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/exasol/tests/conftest.py,sha256=K6J5auB7mlQLsK0upaBOaifKksIEZTPW4mE8LKXhY9Y,3296
ibis/backends/flink/__init__.py,sha256=QPBdTf8Gb5qEkVme18Ur0QWgOh5hdg-cGm6mxEO5_4Y,35590
ibis/backends/flink/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/flink/__pycache__/datatypes.cpython-312.pyc,,
ibis/backends/flink/__pycache__/ddl.cpython-312.pyc,,
ibis/backends/flink/__pycache__/utils.cpython-312.pyc,,
ibis/backends/flink/datatypes.py,sha256=2w-5AQwidU4d-d7XvdbBcT5xxsIBtJ4_TG4kr_dDLPo,6885
ibis/backends/flink/ddl.py,sha256=R6bOYvAgdN3D0L-_unJLGVNnZuarag9LRP2l5EDkT_w,10797
ibis/backends/flink/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/flink/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/test_compiler.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/test_ddl.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/test_memtable.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/flink/tests/__pycache__/test_window.cpython-312.pyc,,
ibis/backends/flink/tests/conftest.py,sha256=dh9-36dBlLcmtVcawZcFv23dtGp5dYbQx3o3u46ZofE,6895
ibis/backends/flink/tests/snapshots/test_compiler/test_complex_filtered_agg/out.sql,sha256=G3NIc9vZEARtUIJO9YEtleH2_Wr-H3dQrG-y1W7vQcE,237
ibis/backends/flink/tests/snapshots/test_compiler/test_complex_groupby_aggregation/out.sql,sha256=-eOIwYw6MkTIxrerDRMWuHXEWRiRMX7toX2eM0vTLnc,246
ibis/backends/flink/tests/snapshots/test_compiler/test_complex_projections/out.sql,sha256=KbC2XTPEtPRbps-3tqme5C2IkH5uqBbmtUEcizHkPVk,220
ibis/backends/flink/tests/snapshots/test_compiler/test_count_star/out.sql,sha256=PNWKSFhHER1w7pgyFTD5pg_j_C2C_0jT_8xYnDjFFvI,92
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/day/out.sql,sha256=150WsaykJ2nZwsLFGP87ljPPW7THu-FZCqe1fhCzM2Y,65
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/day_of_year/out.sql,sha256=GH5ctqqTPd11hzMekqPH6PGLNCwaqP7HgC215WH3R98,58
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/hour/out.sql,sha256=VAbTWSQHZ6Q0jBoX3eqJK1I21oAm4snheVIMdfUw0NU,66
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/minute/out.sql,sha256=mWkDv6LeEmA6jVOLwB7jvYUdenobeVc219dZgVDbaNI,68
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/month/out.sql,sha256=LeKZQIy_Mazr6pDtTX3JxrQYEBPYWqiWqJusdZ2wFqg,67
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/quarter/out.sql,sha256=UN3cy3acqWkVfduZG-Z4zyRaTJS301LNGdU1TysV6Ho,69
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/second/out.sql,sha256=ZIduNWviH-EimBx5p-AAuj16fpv0Y2BTWNlaxzNDhcQ,68
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/week_of_year/out.sql,sha256=2W0ySG61x0LEh6uWJLbxGN0q7DF25wulbop8yQVRoww,66
ibis/backends/flink/tests/snapshots/test_compiler/test_extract_fields/year/out.sql,sha256=HMScb9C-uiQimLxfKZWWkuk8UXzHNIwqYewfOW8E9_c,66
ibis/backends/flink/tests/snapshots/test_compiler/test_filter/out.sql,sha256=CzEONHUa_P7is4f9hxikXSbNNhL1xjwoHXNl_--k4go,133
ibis/backends/flink/tests/snapshots/test_compiler/test_having/out.sql,sha256=HkrPjcITkGExz7fK3cWroy_CJ7KZDw17S1diAy2B-gM,225
ibis/backends/flink/tests/snapshots/test_compiler/test_simple_filtered_agg/out.sql,sha256=S72mZZ6wqWsQl3nv6GlwJcUD-bPCFSOkh3EHOmyMKvI,132
ibis/backends/flink/tests/snapshots/test_compiler/test_sum/out.sql,sha256=zzYTcaPR0Yu_HsfkTwjWuMmfCt7LXk8XO31u8em-DVU,55
ibis/backends/flink/tests/snapshots/test_compiler/test_timestamp_from_unix/timestamp_ms/out.sql,sha256=5ZRfOJ6V5Lf-iy4IBRjL0T-_3QJk6KaGv3yMOA8Ad4M,117
ibis/backends/flink/tests/snapshots/test_compiler/test_timestamp_from_unix/timestamp_s/out.sql,sha256=MBsWnnYr8dO5m1KTuZ_5NMwenG0v2yZj5_KEfuBMBO8,112
ibis/backends/flink/tests/snapshots/test_compiler/test_value_counts/out.sql,sha256=N5Dk7eQfKNVCJzfi5lv5h-kefOdGmTP1g4PCq7qz1fc,202
ibis/backends/flink/tests/test_compiler.py,sha256=9_ylqridgXZjFMleHaDA0bywfdvgWeO8CPAgCvdDjxc,2733
ibis/backends/flink/tests/test_datatypes.py,sha256=yRrglTpg_HoNM4JiO3XNNbe66Y9x0QQbxTWVxYwzziE,1181
ibis/backends/flink/tests/test_ddl.py,sha256=fz59YyRQ4SmPbQQ8DPZWNiu9aVK3wC1Ry8exkRFZII8,18086
ibis/backends/flink/tests/test_memtable.py,sha256=NT1YKxR12TZU2GDNarLw4z2DNFnca600mK3xy4ziZsg,1750
ibis/backends/flink/tests/test_udf.py,sha256=Qpa7GBHOQSnGR2fd2l7yLVkiUM5Bs5Ai-p9PjKe1HZs,632
ibis/backends/flink/tests/test_window.py,sha256=BTyCoCChLPUIJrsy-6CTfBlKdhY7r-AYhY76CzoGzaU,2339
ibis/backends/flink/utils.py,sha256=oP0V1liNlvglG-nBb37olwA90gj1Qr2VvjBxlD7YDIU,11772
ibis/backends/impala/__init__.py,sha256=ONRP-wEaJ5TZ4QX56oxqj1izGgXbBL879iWOMr_jhEU,46732
ibis/backends/impala/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/impala/__pycache__/ddl.cpython-312.pyc,,
ibis/backends/impala/__pycache__/metadata.cpython-312.pyc,,
ibis/backends/impala/__pycache__/udf.cpython-312.pyc,,
ibis/backends/impala/ddl.py,sha256=hXteZkRpSalobU1rx68kpxLo7edqDvIT3t9iPEOPlrI,16026
ibis/backends/impala/metadata.py,sha256=yz4fdtnshafljCI9yPAmVx4gecdETza6VB9UYr15HAM,8596
ibis/backends/impala/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/impala/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/mocks.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_analytic_functions.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_bucket_histogram.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_case_exprs.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_coalesce_greater_least.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_ddl.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_ddl_compilation.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_exprs.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_in_not_in.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_metadata.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_parquet_ddl.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_partition.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_patched.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_sql.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_string_builtins.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_unary_builtins.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_value_exprs.cpython-312.pyc,,
ibis/backends/impala/tests/__pycache__/test_window.cpython-312.pyc,,
ibis/backends/impala/tests/conftest.py,sha256=RW6ArqesSsTdt9dB9UNPftArlUtb9yz1ppBpBh8wVII,6960
ibis/backends/impala/tests/mocks.py,sha256=zKXry1V_h_vjnkHfybGOYSSnrqP8FBZEMuRBzSSBmZo,210
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/first/out.sql,sha256=SQhM6-oMGPuEY0wWMZ9XToVD7rzEUhKX6KXFzy6Kh90,188
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/lag_arg/out.sql,sha256=4IUJz1rAarIu37T985hgY8KoBZLc4TeWzC23MxyJ8sE,118
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/lag_default/out.sql,sha256=IPWDXS05Pm9iwS86sHT4fBRhXoMeDU9kbhWWxPtKOBg,112
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/lag_explicit_default/out.sql,sha256=RW24LyOEGI9EKcp3nD4jCwf3vT1rbuzeYJpJRRwwln0,121
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/last/out.sql,sha256=7bfaF7J9TUk0ND5UZAQ-kwosFCmPNzM2c6WyweO0_mw,186
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/lead_arg/out.sql,sha256=kp-ClknMvYD_Yfn07gpbl3LGe3OhlRhklepYsEmuLqI,120
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/lead_default/out.sql,sha256=UZZypFdfLujaP6Mf3MiSHV0Vqi0zIEzh3mUEpvvHu7c,114
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/lead_explicit_default/out.sql,sha256=1xvoLCalbuI5LNqTmrhj3HX6EOvS7CsjW32_EIFtIeM,123
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/ntile/out.sql,sha256=UXSqpwkPUrXelJtrtMk-O1u-T7u1JEPn-uGxW7UXZDM,108
ibis/backends/impala/tests/snapshots/test_analytic_functions/test_analytic_exprs/percent_rank/out.sql,sha256=1KkXUlN_7kCGBpSTZRC_Sax4hf0OIZKonZjfwH75Uvs,115
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_assign_labels/out.sql,sha256=Lt04AQ5Z_IUohQCTbOUiSW1Qo_LGSGH2zmz3CsL03-4,678
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/close_extreme_false/out.sql,sha256=0TTSoI7gFiK2NQ2AiODJuG9zxTxVC3l0UCnUSOpb7VU,318
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/close_extreme_false_closed_right/out.sql,sha256=poa2hQdr9f1pRvIuaN-Ip6m-HvZAgiaJTNRRjDnMQ6o,318
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/close_extreme_false_include_under_include_over/out.sql,sha256=5Z0kxLAKmhVh7-sJA469TYePIKUbC_Ntx66Pu3nvDmo,386
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/closed_right/out.sql,sha256=lfH_L5zsXGo8zEOyGnCY7MMRAZiFQ7Ub_VknlYIUbmw,319
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/closed_right_close_extreme_false_include_under/out.sql,sha256=8dJEjmx3m_NA7tGPSRlskX6Dh5B2-Ke8HzrV-KwrTzU,352
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/closed_right_include_over_include_under/out.sql,sha256=ru9wzEBaQfW8eLHZFzovlzIafzf-H8KL5cVGCnentTA,123
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/default/out.sql,sha256=0t9m-88xqB_Imw3r5N8WO1N6nK5NJVHqDnmF9yfznig,319
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/include_over_include_under0/out.sql,sha256=2b8CoKWipPSAatsUrtr7mDUibUHZCvsZd_02mF7Rvls,123
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/include_over_include_under1/out.sql,sha256=y451XEMk70mKBDAfyCO_HLJJBw64jcOxJoociPKM6x8,149
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/include_over_include_under2/out.sql,sha256=2xmvmPbu7Jeb2JW7NbFh2MD4ui9Gf3UDnXhgjED5JmA,154
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/include_under/out.sql,sha256=qf3a3MBim_B8oxZIN7H1yGzHFmibWvB1-wEQRvGICak,352
ibis/backends/impala/tests/snapshots/test_bucket_histogram/test_bucket_to_case/include_under_include_over/out.sql,sha256=qghss5PBxbBXmZe9vwpbuhBRIBuUUCxXlMJuQAUHp3s,386
ibis/backends/impala/tests/snapshots/test_case_exprs/test_decimal_fill_null_cast_arg/fill_null_l_extendedprice/out.sql,sha256=htdzLewQOAiD35_TeE3aQjzRa4-xhBMUMj0B3U-LtRw,109
ibis/backends/impala/tests/snapshots/test_case_exprs/test_decimal_fill_null_cast_arg/fill_null_l_extendedprice_double/out.sql,sha256=2zQB05NEliG5XcU1oQoY6YZJziJQsQftKCGC3D2Tog0,113
ibis/backends/impala/tests/snapshots/test_case_exprs/test_decimal_fill_null_cast_arg/fill_null_l_quantity/out.sql,sha256=VFaQxo12cq13weI9mA3x8RNAAFCeeo4osCZY2zs8nHE,99
ibis/backends/impala/tests/snapshots/test_case_exprs/test_identical_to/out.sql,sha256=ISOi2XyPpZddQIYEmGr7vIDMCdlfVx2t2S1XiDNo6i0,110
ibis/backends/impala/tests/snapshots/test_case_exprs/test_identical_to_special_case/out.sql,sha256=v7onWKTFfc6HIFkvTfNKht3paSka3hYuMDGfy2rpdk0,80
ibis/backends/impala/tests/snapshots/test_case_exprs/test_ifelse_use_if/out.sql,sha256=tTG7F554S2nzf3GIgmQvqSgXQhkt-1SVr3z65OtSUpg,102
ibis/backends/impala/tests/snapshots/test_case_exprs/test_isnull_1_0/out1.sql,sha256=DPPKF1b5lFm28_oas8F0hi2qN2-Q7o6-YX_nFPZTxZo,88
ibis/backends/impala/tests/snapshots/test_case_exprs/test_isnull_1_0/out2.sql,sha256=ZAv-RqFudo9ERSscWgdIwYmfe5CnGbIXYkZ7rKTdd9I,98
ibis/backends/impala/tests/snapshots/test_case_exprs/test_nullif_ifnull/nullif_boolean/out.sql,sha256=NpXbo2osr1dgcX9d8GSC_a5Tq4M19YmYO0M6FRWEQXA,148
ibis/backends/impala/tests/snapshots/test_case_exprs/test_nullif_ifnull/nullif_input/out.sql,sha256=ORwtH94SPwF96B-b8JaHwMj29OL_lTOT1O1v5Uop2jo,118
ibis/backends/impala/tests/snapshots/test_case_exprs/test_nullif_ifnull/nullif_negate_boolean/out.sql,sha256=rnT2e2-4QVYskN6vFtHqZYKt7nv2jUqXE04ybz8wNPU,152
ibis/backends/impala/tests/snapshots/test_case_exprs/test_search_case/out.sql,sha256=MgKLp1yzSLofIYhTpNWBQLkrthOwZvpZyOb2OfMhPSU,232
ibis/backends/impala/tests/snapshots/test_case_exprs/test_simple_case/out.sql,sha256=7_udMaqUL42sqsyM3c4feuUQ8kWxypBQbCT9b-tRewU,171
ibis/backends/impala/tests/snapshots/test_coalesce_greater_least/test_varargs_functions/coalesce_columns/out.sql,sha256=H_wsrnbj0KIHtDM9vliYVt5e0ctkGhG_3MZfGJuUvOs,124
ibis/backends/impala/tests/snapshots/test_coalesce_greater_least/test_varargs_functions/coalesce_scalar/out.sql,sha256=1uHNILpK27Y-r2GSfbn3EmWOIZs1v365GTTfgzXJozc,113
ibis/backends/impala/tests/snapshots/test_coalesce_greater_least/test_varargs_functions/greatest_columns/out.sql,sha256=TitOMZAxMVxEtymCgrssEy031gbuArH4j1kLPS6cOCg,124
ibis/backends/impala/tests/snapshots/test_coalesce_greater_least/test_varargs_functions/greatest_scalar/out.sql,sha256=GE66ICK81Ov6wF5uodUB6Zej6ePWueQIXotqt6DJwLU,113
ibis/backends/impala/tests/snapshots/test_coalesce_greater_least/test_varargs_functions/least_columns/out.sql,sha256=4AQ_9Xk9DEocEgRlYv6j8rgG_DpTYnmiRgmAIDyDpg8,118
ibis/backends/impala/tests/snapshots/test_coalesce_greater_least/test_varargs_functions/least_scalar/out.sql,sha256=GTxaFJbg1bYeCgWBjXvhdvO8zHKEH7zt5PW_5FWlwZQ,107
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_add_partition/out.sql,sha256=esypmUewJPmBfD-FzSx2UPnq8zcQmNyWG2Cu-V8QjFg,50
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_add_partition_string_key/out.sql,sha256=0YFwwe3NNL0RSYCUdepjxO7VpVyqsbIoWLXQvIu5diQ,48
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_add_partition_with_props/out.sql,sha256=wekEMj8WPvUJodq0UxH3w2XBK90SWbqa-doVHhLEIVo,80
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_partition_properties/out1.sql,sha256=hvyOz4YBHIvRZ8bzSh9ljp96rEY3B2gh87614y_YxW0,80
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_partition_properties/out2.sql,sha256=r65ch7nA5mM96rmatXvABv8yQQrsLfQdDzlXHa80cFk,66
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_partition_properties/out3.sql,sha256=cXlO4VXIO5ZT1m9tpzviIHq6-9_1xDgdsxaNXF-eSWc,93
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_partition_properties/out4.sql,sha256=6aDD21LTwdrxIwTYsvp2tfaJZN-IGSi3crPMu3AHGws,82
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_table_properties/out1.sql,sha256=hvyOz4YBHIvRZ8bzSh9ljp96rEY3B2gh87614y_YxW0,80
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_table_properties/out2.sql,sha256=r65ch7nA5mM96rmatXvABv8yQQrsLfQdDzlXHa80cFk,66
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_table_properties/out3.sql,sha256=cXlO4VXIO5ZT1m9tpzviIHq6-9_1xDgdsxaNXF-eSWc,93
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_alter_table_properties/out4.sql,sha256=6aDD21LTwdrxIwTYsvp2tfaJZN-IGSi3crPMu3AHGws,82
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_avro_other_formats/out.sql,sha256=rF_C2g74rdqGedJjs6QsujWcO3-yFt_M5htfnm-ASLE,90
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_cache_table_pool_name/out1.sql,sha256=8MxPW-yvHzcq4YdGbNk7lmqrcLxTeNoIlJC-rw8ocrA,47
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_cache_table_pool_name/out2.sql,sha256=Wz6ewthWrnIq52cDpg6FKFoiVyINf8SQ4jESxdiZKak,47
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_external_table_as/out.sql,sha256=7tougU4glbWjPUOQAY1fXMf6yKKUk993TitA2RZVnB0,114
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_external_table_avro/out.sql,sha256=4n5_e0sP5-wb0m2eGlDTrBs6GapwqM3XBRmHRfKwza0,491
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_table_delimited/out.sql,sha256=CvHp7Wg5xJXcDswSYauGWgMA0JYjOh0-ale6zqy5svY,241
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_table_like_parquet/out.sql,sha256=AqUArLJOx2A2q-BuWUaJRnES1D7YzFAkKRgoE6QIyJo,128
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_table_parquet/out.sql,sha256=B8BDaVS5Ftbrs4VpmWq18HeorSiqqoVPyb8MbcPogGo,128
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_table_parquet_like_other/out.sql,sha256=THcgHx__oP-wWFw5gf0tUNl8zwvTWnZlRcalPS2i-4U,106
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_table_parquet_with_schema/out.sql,sha256=bKXaJlqPG4vxqHnedlaulG_OQ7mkFb5R7NNfShPCrhs,140
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_create_table_with_location_compile/out.sql,sha256=5ZJgd2k1Iz_FMj-BwkMtCDr9d4zcJq6wRlM_CvGoWRc,126
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_drop_partition/out.sql,sha256=AfVhrHpV4bTgEibnwdkdXZ6yQynYdRGh0062TsVzJsU,51
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_drop_table_compile/out1.sql,sha256=6-SCdobPw8JxebL-uUCv8-_G4B0uTZk8NCSSAOxH6D0,22
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_drop_table_compile/out2.sql,sha256=WDZuDrpFcwAcgfa6dgDauOpwsER4_btdSU8A6lYrjo4,32
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_load_data_partitioned/out1.sql,sha256=CltYPClzv7dpCADxSPFW_6gEi-1-kA-M_Pz6mWzBBss,102
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_load_data_partitioned/out2.sql,sha256=k-W5k-xrqCPZYflOiMO1RWWJoWVR0GPmve_pSwZwp1o,112
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_load_data_unpartitioned/out1.sql,sha256=k-zR8d_zrz2EdIiRCZcpZZTyw0KTQxqH1BZr6UvEWpQ,71
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_load_data_unpartitioned/out2.sql,sha256=mWOMp_WPXQlocSrRjFEc6K8D_05HnQSh3uXte5wWxe4,81
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_no_overwrite/out.sql,sha256=Fc9cuueHRFeBp-GZs4_xhVliFP5qXU5Yg_oSzySVX1Y,131
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_select_basics/out1.sql,sha256=QKAXfvVD6BrLXu2IKH3R1HzMdvJ1kU0EYGXEizl-NJo,88
ibis/backends/impala/tests/snapshots/test_ddl_compilation/test_select_basics/out2.sql,sha256=tHnLw1kXE7sjC5GdNBGdeNS7sEUxz8N_ancsXhywgoY,93
ibis/backends/impala/tests/snapshots/test_exprs/test_filter_with_analytic/out.sql,sha256=0KRxgc2lpBD9aOsJRcQLaq9gefTt9b98sx6nI_CuhDU,234
ibis/backends/impala/tests/snapshots/test_exprs/test_named_from_filter_group_by/abc.sql,sha256=I4FxR9D5k1O4M0iI0s3nSdLkiRYbF8kZIkLAgBEs2Mg,134
ibis/backends/impala/tests/snapshots/test_exprs/test_named_from_filter_group_by/foo.sql,sha256=LfuciAz6SeBDwt0uVOEmxT1QBSEwCGh50_23npzc3dQ,134
ibis/backends/impala/tests/snapshots/test_exprs/test_nunique_where/out.sql,sha256=lB3tZnenMyS4whyPXwEABJIW8HVjxO9i15aVgV9Brio,132
ibis/backends/impala/tests/snapshots/test_exprs/test_where_with_timestamp/out.sql,sha256=nrDWEbqQfr5JLi4hllnLYvlOmMmgJBJzJQWWAMGvk5A,111
ibis/backends/impala/tests/snapshots/test_in_not_in/test_field_in_literals/isin/out.sql,sha256=9k6Wq_7t-zU6tXvEL5GCLlBwp4KGfNqVbrQf6qDI0GU,106
ibis/backends/impala/tests/snapshots/test_in_not_in/test_field_in_literals/notin/out.sql,sha256=rNcBIyLl8wHNHJNzBgCBFtvdI7f2KSbKKuQOkhZY4mA,125
ibis/backends/impala/tests/snapshots/test_in_not_in/test_isin_notin_in_select/isin/out.sql,sha256=b45OBP7hxb11h6zde9J_6g3vOdYapmfPKhEaV-xEJ2s,69
ibis/backends/impala/tests/snapshots/test_in_not_in/test_isin_notin_in_select/notin/out.sql,sha256=hJ7_PmyEWuXb1Qrqldi0JHEQmt8jOItuBcjQ1XMJwL8,83
ibis/backends/impala/tests/snapshots/test_in_not_in/test_literal_in_fields/isin/out.sql,sha256=XC7zfMBkVsjL6UA6bLtXjvlvpa4wf14qmt5ZEKkBXr0,96
ibis/backends/impala/tests/snapshots/test_in_not_in/test_literal_in_fields/notin/out.sql,sha256=mPg8GFDJyDHrytt4ycFUJwGXWqpQVokXEr0d_IbIit0,115
ibis/backends/impala/tests/snapshots/test_sql/test_group_by_with_window_preserves_range/out.sql,sha256=TZ0PO_msfa5FYcS0m7eG5WUqDEMz9S6sZR9SiOKcHSM,199
ibis/backends/impala/tests/snapshots/test_sql/test_is_parens/isnull/out.sql,sha256=YmLGQ5xGcTDS7NkWwDGQoXhRiOzHOijuu5YmQEFlWi8,95
ibis/backends/impala/tests/snapshots/test_sql/test_is_parens/notnull/out.sql,sha256=keaPUGp-pupGCxiewVKgbMJ2QW6ZVip1s4vxsJbLN8s,103
ibis/backends/impala/tests/snapshots/test_sql/test_is_parens_identical_to/out.sql,sha256=OxSYQRxSyLwnvi6aaT0vZ0ws39u1mgPRxzMKb8Tvj04,111
ibis/backends/impala/tests/snapshots/test_sql/test_join_aliasing/out.sql,sha256=L1W7MMG58QFfz82PSaPr1Qk0JJQqv7bjOYOW6bx6SYk,1083
ibis/backends/impala/tests/snapshots/test_sql/test_join_key_name/out.sql,sha256=z6cEA5cOBMrhJAidUpSVPUgdAdapC_7tqJGX6DRhHy8,1154
ibis/backends/impala/tests/snapshots/test_sql/test_join_key_name2/out.sql,sha256=oSn4NDZkmqNBeahBOYyrcAnvvpgxRdgUiKtuwxNZVNg,785
ibis/backends/impala/tests/snapshots/test_sql/test_join_no_predicates_for_impala/cross_join/out.sql,sha256=0_rdyfz1G0lT_sd7t56R55nWM_dMu_lfoPPNQT1xqtU,111
ibis/backends/impala/tests/snapshots/test_sql/test_join_no_predicates_for_impala/inner_join/out.sql,sha256=E_ieiR6OBBDSagAdEERjkUqRviq9SXgZTGm5jEKAjPU,121
ibis/backends/impala/tests/snapshots/test_sql/test_join_no_predicates_for_impala/left_join/out.sql,sha256=ApMoeYsSVW2UuXVWqF87spUvdWTGethSZC19lKB41DU,126
ibis/backends/impala/tests/snapshots/test_sql/test_join_no_predicates_for_impala/outer_join/out.sql,sha256=wa8RUEDG1VUrF8AeNt-vfewYTvXQrYpC016VYKuJ5PA,126
ibis/backends/impala/tests/snapshots/test_sql/test_join_with_nested_or_condition/out.sql,sha256=Leg9xvfUSEBHCmXCFzHbbRCGZkwL5j9bNNcfn947KVs,183
ibis/backends/impala/tests/snapshots/test_sql/test_join_with_nested_xor_condition/out.sql,sha256=KHtuVOBJd54qQKH-VqAeCS5Dg-AHrHzRQavzL_eYS5M,241
ibis/backends/impala/tests/snapshots/test_sql/test_limit_cte_extract/out.sql,sha256=9tUZD8lr9ihN746jxaXBFDJUVysWiZRgI5jPpDHNIkY,397
ibis/backends/impala/tests/snapshots/test_sql/test_logically_negate_complex_boolean_expr/out.sql,sha256=OMRrHFfufiPUaIO1xb9B7kztAaUhYhHrGJhwS6rUGvg,107
ibis/backends/impala/tests/snapshots/test_sql/test_multiple_filters/out.sql,sha256=ptXkd6XCTgGaZIHDNECjcn2klk3Sg_XyxScXo0VUaiE,224
ibis/backends/impala/tests/snapshots/test_sql/test_multiple_filters2/out.sql,sha256=6tZTVpdKm7lUjVM8eNwoMp-xR-nAw4FUHFX_76m-hMo,245
ibis/backends/impala/tests/snapshots/test_sql/test_nested_join_base/out.sql,sha256=l_gkdjWLXVDFIdthJ5ZB9AiVwk5O1BwQDtavSt9vvTE,337
ibis/backends/impala/tests/snapshots/test_sql/test_nested_join_multiple_ctes/out.sql,sha256=0cUbtYAWVabXq0TnvxDsrA8i-JGUNog_jii-qfjwXU4,742
ibis/backends/impala/tests/snapshots/test_sql/test_nested_joins_single_cte/out.sql,sha256=lyrPAC7g9L3wVjLayoP6OWIjPiTzw63TNQuF5XLkNgA,557
ibis/backends/impala/tests/snapshots/test_string_builtins/test_find/out.sql,sha256=y0WjtKd9Bix2--W5gMXjPIL6KvgOKyDLbkM-AGomooU,148
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/ascii_str/out.sql,sha256=JtSR7G_2nFe4krLb_7rCnO0ck8f0C9tMnBNoaqLxP1s,97
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/capitalize/out.sql,sha256=tSbI1gPVe3x0xnC-l145SrI24K6vWI5FcqzDSHptYg4,406
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/extract_host/out.sql,sha256=xinxDQTS0z6bYmLxzP14tzb84vCNfFCOWnZBKV28zqM,109
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/find/out.sql,sha256=i753h8xPcUeIcCnPSfFf-kryJ8Z0mt0X24axFxgJy0s,111
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/find_in_set_multiple/out.sql,sha256=Jkg0lUfQT-SYzBgL66yZ8_EBI9A7YGATGblhOIuY46c,143
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/find_in_set_single/out.sql,sha256=Gf0kr94nQSHE8wgvWAnWxBXFor4JykMpdRTIsrDD4C8,134
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/find_with_offset/out.sql,sha256=E2KxMMYz2h1ya4uqsNahqPeu7L_sPABfRhmRCqzcLWQ,121
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/length/out.sql,sha256=eZVT15B7vdOGtAfQfWxzD1wF9U1jm1IpZrkw3M7KzlA,99
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/like/out.sql,sha256=CrVc4HobupD8Mk5gLAGjHFreU8a81Rn8OJ0m4CzEGVk,112
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/like_multiple/out.sql,sha256=_08eQJjupdWVi8YYsADr-SRmToK3L7XASKE_IvQqJvk,184
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/lower/out.sql,sha256=x32yWi7AXHPFCEdBdVVvhkDysPZfXIgTXx9OEuA7PzA,95
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/lpad_char/out.sql,sha256=3r86Au0h--p-JEzJ53XAOa7_rY4PvusJ9qec-7DObj0,142
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/lpad_default/out.sql,sha256=8I5jzdTgx_OMvfwa10pQlslyY6WTOu26NB5clQzj5yM,144
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/lstrip/out.sql,sha256=i15D3q49HO2jR3I61zc1ArWCmReiIRQdaGXW3Tcpecc,107
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/re_extract/out.sql,sha256=YIm6PBhs5rV_5ILV0QocAPIUYfq0KRm2NYm_MRdXGOY,133
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/re_replace/out.sql,sha256=Avb4L9bnNdec_uY7qy49eKgiwvg8atnR_Dn0GeVUEYI,141
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/re_search/out.sql,sha256=bITJPT-whLumRxvXltXI7IliYs0PF5UYL0VQo-OtbJU,115
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/repeat/out.sql,sha256=b7PesibSRa1x-QdrhzRUDlvhcGg73xt8YAWK9q-Q5sc,99
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/reverse/out.sql,sha256=Rj6mEgQsYwZFUKlfHu2BNC_HwewSv7fN8BLASUfGSvw,95
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/rlike/out.sql,sha256=bITJPT-whLumRxvXltXI7IliYs0PF5UYL0VQo-OtbJU,115
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/rpad_char/out.sql,sha256=3XniqcCF0pJnKOat4KK6JaUD70QKt5e7tQOZ9prBGjQ,142
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/rpad_default/out.sql,sha256=Kcn_XKpGr6ZSSBl0QaLTHiaUY_gswTRCjPKAmya5tmA,144
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/rstrip/out.sql,sha256=OMmQcZ3fOd5p6dq5bqyO4K_FVRdksgmCPfdwxfiHLJQ,107
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/strip/out.sql,sha256=F9PTB9huKcIIOBw5I7LTjoTCMicu0UP0x_R8hVWHwfo,128
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/strright/out.sql,sha256=Ka05B-X-lNYtOYmSBXHT5hNZ1Lo-mKR3CrvsNmcZEFE,146
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/substr_0_3/out.sql,sha256=fyVSKhC_5OTr6Pne2Yr6elBYZvNl-hffvMeS-8LVr-E,176
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/substr_2/out.sql,sha256=wm2iDkJCQLF-HRdTKBzAwZxkYUNlE3050gZuo6Orddk,170
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/translate/out.sql,sha256=osuP0_KJ1gDZJjJCu_jgfqpIO-YDSx9OVwt4xu57T0w,119
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_builtins/upper/out.sql,sha256=qGoKnHSQegoT4cCGn-fiDwrDzm9vExC_UaXIiXdc6Ek,95
ibis/backends/impala/tests/snapshots/test_string_builtins/test_string_join/out.sql,sha256=Ocjea99dhO5adXdghQN8l0z1LxoakXqrKwhxOvD6B4U,66
ibis/backends/impala/tests/snapshots/test_udf/test_create_uda/False/out.sql,sha256=FulelUdZOOF1f3kSxYmwziK6re9ENwag80psoMixW3U,171
ibis/backends/impala/tests/snapshots/test_udf/test_create_uda/True/out.sql,sha256=wm5pMMi3rloycRuNUSquS4Tai1NZ6HJ1-DT7zZbiMGo,196
ibis/backends/impala/tests/snapshots/test_udf/test_create_udf/out.sql,sha256=YMZg24szyhEFe73wMxRVm8ansKq3GhI26OyNIWLIJgM,99
ibis/backends/impala/tests/snapshots/test_udf/test_create_udf_type_conversions/out.sql,sha256=Tf7Y4QWn88gGRkct-SUIDZFfhMHAPB1Um4ovWJESwsU,115
ibis/backends/impala/tests/snapshots/test_udf/test_delete_udf_aggregate/out.sql,sha256=YEEUYSiLtte5SpI72FETCZ9CwD805pDdTj6JzqeGfp0,51
ibis/backends/impala/tests/snapshots/test_udf/test_delete_udf_db/out.sql,sha256=N3tcXT_kHJfqPIOBdAI63H6UY0HfNzCY1O9Wg9Ofg2c,48
ibis/backends/impala/tests/snapshots/test_udf/test_delete_udf_if_exists/out.sql,sha256=YnvO2sceHtg-0jK_-HRKcMFe8oWDeEPBkOk5DI4hLSY,51
ibis/backends/impala/tests/snapshots/test_udf/test_delete_udf_simple/out.sql,sha256=jVijJcr16fQz_D32k0qlh5tLVPX8peyS-OxjgMVUprQ,41
ibis/backends/impala/tests/snapshots/test_udf/test_list_udafs/out.sql,sha256=JQlVwxj-00_yG6kwiqIeraA632AaVwPZjOXkPR3IO_0,32
ibis/backends/impala/tests/snapshots/test_udf/test_list_udafs_like/out.sql,sha256=2pNHsJ164azIbrJEVwer7HHfwJ8kv8kc3r-wWcB9EaE,48
ibis/backends/impala/tests/snapshots/test_udf/test_list_udf/out.sql,sha256=wI_QKN2rUYNygO1c52RF7ltqOrGII8Qk1Ynnl4NUpwo,22
ibis/backends/impala/tests/snapshots/test_udf/test_list_udfs_like/out.sql,sha256=ZQdIV_oHJusBqooth9Z0cZHXnRwA7vXMTBCBGvYm8_s,38
ibis/backends/impala/tests/snapshots/test_udf/test_sql_generation/out.sql,sha256=pg3tOnwUPGm2ckpkiZB1D8rVz1I5k7b1LcvB5h-zx6A,73
ibis/backends/impala/tests/snapshots/test_udf/test_sql_generation_from_infoclass/out.sql,sha256=kshUiY882_tmSwGQs8hcKRxSZxUrUexPc01lcCnYgak,52
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_hash/out.sql,sha256=xJFlAggpedPbW48slvmF4N1z1h4rkVtnpoSpE2TF6M0,87
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/log_with_base/out.sql,sha256=T2FCXKFKeVdSPAX1RO16envt35ejiqV3uRkyqNmuI0I,93
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/round_expr/out.sql,sha256=5VP7HCNAtc-uYYlT0_X7nAsCIBNXp_awTgOQ3Cwu3gk,124
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/round_no_args/out.sql,sha256=3tktdQqQ57RMfvkfbsrPsNmRjvTHT-C2ka9wkQG6s2I,113
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/round_two/out.sql,sha256=SAEaFPAoaYa3zSBLMsz0t1y8t0-Qc95C1hI3faC4t24,97
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/round_zero/out.sql,sha256=3tktdQqQ57RMfvkfbsrPsNmRjvTHT-C2ka9wkQG6s2I,113
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/sign_double/out.sql,sha256=x9ZqhQ1gXXL6mTXPE10ZnxhBWpTeUn-lrfV9giz3sWE,105
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/sign_float/out.sql,sha256=Ee4jH8L5pKydKeSRN_keF4IJF5ORgdGhlTXNJ_x-YOI,87
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric/sign_tinyint/out.sql,sha256=3k6vfMUdJK_qQ5I8wwlUXqN6gYuW3DzQ8ZB75MtfC0c,108
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-abs/out.sql,sha256=ngoJ73hjg1ZMtGhlEDmGPhsexO6HnOd2fMXkKGmMFXM,87
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-approx_median/out.sql,sha256=iGd4R7TfKJ8A07oSu29eKddYlIwDA-PnGHxFRDvfwGU,104
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-approx_nunique/out.sql,sha256=t6GyxCVzbad_0Cvyg7geTcH_FTQMvQnycZP_AiPX0Gg,103
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-ceil/out.sql,sha256=-67wzFNhXM9c1WZI2vinBJUxD-HDMeI-frqM6HtxmNY,105
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-exp/out.sql,sha256=bfRhbuZIq_D3MzuKD8iuUySW8rJivlt6dxh48CNdlhQ,87
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-floor/out.sql,sha256=R0BCK3uJwcvHB_rZHN2FZqUfh9S6_bg245JR5MG0Okw,107
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-ln/out.sql,sha256=kJeM9yzf-AqK16tcfxeA_iyXzNJUxvL7gwrpjzoQQh0,85
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-log/out.sql,sha256=Qe1fqoyw0icMuHdgvaIyEQ5CHiG4xaBTa2tYsb_4KWU,86
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-log10/out.sql,sha256=DsJksq6O9pyGVo8HnNGX-JJo6FOKSmNiQnmWLXr9KWs,91
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-log2/out.sql,sha256=W_GrgPfTdF1b5b7clVsJjHBqU0feEA1AAO3wcE_2omo,89
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-nullif_zero/out.sql,sha256=GbnaMEwaza8jWo0zH2zlBovCWjrRKzTNHAL8Ki-EUpo,99
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-sqrt/out.sql,sha256=CsqxIkr6WjDyX4J1noJ7Yukc2nR_SljutrIlPZ07FAo,89
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/double_col-zero_ifnull/out.sql,sha256=16uBVHpdKTRA1UTK6K0EgaJX5rIPJtGEgXosGKL1gCs,105
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-abs/out.sql,sha256=hzqTzvLbWhTwDf7UsHDxFZtUJdfe3SfnRyrlBh3mGz4,81
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-approx_median/out.sql,sha256=2rlre3khfMsOK6tt81TBAuCvXJ_HPkbWAYN3E-cg6lE,98
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-approx_nunique/out.sql,sha256=Un_hpwqdPAc52vx7GJKjSuUftiRqSyv0i7Sr7CDanrk,97
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-ceil/out.sql,sha256=I1MBYL3dEgXIT0oD2PtnFl1FWHnlAIYcB4oTI7pCatc,99
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-exp/out.sql,sha256=aD4mvYFmJ_HQvcv0PjLb0Ay5L8MGIwvHY7sfBRtVJPk,81
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-floor/out.sql,sha256=MoKsnhh8n2rpTsf8Gf2hCrurjmtcOQxTLXs9dSkCyfE,101
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-ln/out.sql,sha256=FHqfSzvpoPUQm-hL8kbe4FZYy04CVTPrk71GcaGWue8,79
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-log/out.sql,sha256=BLOowpmdyNp4W2E07DfGI_Isol-GEuTUcdyeddn2Qag,80
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-log10/out.sql,sha256=4mAZUxeqaqP_wlAmDbvPSu1b760xWc_PKsJKXQ5KBGU,85
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-log2/out.sql,sha256=wB74Rn4TIBa1oAtUoiHfq4HPOx5G3cNocq-VyEaeDbY,83
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-nullif_zero/out.sql,sha256=kDJzLbtGXb1sfdmn2wNnNhd47xe6Lgr3mTpUFc1pnCw,93
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-sqrt/out.sql,sha256=14_NqFX8YfPHpfnUuVAPdGClpxeOBdQbtIF7nFKZzsc,83
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_numeric_unary_builtins/int_col-zero_ifnull/out.sql,sha256=EFLzmQcl7q3lX3xcONyu_Lfzl9pxwbJYoMemBu1TX1I,99
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/avg/out.sql,sha256=kB_joodUgVsa4_iNSgZoscdZyAWgBqJXH_yww2hrPAY,144
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/count/out.sql,sha256=Ge1suQb6Ik7G5lAqZyTHc1SZ4BPJM29KvD43jEHxmBA,147
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/max/out.sql,sha256=jIc-J0gJGYPkg7Q2GBOdNBq5zWy8PDdOwQa1kpUpwUc,143
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/min/out.sql,sha256=Jk_27_RQhFZ0s35WXmpX-ms4UjrB_8WBSR47i3J1uVY,143
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/stddev_pop/out.sql,sha256=pelyC8N_6m9um7H9sYNXfPhWCTbs1VVJ3tAzx2ujYV0,158
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/stddev_samp/out.sql,sha256=4nJV1LfI6jSoX6067oasgLSbfulPFtDoLQbvBlwL9V8,159
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/sum/out.sql,sha256=_qnIoZU48YdcvQYgmc4IONTiOVaRPtkXZlfVy8LwyW4,143
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/var_pop/out.sql,sha256=ZzBNKgONURA7O-g_MUQS0SDrclMd1jMTAVMtbN7kitY,157
ibis/backends/impala/tests/snapshots/test_unary_builtins/test_reduction_where/var_samp/out.sql,sha256=CbM_ErN87qOQjBLrd9GSRc3XG8JqvW5c504sQY8uZRk,153
ibis/backends/impala/tests/snapshots/test_value_exprs/test_any_all/all/out.sql,sha256=vYgHxW31dM12KYB0G9u8Taxy4iV1PVI8Rs3T4PZzh2U,73
ibis/backends/impala/tests/snapshots/test_value_exprs/test_any_all/any/out.sql,sha256=McQ_US4N6mMkigPkaR1XNWqsc9NoWR5CUJdAYbMWDTA,73
ibis/backends/impala/tests/snapshots/test_value_exprs/test_any_all/not_all/out.sql,sha256=VTY83zYiw8A5bOANY8rzDjyGg-DWkisxqqKy1vPHktc,92
ibis/backends/impala/tests/snapshots/test_value_exprs/test_any_all/not_any/out.sql,sha256=xpXJ-5bIBE_zZIASsP7me50yQAozy1IUz6hQwqQ0zb8,92
ibis/backends/impala/tests/snapshots/test_value_exprs/test_between/out.sql,sha256=MutOetRX99vtBu2O94QoH07wtzfKCcyr4QkQPIG_YSQ,79
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/add/out.sql,sha256=jmtvPV7X2SfhytKWtKkon0rtElu3MlFOpUPwuVXgpl0,67
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/and/out.sql,sha256=LHD11lueU119X_SjlD_3YPtRInkaZ78Ms3nYcEwsfcQ,95
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/div/out.sql,sha256=GJOxU5VHmhJ7ASuWyuciErgIUHrJibTZU8dMJDlMWQE,70
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/eq/out.sql,sha256=RqAng0vAYxY-wldixQFLunGpyrxo0oPKJ6CnbeGAPoM,70
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/ge/out.sql,sha256=UV7m7TkiNhJ6eE9eeXxPaQi_FduWc5vfFlFKAqcWNtk,77
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/gt/out.sql,sha256=7nehYMXhTKdj0JlRHXKKUvD6sHCt7NP1ajWa5aABXLQ,71
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/le/out.sql,sha256=U6dgU6Z9sHv2s-yt4pcnJRq2Exzc6ODIVrSk1toeoKQ,74
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/lt/out.sql,sha256=gKk3F9XBTmZ7XUTcEMOZFuekN7PQelnCYmAOgo8tCQI,68
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/mul/out.sql,sha256=od7jRahRdDXmuCH0yrjK_GugImO7YT-U_VW7VT5a3Fg,72
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/ne/out.sql,sha256=RG9z0iy5pG4FXWChL82ndvmgcCoiqo-XDrw5BGqQ_z4,74
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/or/out.sql,sha256=sXE6XUaKpXOxXwLwJmwpzrjGSEmZh6r_yXpngPKXU1w,93
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/pow/out.sql,sha256=cXFNczZ4huN65yavGhE1QAYV3vt-3H8KVhYI9ZRUm0I,75
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/sub/out.sql,sha256=6Tbh472xxCskrDZvvZ1RaxbA_kjw1KnSiNto0qpxjrk,72
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_operators/xor/out.sql,sha256=uttZ5TURebOkCdvCQCXeZLmo6CEZK1UWTNd0WtTa68M,138
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_parenthesization/function_call/out.sql,sha256=NtbnQ0UfUIszdZ32gDWf-XSZCKMlfJu7pbjK8JWq7ZU,76
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_parenthesization/negation/out.sql,sha256=M7Kzl5Guw8uBzfRzB7QcDz0BZ49qBzU6uuXaGS6xq1o,105
ibis/backends/impala/tests/snapshots/test_value_exprs/test_binary_infix_parenthesization/parens_left/out.sql,sha256=-0h-SJ_Bi0LVws7FOwStMvIsqdu5Niw0FbrI0lKLp5M,86
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/a-int16/out.sql,sha256=agkUjgUcyV8w8NXYlYTsxrqX6y49yyw15CYb0fVoyEE,79
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/a-int32/out.sql,sha256=ZoCtpQZJo6pbfxn43YJ5ZIm63S3zVgCHPJWFSlGNEOk,74
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/a-int64/out.sql,sha256=0fd12QSpeKihQ6v8gb1YzdbixY9nDK_YRqnCAp9JCg4,77
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/a-string/out.sql,sha256=XMClNNel8lsh6BpuRJKYY-Ho95hEO1MwF-bQbzs7f3E,78
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/d-int8/out.sql,sha256=xWsloKaf28FzMVPCZ5Qigc7p0u9_A5shv5G0Q7btgtQ,77
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/g-double/out.sql,sha256=zUPGDM4HdzaZKbBRIPunvSmroZsi7LtfGmYeQHJ-5JI,79
ibis/backends/impala/tests/snapshots/test_value_exprs/test_casts/g-timestamp/out.sql,sha256=n4Nke1bJ0noLk470QjAPWWirXDLTFjOW4TPJnJ-N2iM,84
ibis/backends/impala/tests/snapshots/test_value_exprs/test_correlated_predicate_subquery/out1.sql,sha256=xurlcVDVefglxMSWB_AtNSMf-Nu0NhASuIckYM2BSOk,62
ibis/backends/impala/tests/snapshots/test_value_exprs/test_correlated_predicate_subquery/out2.sql,sha256=RDqi9RPSfL_HsGOhNjSwtiH5d9puRhG5FOV1TFJAoU4,62
ibis/backends/impala/tests/snapshots/test_value_exprs/test_decimal_casts/column/out.sql,sha256=VlUhkd0CUzaA-mppr13Cm-gbwIg-8IkjI2MTvtgEKaw,94
ibis/backends/impala/tests/snapshots/test_value_exprs/test_decimal_casts/literal/out.sql,sha256=-TULx2Z2KO5U4fIZSYw7jFxXV7ycqGR98hZof6GPNjA,83
ibis/backends/impala/tests/snapshots/test_value_exprs/test_isnull_notnull/compound_isnull/out.sql,sha256=Orl_VIuxF-vnpMPWcvBB0a5Zp591klxTK1RtMqo5FTg,93
ibis/backends/impala/tests/snapshots/test_value_exprs/test_isnull_notnull/isnull/out.sql,sha256=8-6Pu6bWZiV3adPrgm8VkaKeZ66qggVGIcJCtbfDllc,64
ibis/backends/impala/tests/snapshots/test_value_exprs/test_isnull_notnull/notnull/out.sql,sha256=BcrreGCYXHIL7IaGGEvaeu5rV4mW--nmNn2xIEVJ764,69
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/embedded_double_quote/out.sql,sha256=YzgtUE6GUMvjDmm6OsXSYH45g9O_KJesNe_Hh14ilaY,41
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/embedded_single_quote/out.sql,sha256=kWun3Jzcd-hYxyj3p93rgEGjKwizhcEJDRjPhKxHYgw,34
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/false/out.sql,sha256=NOwVsAQB2tYcQemK0O5BUM8MJtRoZC32jGJfTUNDC34,25
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/float/out.sql,sha256=-B86XhZqBlqFc-Gtr4d-S_q0ooVyaRMx_L2Qs6PfhSQ,21
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/int/out.sql,sha256=woWr2XMxGQupnUihbfq_il-nCQfi9h_cl1o0vc1mu94,17
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/simple/out.sql,sha256=iMoJW4-lRQETI0ooEbbMka5bABCCkr4z3NsTd9C0tQM,31
ibis/backends/impala/tests/snapshots/test_value_exprs/test_literals/true/out.sql,sha256=ufLeiZ831xFcEQBfaRLl3B1xf4ke7zuKkDn_iuIi3og,23
ibis/backends/impala/tests/snapshots/test_value_exprs/test_misc_conditionals/out.sql,sha256=whBBKwbrYurayrMf5Ceyh0dHtvCKTdOQ5Vj5N3iSi_Q,70
ibis/backends/impala/tests/snapshots/test_value_exprs/test_named_expressions/cast/out.sql,sha256=ultHAnrv4Jw9L7RE9Lqmghx6QLYiRKBxGsRTgcakBq0,68
ibis/backends/impala/tests/snapshots/test_value_exprs/test_named_expressions/compound_expr/out.sql,sha256=-BO4hkoHZWz7D1sMYSSZw9qVao07Q4sehd64QemVDeE,83
ibis/backends/impala/tests/snapshots/test_value_exprs/test_named_expressions/spaces/out.sql,sha256=ahJ2MWyXnDOwP2n2_Y17O5FfX-mQylH3xgF6rHaivfQ,58
ibis/backends/impala/tests/snapshots/test_value_exprs/test_negate/a/out.sql,sha256=e1UINOS7bOLuN-8QlaiZy0wtsOaKkun7aW2kR8_27cQ,67
ibis/backends/impala/tests/snapshots/test_value_exprs/test_negate/f/out.sql,sha256=7NoHPDioTRjWmME-MxHH6MvrRktM4bX5_jXXIshiggU,67
ibis/backends/impala/tests/snapshots/test_value_exprs/test_sql_extract/out.sql,sha256=7j6I-qy0yXcttZJbnCcdYx6LAqsmP-XjYApWL4PNTA8,152
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_day_of_week/full_name/out.sql,sha256=jJ11VEPPtXbJYlgZmhGqwoVWe621sZAvMT8voyb5D7o,99
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_day_of_week/index/out.sql,sha256=lG_achjAU4gL7yEucbg4MwtlrmjdDWA7_B9hjSR42k8,115
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/days/out1.sql,sha256=RBmTbScrjTdjNXYSY_TgDPNoDhJvsh4HQYvsBz9Lqb4,121
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/days/out2.sql,sha256=qpQQj8oXiNuqjbNPsJW4MwO0d7KmgUBsq6LE_2-_oc8,83
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/hours/out1.sql,sha256=7AkF8ZbPPxxbWuYykQ774wcO7WaEHTT48WMl2xHYKbc,122
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/hours/out2.sql,sha256=v2xrHRgqiOKdDjwt0j82l_x-6EMKUGVb0lcMwIxNc3s,84
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/minutes/out1.sql,sha256=1_Y35ijXJcoGojJ2OU1HcIEfJgsUdSx68mVonCgjJiQ,124
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/minutes/out2.sql,sha256=N3dlqcUm3fWJaMgDRb0YlBtbmJGbVUVeQNyvQDxc-A4,86
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/months/out1.sql,sha256=gBGzRfbKPvHDaRgyq7ESQXTllJdD7uTF0yRmJ0BarfE,123
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/months/out2.sql,sha256=ZeFff-RMijgWHMguUXhgP46TpyHIGzw6VdlfmY6VvPQ,85
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/seconds/out1.sql,sha256=rIHYRh--4-D5EAzsox2x0q61Lx0pfqCICOq3tGDw-JA,124
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/seconds/out2.sql,sha256=9fatGtYx_Ivy7HRtYRGMk2dhTJGQs4lUx3omecDzi7w,86
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/weeks/out1.sql,sha256=G-eqjzqCtPH9UI2c3hGjsn_LkO8VDVJMqtfs5YDXhAQ,122
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/weeks/out2.sql,sha256=hxvnwZl3kHT0BH7Qj1x9D8zaCve1b2ak8dBhIPG1Cno,84
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/years/out1.sql,sha256=h5y6CMQDwvn7UBzEfRrh9E2ZDNrgJoRd461xyXK_2bw,122
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_deltas/years/out2.sql,sha256=r5PH_wITJDC0IndqBb7l-YgxbmL5Fom14NpeJg6vxK0,84
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/day/out.sql,sha256=hCEu6b5QeovRKZH7E6pqtIx6zWO5pICg5eQFx6aqMww,78
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/hour/out.sql,sha256=f8dUnq738L91xu5nsEDSHSed7fNhaGuu96dXPugsTYs,80
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/microsecond/out.sql,sha256=-Cd7_Lbtv1BQDpVj_IaT_Ay8wo1q-bFltRtMC16Vy-I,104
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/millisecond/out.sql,sha256=2i2TFaTpjl-WhfHEhMNeD-cTfhxgbXqrJZ_vcJrXv-A,101
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/minute/out.sql,sha256=wK8WvulZ_mkxE5nBAN6bhcSF-2l446VVNXqjfRfPmcU,84
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/month/out.sql,sha256=6r6Oqnla-mpOj7mq6PkSUdghU9myCUrV4n6wgdPwKW4,82
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/second/out.sql,sha256=9WO26Cw3e5VYgmINUKJMtU1bkxMlrI43Eg3evuIF5Yo,84
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_extract_field/year/out.sql,sha256=-2jmxIWKk-anCLg0ZbQ7QT5xtJjgqJW84BuqOuDjdHY,80
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_from_integer/default/out.sql,sha256=PZP-eEiGfZf-qWYZ2TbFcbWHzPOpjePCauk8-69dnpM,145
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_from_integer/ms/out.sql,sha256=t65dASdzgpUDVX7U8AZpC7T51MPzbkpE7-jUG2lTq4A,159
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_from_integer/us/out.sql,sha256=qKLrWBlNp9dlmkiqqDJ9SZ99tP_E1zb7pjUBOMyKbYg,162
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_literals/pd_timestamp/out.sql,sha256=Fn-62ttDNaDiOM168eWzU5F9eyW94QqmUW3NP0cH0pE,77
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_literals/pydatetime/out.sql,sha256=Fn-62ttDNaDiOM168eWzU5F9eyW94QqmUW3NP0cH0pE,77
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_literals/timestamp_function/out.sql,sha256=Fn-62ttDNaDiOM168eWzU5F9eyW94QqmUW3NP0cH0pE,77
ibis/backends/impala/tests/snapshots/test_value_exprs/test_timestamp_now/out.sql,sha256=GbKfdmPkJCDVgb2wmgu-X-RxSJeoO5WbHDwbf4bu-Ao,48
ibis/backends/impala/tests/snapshots/test_window/test_add_default_order_by/out.sql,sha256=PUGgdvNEmNryuVIS_4okrLAlKohAkW11mOTd2ct4wQk,712
ibis/backends/impala/tests/snapshots/test_window/test_aggregate_in_projection/out.sql,sha256=hO3Y58L_S8PnPMIGmFclcp3M-q1NTGx_eJMBcV4vJj4,285
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/max/out1.sql,sha256=ESuFSLKb_v0gqJf5qA50GsymPNu1E61GGvrQlmoGk9E,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/max/out2.sql,sha256=ESuFSLKb_v0gqJf5qA50GsymPNu1E61GGvrQlmoGk9E,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/mean/out1.sql,sha256=m1Nl2k6Xb-wwNZwZlg2WmG9rwfem5VnzYWIoZI_Mg20,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/mean/out2.sql,sha256=m1Nl2k6Xb-wwNZwZlg2WmG9rwfem5VnzYWIoZI_Mg20,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/min/out1.sql,sha256=u910x6emlxnpPIGxE28pWDGi6b-KNedUabaEtz4qGWo,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/min/out2.sql,sha256=u910x6emlxnpPIGxE28pWDGi6b-KNedUabaEtz4qGWo,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/sum/out1.sql,sha256=l-WyuBnHn7s0aF8fpapiPXbCZY4vOUE8i2yPCYARf4Y,133
ibis/backends/impala/tests/snapshots/test_window/test_cumulative_functions/sum/out2.sql,sha256=l-WyuBnHn7s0aF8fpapiPXbCZY4vOUE8i2yPCYARf4Y,133
ibis/backends/impala/tests/snapshots/test_window/test_multiple_windows/out.sql,sha256=lgPKyukWniwpraWPbJysyIdoqxNhwz6HXaJusz91Png,288
ibis/backends/impala/tests/snapshots/test_window/test_nested_analytic_function/out.sql,sha256=UHKqGe8uQv_kWQXdg9LILja389gEeabI5ltxetPswGA,129
ibis/backends/impala/tests/snapshots/test_window/test_order_by_desc/out1.sql,sha256=kD-JCksN_ocWjAH-fBXosssPpbJJvTkCxiMxEvFb0WU,115
ibis/backends/impala/tests/snapshots/test_window/test_order_by_desc/out2.sql,sha256=GQ19lbNQgVG73wXEvsCgNfxNk1flUNDGEWECgNnt1Ww,283
ibis/backends/impala/tests/snapshots/test_window/test_propagate_nested_windows/out.sql,sha256=TtzOfYhpSvkQXuKd8dXXNi5OhqxqTwrMtnJiBG_edqg,173
ibis/backends/impala/tests/snapshots/test_window/test_rank_functions/out.sql,sha256=FwuQfLtKh9RBPF1C3nYxRIk7zZytYI_IAqlMMq9YzhE,155
ibis/backends/impala/tests/snapshots/test_window/test_row_number_does_not_require_order_by/out1.sql,sha256=cgeKHmQsEAP08oVZZpbujBBL2vRoDz3-Jhq89tTibPs,233
ibis/backends/impala/tests/snapshots/test_window/test_row_number_does_not_require_order_by/out2.sql,sha256=KhzhyA1DgSeSVyuh8D_KfS9AghE_QvZQwPy_uLt3Xh0,241
ibis/backends/impala/tests/snapshots/test_window/test_row_number_properly_composes_with_arithmetic/out.sql,sha256=C7Yjrp670ah8BJqup8MILk6n44fVjU3TxIUTKHTGGfw,233
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/cumulative/out.sql,sha256=pBHH1drBYb0O-88B6VXDPCF6csqG0YRThC-POGDcAFs,133
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/foll_0/out.sql,sha256=pBHH1drBYb0O-88B6VXDPCF6csqG0YRThC-POGDcAFs,133
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/foll_10_5/out.sql,sha256=t9rdMa8StvZ3fIFsJnuEVXIIiz7G5C0vCiUUPsWhJpI,126
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/foll_2/out.sql,sha256=gjk5-KsQRZQYXiyTXflkLoRF6kc6hYT660I7EJ1lV50,133
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/foll_2_prec_0/out.sql,sha256=IGIh2AE3US0eixZK9X3avWpu98gLlB_IFzX0d9AzrNc,125
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/foll_5_10/out.sql,sha256=yTMXaVDc3PZyNUFD_-sb_rmOYMRmg76bTpRfmSXpPYg,126
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/prec_0/out.sql,sha256=w5GN7U_-PN__8IS5_PAy00mVl7-s1tJ_wUeEL1dUFu0,133
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/prec_5/out.sql,sha256=TLFmcWcp4pNF24MJUW7vCM7Xv6dfaqvuEE3TR9nWo94,133
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/prec_5_foll_0/out.sql,sha256=5yKE1bRp1t2qb0Oo1CUUG9S_PZojgl8pADWv0cuFXkA,125
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/prec_5_foll_2/out.sql,sha256=FHg7V3JW1zeGrkwgkHFcA7UjGUC1sDxIqKMwlefIwfY,125
ibis/backends/impala/tests/snapshots/test_window/test_window_frame_specs/trailing_10/out.sql,sha256=BXiwWq8n6NZspbkA2L9JJmYZZYhwS5AUhMdx2JUQ3PQ,126
ibis/backends/impala/tests/test_analytic_functions.py,sha256=Bs2GZGeb9zq_suO_PFUsiq0FD7Y-gw_THi-Jxc78IGY,1215
ibis/backends/impala/tests/test_bucket_histogram.py,sha256=3T_PKAIVMGieMUuGOlt_RKIlpsi3uIUjQXKIKO-S6AE,2880
ibis/backends/impala/tests/test_case_exprs.py,sha256=Ej6T5vcd4b9Mi-l1KyIOkQ9dfxp2l-NE5v3ilcUZ-9k,2862
ibis/backends/impala/tests/test_client.py,sha256=_nkf8FFQT7JJM0s0McxNTcVPo1KH0OEqQYqPhW2rsa0,7047
ibis/backends/impala/tests/test_coalesce_greater_least.py,sha256=0KcG78Zb7z4X07mCjGIdXX2gnFTpsPBm6LsydK8AuH0,1076
ibis/backends/impala/tests/test_ddl.py,sha256=dKtpf-JqD37qc0BOZmA9oA1nuk6L1aIHrZ1bHtvq5qc,6608
ibis/backends/impala/tests/test_ddl_compilation.py,sha256=UYG6ioFgrYpV9GNXCESvh6ZA8PFw-dTBBPZEfKnfBtY,8084
ibis/backends/impala/tests/test_exprs.py,sha256=QGN2jUadsmVqhUNcdDKqR2NkZU7--nevQUev3s849bE,19590
ibis/backends/impala/tests/test_in_not_in.py,sha256=PohhSbuLGMlI42PS15BhJivshlLt1QEwjAoCkUqhs80,1142
ibis/backends/impala/tests/test_metadata.py,sha256=-Sz7fFp2SInViUYhIwRg4kGhCgrZtCJC9Qsmq_CfKUU,3857
ibis/backends/impala/tests/test_parquet_ddl.py,sha256=bZR11PphYjz0rkiCP5PbL6BSXYgql4wfCupHYdsvNtA,2463
ibis/backends/impala/tests/test_partition.py,sha256=6P8sx-cOd3qTj7pZme4siIJ_c-mpWQIF-WhoT0neey8,5761
ibis/backends/impala/tests/test_patched.py,sha256=RGMy4FS447310NCWvobTSw_NdX3c-oURk_BHutVu8zg,1777
ibis/backends/impala/tests/test_sql.py,sha256=fLmTpxt_sF7fVc3t0kpTJUuxZeYItY0mSp3b5GfX6Xw,9429
ibis/backends/impala/tests/test_string_builtins.py,sha256=hoUmbU7G-YFuWtZkpbcm_ZrmIzAddXSMFy5KYFmcMBI,2580
ibis/backends/impala/tests/test_udf.py,sha256=4qhuU8_OzXo4xRkkDx5uDyibaJxSJcLIRNw1HCnH1ik,16540
ibis/backends/impala/tests/test_unary_builtins.py,sha256=CqRf9nnIMxQUCkSdnKzzexsQUUWR1JhUA0z8X8ArKp8,3803
ibis/backends/impala/tests/test_value_exprs.py,sha256=ZD8tCI63FoHlF5Rd_NZYCNJsG1M_zFF_JDLbD6kN_z0,7376
ibis/backends/impala/tests/test_window.py,sha256=peSSMPBC7VfAF88RlVBy3LNVUusBb6RIKSrnf7f-clA,4332
ibis/backends/impala/udf.py,sha256=HAtqWVtY1XXqezSPhCSZq0DtBtAv5mnH80H2dI6MVqE,7089
ibis/backends/mssql/__init__.py,sha256=9T5detT6asB6uoJyU11THKxjEWD6lO14hpztYQGQZAM,25413
ibis/backends/mssql/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/mssql/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/mssql/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/mssql/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/mssql/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/mssql/tests/conftest.py,sha256=OEFGC_v3IyG8I2iX7bxf-iHImNEHD6gU-NSuDIHBOac,2026
ibis/backends/mssql/tests/snapshots/test_client/test_rank_no_window_frame/out.sql,sha256=mcFwGvJB5BBxTE_JjCSGsXEqvabIm0YMUgiZJdqpKxQ,218
ibis/backends/mssql/tests/snapshots/test_window/test_rank_no_window_frame/out.sql,sha256=mcFwGvJB5BBxTE_JjCSGsXEqvabIm0YMUgiZJdqpKxQ,218
ibis/backends/mssql/tests/test_client.py,sha256=INgNefjOBXqWPkQGkz_7xJ4sav0IaqKJks7MImHeTN4,10306
ibis/backends/mysql/__init__.py,sha256=QyPAKWv3Nj5ZJxoNTE9S8qoBuJLK0F5L-vNnZ5HQOOU,15981
ibis/backends/mysql/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/mysql/__pycache__/converter.cpython-312.pyc,,
ibis/backends/mysql/__pycache__/datatypes.cpython-312.pyc,,
ibis/backends/mysql/converter.py,sha256=8YdE0dePrgC6Ov0hoTsxPGqnCzX3iTHzCWk3NvO2uPk,855
ibis/backends/mysql/datatypes.py,sha256=bZST62UipD_4OD4UeyLbNO_nGRwAbms0xcr_1Tf5jLk,4421
ibis/backends/mysql/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/mysql/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/mysql/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/mysql/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/mysql/tests/conftest.py,sha256=BMQM0zqkQBXIeHhyOmcuC_nk65Ph_aJ74f0kSRROC0M,2474
ibis/backends/mysql/tests/test_client.py,sha256=yPis0UgdjqCFIzqOtZr37dnJJ4dGgi5-W6KxiQpwHUw,9068
ibis/backends/oracle/__init__.py,sha256=gqRyX17yZqKAvLcnjDxf-O7ku6XlJV-OVV-4pzqKepE,21176
ibis/backends/oracle/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/oracle/__pycache__/converter.cpython-312.pyc,,
ibis/backends/oracle/converter.py,sha256=ePzdpwc_M4hM_Nd1vbLIvhUfvVMo72E__rQ2RcpM2qo,448
ibis/backends/oracle/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/oracle/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/oracle/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/oracle/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/oracle/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/oracle/tests/conftest.py,sha256=09R85rMwCp0SY5GDL8qbZXCJywS45lNKje_n0BeBiLg,4627
ibis/backends/oracle/tests/test_client.py,sha256=ecmMmn0V5I5Fjo_kIqwknBRU0qxA2DsXE_2bCj1_A6I,2302
ibis/backends/oracle/tests/test_datatypes.py,sha256=Ct_w87WtHdrkrC6adP6QBa0cBlKlozA2EiTAehmuLjQ,1575
ibis/backends/polars/__init__.py,sha256=_VZ3D8EwTOWcmsW3fH3BI2feA-JINA007zzHeHOIiMw,19910
ibis/backends/polars/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/polars/__pycache__/compiler.cpython-312.pyc,,
ibis/backends/polars/__pycache__/rewrites.cpython-312.pyc,,
ibis/backends/polars/compiler.py,sha256=JGc8bsq-A1JrRRU97Fp2ZZxwUfMhzhd7JveoNQaZroA,46503
ibis/backends/polars/rewrites.py,sha256=mf7QvOoGnJ44gt5RaYU-096syR48isFJZAVNZAABvQI,4589
ibis/backends/polars/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/polars/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/polars/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/polars/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/polars/tests/__pycache__/test_join.cpython-312.pyc,,
ibis/backends/polars/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/polars/tests/conftest.py,sha256=4VFAo_RfkN6aseuCHiWGb46kdTLwfga6ED4wBB5ymYk,1662
ibis/backends/polars/tests/test_client.py,sha256=p_5u1hVhwJ7_1UKEzBKuNSNDPEcFRUY9_9m2UAyKpLk,2358
ibis/backends/polars/tests/test_join.py,sha256=cLIxaUvBS0UJ42pWwNyEQ5Ji68AvT9yX2ju_Er_nHoI,724
ibis/backends/polars/tests/test_udf.py,sha256=nEnN9etlIQMZ7D5iIhrXLNNWg5pIi69__yaDIMQIUTs,3353
ibis/backends/postgres/__init__.py,sha256=hqvz7ttL6lK3nLxofS2gls2j0ycKrgoLaNallN5xblQ,24754
ibis/backends/postgres/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/postgres/__pycache__/converter.cpython-312.pyc,,
ibis/backends/postgres/converter.py,sha256=GPHbKaXkb5-Hi6AHjY7w2wgs7goE3MsAHhegMsxN000,1099
ibis/backends/postgres/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/postgres/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_functions.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_geospatial.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_json.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_postgis.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_string.cpython-312.pyc,,
ibis/backends/postgres/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/postgres/tests/conftest.py,sha256=xN2rtrXUaDEfwYI-448OGrvTbvnzPapFiFcF8XdQisM,2902
ibis/backends/postgres/tests/snapshots/test_client/test_compile_toplevel/out.sql,sha256=0QuDfWtFEKmfNOh43oPoolZO1K3_BgRmOB_ZEuCBFWg,56
ibis/backends/postgres/tests/snapshots/test_client/test_timezone_from_column/out.sql,sha256=7_n5C5ZOvQv3souumwQbFwoMhgtIuGPJ72cbMUMrQak,246
ibis/backends/postgres/tests/snapshots/test_functions/test_analytic_functions/out.sql,sha256=EBbJ6JNIs2AauNVYMuN_xzw6t1fIJvDEcRPwkZNHJtI,728
ibis/backends/postgres/tests/snapshots/test_functions/test_cast/double_to_int16/out.sql,sha256=ErqI1xA_dM_R7ENN-3_KbemI-F3uX-dJu1WdzzATcDo,108
ibis/backends/postgres/tests/snapshots/test_functions/test_cast/double_to_int8/out.sql,sha256=628DTIg64pwrypoMuEfel7TFe7_tz9Fm1IDPPP9F1U8,107
ibis/backends/postgres/tests/snapshots/test_functions/test_cast/string_to_decimal_no_params/out.sql,sha256=5pUpdFykI01mxYdI9Z65pVSWn_2CDVH_UO7arW2NIeE,109
ibis/backends/postgres/tests/snapshots/test_functions/test_cast/string_to_decimal_params/out.sql,sha256=bWhc4SKELZ5AC1mo5IdDms6VWr4_BOP5ksnLBiwbGDw,121
ibis/backends/postgres/tests/snapshots/test_functions/test_cast/string_to_double/out.sql,sha256=_ewQ2mRRSNsRc7HTh7FLCcGu4oGRhprPWzbu0PdS8tg,118
ibis/backends/postgres/tests/snapshots/test_functions/test_cast/string_to_float/out.sql,sha256=cUR_snqcHEqmrqyHCSEmo0Qjxtphv7OdkpTizm6TZ6o,106
ibis/backends/postgres/tests/snapshots/test_functions/test_date_cast/out.sql,sha256=L4oLuMC73j--zGRSy1Gh8bIfoNSuo9TSGXlJGOtuvYI,113
ibis/backends/postgres/tests/snapshots/test_functions/test_timestamp_cast_noop/out1.sql,sha256=BNywfnpMtXm8i7Jh9GTkIU6nNjhKyHXuloZ2BRY_he0,64
ibis/backends/postgres/tests/snapshots/test_functions/test_timestamp_cast_noop/out2.sql,sha256=8_nLqRSYfek3V4oOR9hvIVnRZ7Iwng60s0L_9Qe-snw,102
ibis/backends/postgres/tests/snapshots/test_functions/test_union_cte/False/out.sql,sha256=mnSg_kBQXJkR6-bokXEUkrGFyaKA7KWX-rIHgwzuBjY,290
ibis/backends/postgres/tests/snapshots/test_functions/test_union_cte/True/out.sql,sha256=68O6meBqljMkuZnF8srtR3fMBVw9EmJXs4QGIK5h8vk,282
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_equals/out1.sql,sha256=NvNmiyz70HUjxCvxCLocKwwtS9NYrgoVUKeO8vSSP3I,338
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_equals/out2.sql,sha256=jpM-Lm5hLNxanbIcazrbs_ySq7BOwoM2XYdZS5b1ayk,97
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_equals/out3.sql,sha256=1-RSNSKDyHage4xGHXwk6h9Jd_4qDRENKM3HGpO9XLM,82
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/linestring-geography/out.sql,sha256=6HCqEUlC-EyrVhh_W_dd1csiQ93O7J2V4a6w4HwLt2g,229
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/linestring-geometry/out.sql,sha256=6HCqEUlC-EyrVhh_W_dd1csiQ93O7J2V4a6w4HwLt2g,229
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/linestring-none/out.sql,sha256=Yr_YS6R5FAS2sLeYIEnZ6iZX_d4OFRQMlHSL9tsu_6U,223
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/linestring-srid/out.sql,sha256=6HCqEUlC-EyrVhh_W_dd1csiQ93O7J2V4a6w4HwLt2g,229
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multilinestring-geography/out.sql,sha256=uLrb5TFr124L9bad_ogd84_VMwS6CZcpi9ReT-Qu8xY,377
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multilinestring-geometry/out.sql,sha256=uLrb5TFr124L9bad_ogd84_VMwS6CZcpi9ReT-Qu8xY,377
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multilinestring-none/out.sql,sha256=u_Pi37Dr9xcVdFIw6-TAt4stIJlUiI_f1Kz-mHh_82U,371
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multilinestring-srid/out.sql,sha256=uLrb5TFr124L9bad_ogd84_VMwS6CZcpi9ReT-Qu8xY,377
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipoint-geography/out.sql,sha256=SHNULnkRa5MTvlGuUMeVVPjchjv44x2WOCfsraQhGfk,289
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipoint-geometry/out.sql,sha256=SHNULnkRa5MTvlGuUMeVVPjchjv44x2WOCfsraQhGfk,289
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipoint-none/out.sql,sha256=q8isIxD2RM-aLaRxXKvnDMACkw695HI1MNlpNL_g3KI,283
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipoint-srid/out.sql,sha256=SHNULnkRa5MTvlGuUMeVVPjchjv44x2WOCfsraQhGfk,289
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipolygon-geography/out.sql,sha256=iKEBJ8aCHEpFK2SoWanp7voSvOaRky5kdSy9Dsy4vBY,486
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipolygon-geometry/out.sql,sha256=iKEBJ8aCHEpFK2SoWanp7voSvOaRky5kdSy9Dsy4vBY,486
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipolygon-none/out.sql,sha256=8HphNhL0ZpMV1d4MUyKIbcCWxwk1EmG6bLmPzMfOxeQ,474
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/multipolygon-srid/out.sql,sha256=iKEBJ8aCHEpFK2SoWanp7voSvOaRky5kdSy9Dsy4vBY,486
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/point-geography/out.sql,sha256=YNyu2RFa-AjTHa6gd9AGOGXXzrlBC9v4dfxsmDTFhSA,153
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/point-geometry/out.sql,sha256=YNyu2RFa-AjTHa6gd9AGOGXXzrlBC9v4dfxsmDTFhSA,153
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/point-none/out.sql,sha256=qta4cXXPHmue94mGRtmwA9fd8iS7qFEnBV7-kn1ONnk,147
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/point-srid/out.sql,sha256=YNyu2RFa-AjTHa6gd9AGOGXXzrlBC9v4dfxsmDTFhSA,153
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon-geography/out.sql,sha256=bP2O18ZNYC3HfvxMXiOkLDJ-v_13chKGvK9AukYyIn8,441
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon-geometry/out.sql,sha256=bP2O18ZNYC3HfvxMXiOkLDJ-v_13chKGvK9AukYyIn8,441
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon-none/out.sql,sha256=********************************-r_NooEe2wI,429
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon-srid/out.sql,sha256=bP2O18ZNYC3HfvxMXiOkLDJ-v_13chKGvK9AukYyIn8,441
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon_single-geography/out.sql,sha256=zrgG-FHj9ig8ognNunH1-o1qnirMsEZGYu247Pzxi8k,281
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon_single-geometry/out.sql,sha256=zrgG-FHj9ig8ognNunH1-o1qnirMsEZGYu247Pzxi8k,281
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon_single-none/out.sql,sha256=3URf1qYRGuVwLNeXfRycdQFiO6McFX6QvCT_X7XU31s,275
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_literals_smoke/polygon_single-srid/out.sql,sha256=zrgG-FHj9ig8ognNunH1-o1qnirMsEZGYu247Pzxi8k,281
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/linestring_contains/out.sql,sha256=3pNfzmT-VCnOG_Tz0k4jXzEDJ6kDiRrZ20Pbk0R0bSs,124
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/linestring_end_point/out.sql,sha256=B29IObJ72AYXgY83nGoMmo6-F5VHUIBhaYQSP506Zjw,106
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/linestring_length/out.sql,sha256=TdaOpy4oqImwpxALEm32kzofHK0hfDOvyi8y6NXq20c,91
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/linestring_start_point/out.sql,sha256=mtOI0dWYrxcW6hfA3BWYfsGehDwkW4cIXaFqguYeCco,110
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/multipolygon_n_points/out.sql,sha256=DZIBeYcgBNpqPMgzgpQhwe7Jk-Cyt9MiDu92N_xg4aU,97
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/point_set_srid/out.sql,sha256=rOG46PGxMqlEeQDhzfhRyWrljMdy3HXf3CE84fz4Hbc,106
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/point_srid/out.sql,sha256=AHm5mkEAVAtZw3Yb2d3eQFz2xTmwXwefvKenIPk2XBk,77
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/point_x/out.sql,sha256=e33t71npMy2cn8icmv7v1cU0Qj2rjb7In7KTUgNbsS4,71
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/point_y/out.sql,sha256=LUARmMrjqzfsq1uGp3e6Kfl0Lo5C7vqOfu2pqWF0jyk,71
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/polygon_area/out.sql,sha256=h21ErpM_jbKziNx2Qaxc7h6ovyXmV2oZ00lXbkAr4QU,81
ibis/backends/postgres/tests/snapshots/test_geospatial/test_geo_ops_smoke/polygon_perimeter/out.sql,sha256=aJfa7L-Lc8qj71RGkvowyMlCe2os3wAoQKJq61-yCRI,91
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr0/out.sql,sha256=nPiiK2QGVcfdEKibdCC7JDZL965j1JLkDa2BGitegc0,103
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr1/out.sql,sha256=hcLYNGEu5p347PmNNNYssh5F9tgjrWMP-1Zf39OzD1g,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr2/out.sql,sha256=hcLYNGEu5p347PmNNNYssh5F9tgjrWMP-1Zf39OzD1g,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr3/out.sql,sha256=fmml-f1nGjuvScRgbBSDxT0tLtmYtk8xXY7TjCQaR4M,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr4/out.sql,sha256=WU3kghjkj0wwUuoQd7DAJioQQpKb3AN_l-kpcI9HB3I,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr5/out.sql,sha256=hcLYNGEu5p347PmNNNYssh5F9tgjrWMP-1Zf39OzD1g,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr6/out.sql,sha256=fmml-f1nGjuvScRgbBSDxT0tLtmYtk8xXY7TjCQaR4M,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_explicit/expr7/out.sql,sha256=WU3kghjkj0wwUuoQd7DAJioQQpKb3AN_l-kpcI9HB3I,109
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp0/out.sql,sha256=hIwxW9ZZ9dJyb3qdIzod3GbG9sqUuRRBs5337vAe5E4,139
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp1/out.sql,sha256=msP5axTS0u_z6O3hBFAfkeXWusX26wuR8_nKtYnj4UM,139
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp2/out.sql,sha256=KBZ03uDjH9Xgkzx2ouNNeZpZRLjW3otKmG-mnyJ57WQ,139
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp3/out.sql,sha256=BMYGzoyxoUkKK8fGM2a5soIo9V6OsZevil3J6ls3WaI,199
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp4/out.sql,sha256=j--lg9nxFbOk8-XVaQUiWp_UnAQzEK-ejQtkapYV36s,199
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp5/out.sql,sha256=VNKBZ10-fWfjOh1Kfv4APi87ezs2i39098SsdtHGdCA,215
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp6/out.sql,sha256=HB0UkQTs9uGZXWBYhNF6At2C89yeOBs-d9imwJs-0r8,243
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp7/out.sql,sha256=clkm2S2OT6mF06Wqnqxxczt8nqKEhj_4sfWoYbIma7M,295
ibis/backends/postgres/tests/snapshots/test_geospatial/test_literal_geospatial_inferred/shp8/out.sql,sha256=Qep0R2OvsR4uPNZ40vzQj_YuzORwItFvHmohbe3V-0Q,223
ibis/backends/postgres/tests/snapshots/test_postgis/test_select_linestring_geodata/out.sql,sha256=lIobiulc7CUY2lYOkDntQ6i_dad9v3tPbFCf4jhPPMc,80
ibis/backends/postgres/tests/snapshots/test_postgis/test_select_multipolygon_geodata/out.sql,sha256=PuHxfj6givBBgUZa3uxgvt893IaanS5LDOLJF9NLO0c,84
ibis/backends/postgres/tests/snapshots/test_postgis/test_select_point_geodata/out.sql,sha256=NzWLxTWIHlaLgQE45pm1kSoilp89aNuQ6CTZe2XrnWI,70
ibis/backends/postgres/tests/snapshots/test_postgis/test_select_polygon_geodata/out.sql,sha256=pcblDEgK1sUQAmnor7a143EFkMJIsiH6mFWZg5f7N_Y,74
ibis/backends/postgres/tests/test_client.py,sha256=fZZkKmeBFIqdHxMpSnKFNdE09tH4WZcJg-ZDHO_2aIc,15980
ibis/backends/postgres/tests/test_functions.py,sha256=5okaG1OOwkRzNBk3Y61Bx2I_8i25q1eqJWEy0jXZm78,37879
ibis/backends/postgres/tests/test_geospatial.py,sha256=XwuE9M7HMUowsZxFe5UtUKFQc7WyIjycUEEXHE4QRng,17409
ibis/backends/postgres/tests/test_json.py,sha256=9iuoL96-_WN389QzdvuCvAksEeyz4w0ZhjJAOkhe3m0,2769
ibis/backends/postgres/tests/test_postgis.py,sha256=d-cIfM8lO0GJzv6h00dsI4CduhFdc-M2U6-zDSQyOQI,9069
ibis/backends/postgres/tests/test_string.py,sha256=o4IHoFb1XCQ_PFD01Bpwbs9n9VvwewfbviW0f3Eb4zk,493
ibis/backends/postgres/tests/test_udf.py,sha256=cPwQws81QPxw3X928QYsLo-BPllg8KUiObHqFUEqWx4,5295
ibis/backends/pyspark/__init__.py,sha256=S92dsnZCcsVEUnaWYKpH04hK6TE3k_EJeDR0EDLSHPQ,49182
ibis/backends/pyspark/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/pyspark/__pycache__/converter.cpython-312.pyc,,
ibis/backends/pyspark/__pycache__/datatypes.cpython-312.pyc,,
ibis/backends/pyspark/converter.py,sha256=Ybh-WGh3qNaZaqRbUzhsXNpAPHTQa8aUHGXBdBNpJkY,1263
ibis/backends/pyspark/datatypes.py,sha256=Kqu5moTssg4nYFNtfbtrG6A1Pa8ZnjM59LItg08WU98,4491
ibis/backends/pyspark/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/pyspark/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_aggregation.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_basic.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_ddl.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_import_export.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_null.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/pyspark/tests/__pycache__/test_window.cpython-312.pyc,,
ibis/backends/pyspark/tests/conftest.py,sha256=j9909bTHRA_UOBjUYBxiVE2XcJxcXNGTUdMz5rinShM,16359
ibis/backends/pyspark/tests/test_aggregation.py,sha256=PX62jfPvdp0TY2_Q7-TTqsFmdMiL2o7gIwvZpRdXP04,665
ibis/backends/pyspark/tests/test_basic.py,sha256=sETE88v1Z5CtQLbREoDt0-wkGGNhIiJBxpAhTFl2YI0,4354
ibis/backends/pyspark/tests/test_client.py,sha256=txRF-aKm4JfCBHEWwSa3GodtAAWvdMlIa2BrHn7Z204,6144
ibis/backends/pyspark/tests/test_ddl.py,sha256=1KfWq8ViAyUCN46qFrjqWqL4h49w1vG3OAY1h3hnWcc,5516
ibis/backends/pyspark/tests/test_import_export.py,sha256=yp8geTbXz0lCet2NVZKQx6E27T6Fx74vW0aAGeezdYk,3646
ibis/backends/pyspark/tests/test_null.py,sha256=pY68X6vDl79pSa8h46Hdw6-Bo_h-A6MpD8wNwk8RAGU,815
ibis/backends/pyspark/tests/test_udf.py,sha256=AnI9lq4wEI_CVPSrFeYeCZnktNRtSeDV_G_r7tYkeH8,1846
ibis/backends/pyspark/tests/test_window.py,sha256=kZMiEDmdMHxkJO_MklZO7ps9R-IfO5AhNehMTHLOuIQ,4008
ibis/backends/risingwave/__init__.py,sha256=QCOPqEWAEBKmbk4zy4uzNIl6SfX6CcS_oxm5qQavn9Q,30281
ibis/backends/risingwave/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/risingwave/__pycache__/converter.cpython-312.pyc,,
ibis/backends/risingwave/converter.py,sha256=AAF0bJnW2WEZZZql5g28C-rEAKaYJGBgmjP9C8fNwoU,238
ibis/backends/risingwave/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/risingwave/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/risingwave/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/risingwave/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/risingwave/tests/__pycache__/test_functions.cpython-312.pyc,,
ibis/backends/risingwave/tests/__pycache__/test_json.cpython-312.pyc,,
ibis/backends/risingwave/tests/__pycache__/test_streaming.cpython-312.pyc,,
ibis/backends/risingwave/tests/conftest.py,sha256=mSE8Wmg9nNAHrntjzFaCNG4mdK6eFgWbqbeG18lPWWg,2338
ibis/backends/risingwave/tests/snapshots/test_client/test_compile_toplevel/out.sql,sha256=0QuDfWtFEKmfNOh43oPoolZO1K3_BgRmOB_ZEuCBFWg,56
ibis/backends/risingwave/tests/snapshots/test_functions/test_union_cte/False/out.sql,sha256=mnSg_kBQXJkR6-bokXEUkrGFyaKA7KWX-rIHgwzuBjY,290
ibis/backends/risingwave/tests/snapshots/test_functions/test_union_cte/True/out.sql,sha256=68O6meBqljMkuZnF8srtR3fMBVw9EmJXs4QGIK5h8vk,282
ibis/backends/risingwave/tests/test_client.py,sha256=m2i1QVZdESHEgjdpr_GPtvpEp4M2CxtAOyuH9PyVHBA,3971
ibis/backends/risingwave/tests/test_functions.py,sha256=12UQNr6yz4OJ4EM_Tmjnk5VAg43cKZVGd6h9juuwpVI,25309
ibis/backends/risingwave/tests/test_json.py,sha256=q_zz-EflpwS5z0Lo4UmaY8mz5RZKYKlvbgUBPzUmtpw,421
ibis/backends/risingwave/tests/test_streaming.py,sha256=qyyQR9P3iQeKxmRaprZbSXZXZsZoNEkUfBsQryWPv28,3411
ibis/backends/snowflake/__init__.py,sha256=ZSYCSqBtVGiMvhCzKEFVK21Fpd2HsIGTg48_kgiWtAU,41051
ibis/backends/snowflake/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/snowflake/__pycache__/converter.cpython-312.pyc,,
ibis/backends/snowflake/converter.py,sha256=1cjFYVFZ6qJsZM6qGKfLkfmh1FJX-2hiQkDYKYwSVLw,3476
ibis/backends/snowflake/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/snowflake/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/snowflake/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/snowflake/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/snowflake/tests/__pycache__/test_compiler.cpython-312.pyc,,
ibis/backends/snowflake/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/snowflake/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/snowflake/tests/conftest.py,sha256=hiUbC16HpxRX5rKsSOGymkktPRfa_6inS1MxJI3F1sU,8122
ibis/backends/snowflake/tests/snapshots/test_compiler/test_more_than_one_quantile/two_quantiles.sql,sha256=liUwjVWtrPH_FvursYyLnwyWyfOsWXAMoOQDKWbtEqc,200
ibis/backends/snowflake/tests/test_client.py,sha256=fzgsww8WSvwhHlWmqS1V4pZJtGRoXdOO7uJjtb1VqSA,12706
ibis/backends/snowflake/tests/test_compiler.py,sha256=AU-DFqVnRki1xgKLnxePZQJq7RLtTFV4y8e1jR8nXEY,413
ibis/backends/snowflake/tests/test_datatypes.py,sha256=CEQTciiQZ9lc8V80Vd4okmLSBMw9bCJ7JFuPBD3Tei4,4111
ibis/backends/snowflake/tests/test_udf.py,sha256=KL7yrxkFOaMCSqqSVlhvCQwNXUv9jjWmcxfa_1Y-cCo,8001
ibis/backends/sql/__init__.py,sha256=Mr634d0Am9GuxKGrTzdHCSxqDKRhTTpXArIY6vHQH6s,21707
ibis/backends/sql/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sql/__pycache__/datatypes.cpython-312.pyc,,
ibis/backends/sql/__pycache__/ddl.cpython-312.pyc,,
ibis/backends/sql/__pycache__/dialects.cpython-312.pyc,,
ibis/backends/sql/__pycache__/rewrites.cpython-312.pyc,,
ibis/backends/sql/compilers/__init__.py,sha256=-_1Aj9Zh9y5_oO_WoAHW8HuukQSUV0Y0rugFCeNtZdc,1704
ibis/backends/sql/compilers/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/athena.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/base.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/clickhouse.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/databricks.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/datafusion.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/druid.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/duckdb.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/exasol.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/flink.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/impala.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/mssql.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/mysql.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/oracle.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/postgres.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/pyspark.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/risingwave.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/snowflake.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/sqlite.cpython-312.pyc,,
ibis/backends/sql/compilers/__pycache__/trino.cpython-312.pyc,,
ibis/backends/sql/compilers/athena.py,sha256=vcv7XGLM9ypD1-2oQ8Lh2b22I95Eebu_fTQXLvNhudw,1405
ibis/backends/sql/compilers/base.py,sha256=0tI8lckxGG-fKH-5nLbL8tTN3wthBvA5abe2aYTqbw8,54004
ibis/backends/sql/compilers/bigquery/__init__.py,sha256=Jj4SS55Ejh-vy0ffG9mFDo74cgbppCMvFHjbv5EJD-c,40853
ibis/backends/sql/compilers/bigquery/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sql/compilers/bigquery/udf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/sql/compilers/bigquery/udf/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sql/compilers/bigquery/udf/__pycache__/core.cpython-312.pyc,,
ibis/backends/sql/compilers/bigquery/udf/__pycache__/find.cpython-312.pyc,,
ibis/backends/sql/compilers/bigquery/udf/__pycache__/rewrite.cpython-312.pyc,,
ibis/backends/sql/compilers/bigquery/udf/core.py,sha256=Lz2ZOmgwv-BBzp5Y9vvvmB60LL0lsSouf2emApAWtSY,17292
ibis/backends/sql/compilers/bigquery/udf/find.py,sha256=cwFWEC_2SXnARyL6l8_XFgNjovyXye2LdY0zD6dEJXs,1599
ibis/backends/sql/compilers/bigquery/udf/rewrite.py,sha256=Gxp840qinD4RINdAvX3NJz6X9bpsk_aj8wlAulDdzt4,1401
ibis/backends/sql/compilers/clickhouse.py,sha256=yGrZUAlHz_-nnyDpaWu5R7kxf8RpmRb1EPBIQ2C22DY,30477
ibis/backends/sql/compilers/databricks.py,sha256=ii72CZINgmF6FrJewuL4U74cToeYaBSJmrl9F5s50zA,2936
ibis/backends/sql/compilers/datafusion.py,sha256=c-DH7V-uqjytzcUWYwfKHp7WExaaSbFnErdPXQ9DZuY,22639
ibis/backends/sql/compilers/druid.py,sha256=kpW2PIvrs_fUMS_aD14J8q_XCS0Qs2apgoRNpitsW64,6787
ibis/backends/sql/compilers/duckdb.py,sha256=Aqtha8lKexmHQ1RjBk3tRMbvUHfGYHvez1tbE0jRRG8,25426
ibis/backends/sql/compilers/exasol.py,sha256=MLuJVkdgeYxyBj4_zpEg1zglon3hGxbT_zM4pa26IjI,10482
ibis/backends/sql/compilers/flink.py,sha256=bcO46eO2hNy46DzLGSeLtAvDCT1vpd8tZIQ_sOVx4wA,22571
ibis/backends/sql/compilers/impala.py,sha256=r0l34bMlBP9G8UEicWbq4O4fSJqPF0mLYadreV3zeTk,11342
ibis/backends/sql/compilers/mssql.py,sha256=S4Nv_x-eD2fMBsoSsORFxkjn9roIAnXzmhCaTKMdt7g,19012
ibis/backends/sql/compilers/mysql.py,sha256=IkRtZp2JagMGGRx5x3hr651KriTVHPepe5IQ6Rmbbh4,12600
ibis/backends/sql/compilers/oracle.py,sha256=nyvypPDoGA9eAbPa1n9urGuwbvTtYXkiczXRINwsBDo,18832
ibis/backends/sql/compilers/postgres.py,sha256=-VB-ExETTlqunkqEBHwHvGSX4d0rtYpM_dSG4yknZfI,33190
ibis/backends/sql/compilers/pyspark.py,sha256=Nr5neZx2BP8_Qwl-0ptpyEJdbRk_8rTWsT280kuiSuk,24225
ibis/backends/sql/compilers/risingwave.py,sha256=spYMp-94j_khKzChoFOJx9TI4v2x-OKFTqikFZ_s2nY,6548
ibis/backends/sql/compilers/snowflake.py,sha256=rrbJAts__9RlYfPbcWc6fchotjnca9sEbz8qF09OrMM,32748
ibis/backends/sql/compilers/sqlite.py,sha256=xJ1CfjHb28jeGFw7jqztB21-O0zsmRywSdbMy53HtYw,19220
ibis/backends/sql/compilers/trino.py,sha256=WHLtB0IABbEFJ7V7Mw_wlMp51C-xnMlTe-8DQqgQM4Y,26047
ibis/backends/sql/datatypes.py,sha256=vwl665RVY6b2BzF2IW6sYKhMVuh7GAk1K-IrDuTvNh8,47588
ibis/backends/sql/ddl.py,sha256=_HPYjVxjXPqPLXBu7lTyU8Op2-XSJy7b5518i1DPVvw,3520
ibis/backends/sql/dialects.py,sha256=6wO2-AMjmVHIgTdLwss-8PBdgyPhiyZSfcGAALhAeqs,19308
ibis/backends/sql/rewrites.py,sha256=m_rIJM9fUOD-T4-3toIkr4Pi_-OXWbsvXVHz6ULBP2I,21831
ibis/backends/sql/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/sql/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sql/tests/__pycache__/test_compiler.cpython-312.pyc,,
ibis/backends/sql/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/sql/tests/test_compiler.py,sha256=4kDPlUpg4wNofjbi4RfMYX7fi1cDKvS2qejU5k7U8TQ,792
ibis/backends/sql/tests/test_datatypes.py,sha256=STmee6IK9ZS8_RycVios0yEXoXqtVKuonn3VILI5k-o,4064
ibis/backends/sqlite/__init__.py,sha256=dpQ0NS-wiVK_Myw3l2d2Rf09OgDrFmYhUdQ9LosgmxI,19441
ibis/backends/sqlite/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sqlite/__pycache__/converter.cpython-312.pyc,,
ibis/backends/sqlite/__pycache__/udf.cpython-312.pyc,,
ibis/backends/sqlite/converter.py,sha256=XvWCtDqFcGdIdzuNdWTTY6JPToQMNwJkpdMqVYJ5jtk,813
ibis/backends/sqlite/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/sqlite/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/sqlite/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/sqlite/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/sqlite/tests/__pycache__/test_types.cpython-312.pyc,,
ibis/backends/sqlite/tests/conftest.py,sha256=GiH7dsCvvY1f_8afMvTIZ2ZxMp9yz9hYH5ZQrNDjdoQ,1975
ibis/backends/sqlite/tests/test_client.py,sha256=QkG6Ue1NPDZ6EFtuXmKaFIaRUhebQJ1gMLW0oIY2z-A,2905
ibis/backends/sqlite/tests/test_types.py,sha256=WUtRf4lm2Tw6J-RgR5NQLwUmw0ILTp8l8QfknsyCvu4,3253
ibis/backends/sqlite/udf.py,sha256=9Y_IOeKyvczH4t2ckC2VjfWmov70UHp5UMQay4Ppj4g,10336
ibis/backends/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/tests/__pycache__/base.cpython-312.pyc,,
ibis/backends/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/tests/__pycache__/data.cpython-312.pyc,,
ibis/backends/tests/__pycache__/errors.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_aggregation.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_api.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_array.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_asof_join.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_binary.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_column.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_conditionals.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_dataframe_interchange.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_dot_sql.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_examples.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_export.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_expr_caching.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_generic.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_impure.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_interactive.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_io.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_join.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_json.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_map.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_markers.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_network.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_numeric.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_param.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_set_ops.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_signatures.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_sql.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_string.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_struct.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_temporal.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_udf.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_uuid.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_vectorized_udf.cpython-312.pyc,,
ibis/backends/tests/__pycache__/test_window.cpython-312.pyc,,
ibis/backends/tests/base.py,sha256=UR56YkbiQ2a6kcnlRrQGtXQwYEG5CFjIfDSC4yYnNyM,12837
ibis/backends/tests/conftest.py,sha256=9ewzpSNskvooyrJ86UJwREGa2ng8ZCvI8NUfgSBc49I,2279
ibis/backends/tests/data.py,sha256=CaHg0H0Q6uaa7mNCQlzW3QRCEgsQD1TKKPCURMZff_A,2922
ibis/backends/tests/errors.py,sha256=MpU0lHwiqAtsYoF7tcdLtAGjup2r5_k5JnGXNPkpf3k,7612
ibis/backends/tests/signature/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/tests/signature/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/tests/signature/__pycache__/typecheck.cpython-312.pyc,,
ibis/backends/tests/signature/tests/__pycache__/test_compatible.cpython-312.pyc,,
ibis/backends/tests/signature/tests/test_compatible.py,sha256=ioGwNzNRofDf_P29lQMxImaVOodmBJXBvFtGMKrHSNc,3520
ibis/backends/tests/signature/typecheck.py,sha256=tI6xL84nnGGs4Ezy23GAn3PA7Qn_1C6oQpHVp2G8PvY,4188
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/athena/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/bigquery/out.sql,sha256=-VbLYqW2aVtVsupbr2zPtnzjbqlcDbfrmFn8pNykvaI,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/clickhouse/out.sql,sha256=p0xj-ZatpdhPojNW5v1DC2E7W8RHZkGsfp1fjIsY4BM,332
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/databricks/out.sql,sha256=-VbLYqW2aVtVsupbr2zPtnzjbqlcDbfrmFn8pNykvaI,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/datafusion/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/druid/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/duckdb/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/exasol/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/flink/out.sql,sha256=-VbLYqW2aVtVsupbr2zPtnzjbqlcDbfrmFn8pNykvaI,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/impala/out.sql,sha256=-VbLYqW2aVtVsupbr2zPtnzjbqlcDbfrmFn8pNykvaI,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/mssql/out.sql,sha256=HOa0DzygXPWwDxUQKPejTdgfmQLTGyk6XHQwNYSX5Tk,326
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/mysql/out.sql,sha256=-VbLYqW2aVtVsupbr2zPtnzjbqlcDbfrmFn8pNykvaI,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/oracle/out.sql,sha256=TRFhdhOTKHHPtsl9to3pGGdPPmDQ-JuPCUdIaJdhq3o,296
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/postgres/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/pyspark/out.sql,sha256=-VbLYqW2aVtVsupbr2zPtnzjbqlcDbfrmFn8pNykvaI,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/risingwave/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/snowflake/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/sqlite/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_cte_refs_in_topo_order/trino/out.sql,sha256=HzFbIeOjzDRd8LLg-Gwb3ENn_53I1zC7y0I8YHO6pEM,314
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/athena/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/bigquery/out.sql,sha256=BccIFF-VBHuehtGaEKH9q_DW3prWqFvfhqkKLxtLocY,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/clickhouse/out.sql,sha256=0kuLHGHVACrWjDn25oSRuyTx-XhCMTNq4-YppdVzXYY,691
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/databricks/out.sql,sha256=BccIFF-VBHuehtGaEKH9q_DW3prWqFvfhqkKLxtLocY,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/datafusion/out.sql,sha256=DMQNbut6uqjYQmYZcbmrXzDg9EueV1lXEkW4zOcPvU8,514
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/druid/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/duckdb/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/exasol/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/flink/out.sql,sha256=kGqHF8HDnCVRexPl4alt0ZUXovsZoFt-fMtK4MtcgNk,691
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/impala/out.sql,sha256=BccIFF-VBHuehtGaEKH9q_DW3prWqFvfhqkKLxtLocY,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/mssql/out.sql,sha256=u0HbxkNf6C7y9EqUWiBkX6jIuOxBH7Tcp79TDVtLc9M,691
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/mysql/out.sql,sha256=BccIFF-VBHuehtGaEKH9q_DW3prWqFvfhqkKLxtLocY,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/oracle/out.sql,sha256=XlqbSjCNNRVlSGYMMPpv-ShOB4F3rOmYOHBP1tpGlbc,688
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/postgres/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/pyspark/out.sql,sha256=BccIFF-VBHuehtGaEKH9q_DW3prWqFvfhqkKLxtLocY,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/risingwave/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/snowflake/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/sqlite/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_group_by_has_index/trino/out.sql,sha256=NcMyjVaUvgGYtR1gJs3L7yNO42hTrb7WjNXLF_XcGkU,395
ibis/backends/tests/snapshots/test_sql/test_isin_bug/athena/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/bigquery/out.sql,sha256=mMotUbvjPiYkGb4-PBIrSb_ysCYwodXT9Nwfv3pEf04,130
ibis/backends/tests/snapshots/test_sql/test_isin_bug/clickhouse/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/databricks/out.sql,sha256=hpgw_XnXTZnhgLsim_nB6rHrmsmcsNeTvVBKoV0-MIQ,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/datafusion/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/druid/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/duckdb/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/exasol/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/flink/out.sql,sha256=hpgw_XnXTZnhgLsim_nB6rHrmsmcsNeTvVBKoV0-MIQ,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/impala/out.sql,sha256=hpgw_XnXTZnhgLsim_nB6rHrmsmcsNeTvVBKoV0-MIQ,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/mssql/out.sql,sha256=9d8PeDJqR4tnguZET3MYIEQHAqXKSXaW-Inyp0Dohtg,238
ibis/backends/tests/snapshots/test_sql/test_isin_bug/mysql/out.sql,sha256=hpgw_XnXTZnhgLsim_nB6rHrmsmcsNeTvVBKoV0-MIQ,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/oracle/out.sql,sha256=n8gMVTDsKQKtphiDSy69Ym7aHBnDHqd2_bZrIOcpTF8,125
ibis/backends/tests/snapshots/test_sql/test_isin_bug/postgres/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/pyspark/out.sql,sha256=hpgw_XnXTZnhgLsim_nB6rHrmsmcsNeTvVBKoV0-MIQ,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/risingwave/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/snowflake/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/sqlite/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_isin_bug/trino/out.sql,sha256=jUi8bQAQscJbii5yZxaWIijlAC8ae7djAdr_hbjkXP8,131
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/athena/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/bigquery/out.sql,sha256=0d8PBnji-weLi5249oDZYNmSUrYgB0uUuXg1hHLUEso,281
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/clickhouse/out.sql,sha256=7m93zok99anHpJH_bCT_Jdcm09afZ4pb7EVd8l63HIc,287
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/databricks/out.sql,sha256=jzqrA6iHRIGdf9m1GKAETEHgwzSKSTCVpef-YZPIZ-s,395
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/datafusion/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/druid/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/duckdb/out.sql,sha256=ZHxpZkTFV5ZgvlyW_-oqsqmLcKJGmZ1rz2s8HVwo0Mg,281
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/exasol/out.sql,sha256=wfEYvxTWfTJdmHYqS0-Di7q9Lpuex5gAEDDHbnEJheA,405
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/flink/out.sql,sha256=1dzhKRvxd-20t3TegXABlTmsnyavXg04cbeoIq3KLy4,300
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/impala/out.sql,sha256=P0R9t5idvUOUadCv1eWQ1JQhhWN08NGzcDmsTQGrIWY,405
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/mssql/out.sql,sha256=sr2oPOHwsV8sbMGvrEykCBZIfBtc7tE_36eNByLPlFc,561
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/mysql/out.sql,sha256=pr_8e8xt_wCgD-UCl66axdVm4ng9vSrYneEFBeVQTYk,531
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/oracle/out.sql,sha256=3j0IZCYQQ0or0WFGfVpU9QGTGJxTbC1y-s5eDLXUvRg,396
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/postgres/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/pyspark/out.sql,sha256=iYu2PewAyCHNkGOl_m0msOWLejsDuf90b_NuE3r1lY8,480
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/risingwave/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/snowflake/out.sql,sha256=YxBlqH0xr39YXsuZu2a1zTpxTMBDgUQmm4Msuf6q8eE,317
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/sqlite/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_mixed_qualified_and_unqualified_predicates/trino/out.sql,sha256=S9wbuC9QKTuj8ezODAKWIFRFHuWklArfbj62P-KJnfk,369
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/athena/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/bigquery/out.sql,sha256=ZCTeCMq0iR1Ss7bt6FOWaw69yS1Ln1sh8a4e3DyOX18,100
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/clickhouse/out.sql,sha256=tC8IKdnbVdb22oQaNQZdV19WzVRlrKPvDsw3tL2JpSQ,96
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/databricks/out.sql,sha256=ZCTeCMq0iR1Ss7bt6FOWaw69yS1Ln1sh8a4e3DyOX18,100
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/datafusion/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/druid/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/duckdb/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/exasol/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/flink/out.sql,sha256=ZCTeCMq0iR1Ss7bt6FOWaw69yS1Ln1sh8a4e3DyOX18,100
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/impala/out.sql,sha256=_3DlGvew2ba9vJ6DRGkKgGssUVXrkFAogDVGGDo4fy8,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/mssql/out.sql,sha256=kkhcEZmu6E6jf1NQ4Wf0KNBQ_2QqMYOIjmunV7zKnYg,135
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/mysql/out.sql,sha256=ZNIGAjlfhdBDNzW7BKFCxZNxkik9Vaoqq6Egru72ZX0,135
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/oracle/out.sql,sha256=6GnnUk9g_mpYXbmokIe_UGp-OewGxhZi5i425amkG0c,86
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/postgres/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/pyspark/out.sql,sha256=ZCTeCMq0iR1Ss7bt6FOWaw69yS1Ln1sh8a4e3DyOX18,100
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/risingwave/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/snowflake/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/sqlite/out.sql,sha256=vpD5ue_3M3xHsjdNNnqfxKUFqJKboMWWkXIza64gRoI,100
ibis/backends/tests/snapshots/test_sql/test_order_by_no_deference_literals/trino/out.sql,sha256=WFFlba0xIBIVYTobeZu7_Ej4BvSc1RYvPlL5NKwcGUE,89
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/athena/out.sql,sha256=6Hl56DRZ1hTL2k-ysc8CxhCpH4odf4yFs4OPeue4_ms,90
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/bigquery/out.sql,sha256=08KbKRwH4JLndqMEqMP0FwYqW0ZKGGJFVPRCcukGpmE,90
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/clickhouse/out.sql,sha256=fyFRL77tzs24e204chmFqxJ3PT9dBkdYHaub0W5waFI,99
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/databricks/out.sql,sha256=CiPGSQF4UWqPfyEf5ij2BZVk0A_G64Gonh2E5hYrT8Q,101
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/datafusion/out.sql,sha256=9tA6fRGAKgMUMSUtTGYQup11F-Ql3FPePDiO944HlkQ,149
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/duckdb/out.sql,sha256=9tA6fRGAKgMUMSUtTGYQup11F-Ql3FPePDiO944HlkQ,149
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/exasol/out.sql,sha256=LlUr8UAtKAAE4OyxbFO5O3-vRLtYJm8fQ3lesUfQApE,92
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/flink/out.sql,sha256=U4I2LARJ8ShMkkGp1A32_yw30ot0e2ogy3mnIexoKu0,196
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/impala/out.sql,sha256=P4wTOEWDTGx8Rt_LTMzOxYEkxaoLJlGwNHSX_XEs2wg,125
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/mssql/out.sql,sha256=gPD9bPWi4Ekdzcrgs14bcgDkvmsijqmf-lP9U5DdXqA,105
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/mysql/out.sql,sha256=BFB5x4SrLBdphaNETIyqncj__d1yEbl7vXogosJL-zo,90
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/oracle/out.sql,sha256=X-V4-r8DN4kJJalydcpRHeCqynYG3SUQ24rhdVZ3Y30,116
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/postgres/out.sql,sha256=9tA6fRGAKgMUMSUtTGYQup11F-Ql3FPePDiO944HlkQ,149
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/pyspark/out.sql,sha256=CiPGSQF4UWqPfyEf5ij2BZVk0A_G64Gonh2E5hYrT8Q,101
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/snowflake/out.sql,sha256=wV3jmd5mNeI72wsjxKcxZ2o3CGOjWg8zhMKmYNQ8yF8,133
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/sqlite/out.sql,sha256=tqeJI9lFVG9-JTtChpbZJdJwyDxOrLHJPyFiKv1OiEs,232
ibis/backends/tests/snapshots/test_sql/test_rewrite_context/trino/out.sql,sha256=6Hl56DRZ1hTL2k-ysc8CxhCpH4odf4yFs4OPeue4_ms,90
ibis/backends/tests/snapshots/test_sql/test_sample/athena-subquery/block.sql,sha256=2wJm-slpGqpPSyDi3VEXaM56IMySohlhhUh23fDPNV8,56
ibis/backends/tests/snapshots/test_sql/test_sample/athena-subquery/row.sql,sha256=2nVzNMV8bLSRvr9CxDIoGC16uOzuae5pIw4I1lVBSDI,59
ibis/backends/tests/snapshots/test_sql/test_sample/athena-table/block.sql,sha256=Lj4EJvxzOke_NXOaSUdaKlPo5wvIgZnAeLYiKgGHPos,116
ibis/backends/tests/snapshots/test_sql/test_sample/athena-table/row.sql,sha256=O0gYN-X7-iBjsKC07SN5a5v4xNa1yAWLRAyodtd7LU4,119
ibis/backends/tests/snapshots/test_sql/test_sample/bigquery-subquery/block.sql,sha256=vNnBZiqWfCe7B1bAHD73A4HCWofQxmeuYhVnJg43KvE,64
ibis/backends/tests/snapshots/test_sql/test_sample/bigquery-subquery/row.sql,sha256=DLyI9QWuZKjVD4XRNWg_eMTYLquttmCz0J4lNgNJVfY,52
ibis/backends/tests/snapshots/test_sql/test_sample/bigquery-table/block.sql,sha256=Ai3J0ikxCAicPlwt9MxpSaY-HNVA_X_55AVKwJLrcZU,112
ibis/backends/tests/snapshots/test_sql/test_sample/bigquery-table/row.sql,sha256=Ai3J0ikxCAicPlwt9MxpSaY-HNVA_X_55AVKwJLrcZU,112
ibis/backends/tests/snapshots/test_sql/test_sample/clickhouse-subquery/block.sql,sha256=FubFQigUwCCmG_m5FW26wEwN-Bcu-kbRQNlj9gbEyiY,61
ibis/backends/tests/snapshots/test_sql/test_sample/clickhouse-subquery/row.sql,sha256=FubFQigUwCCmG_m5FW26wEwN-Bcu-kbRQNlj9gbEyiY,61
ibis/backends/tests/snapshots/test_sql/test_sample/clickhouse-table/block.sql,sha256=qYI3uhPdS1sSVJOK4PXtTTD9M2MoXoHoc1RDug9207s,121
ibis/backends/tests/snapshots/test_sql/test_sample/clickhouse-table/row.sql,sha256=qYI3uhPdS1sSVJOK4PXtTTD9M2MoXoHoc1RDug9207s,121
ibis/backends/tests/snapshots/test_sql/test_sample/databricks-subquery/block.sql,sha256=bwAh_KgY8ry9Yvg9sH52tWuLHAp4Q8B2E_tcDPejK2o,57
ibis/backends/tests/snapshots/test_sql/test_sample/databricks-subquery/row.sql,sha256=bwAh_KgY8ry9Yvg9sH52tWuLHAp4Q8B2E_tcDPejK2o,57
ibis/backends/tests/snapshots/test_sql/test_sample/databricks-table/block.sql,sha256=gjgp-exCdwpJSJjA7rmCbbe9Kg4qfhcR5LBiF948XSc,117
ibis/backends/tests/snapshots/test_sql/test_sample/databricks-table/row.sql,sha256=gjgp-exCdwpJSJjA7rmCbbe9Kg4qfhcR5LBiF948XSc,117
ibis/backends/tests/snapshots/test_sql/test_sample/datafusion-subquery/block.sql,sha256=u_DyptQVAvXKMKe_Cj-bNQDI3YZLD0AUIppabK2hC1g,54
ibis/backends/tests/snapshots/test_sql/test_sample/datafusion-subquery/row.sql,sha256=u_DyptQVAvXKMKe_Cj-bNQDI3YZLD0AUIppabK2hC1g,54
ibis/backends/tests/snapshots/test_sql/test_sample/datafusion-table/block.sql,sha256=1vOGv4xScJtQrEuFdZSUymNzqBt96C1kMV0LDQOTsEE,114
ibis/backends/tests/snapshots/test_sql/test_sample/datafusion-table/row.sql,sha256=1vOGv4xScJtQrEuFdZSUymNzqBt96C1kMV0LDQOTsEE,114
ibis/backends/tests/snapshots/test_sql/test_sample/duckdb-subquery/block.sql,sha256=pcB6sf6HIoaJonfrKQW3Hk4zKDvfXtbqWLKNSGfffqE,64
ibis/backends/tests/snapshots/test_sql/test_sample/duckdb-subquery/row.sql,sha256=ixZd0-Crnao9GeBi6UMU95iG19ha-T4bkEFQfHxKod0,67
ibis/backends/tests/snapshots/test_sql/test_sample/duckdb-table/block.sql,sha256=qybQ-Sy-_LNn883DqbSQclUTmFDiEJ8qC5A4cdFQ22Y,124
ibis/backends/tests/snapshots/test_sql/test_sample/duckdb-table/row.sql,sha256=kOmZhf8h0ClzD-4m88NyveJ5ZswpQbCUOp3Ar5KOA7Q,127
ibis/backends/tests/snapshots/test_sql/test_sample/exasol-subquery/block.sql,sha256=u_DyptQVAvXKMKe_Cj-bNQDI3YZLD0AUIppabK2hC1g,54
ibis/backends/tests/snapshots/test_sql/test_sample/exasol-subquery/row.sql,sha256=u_DyptQVAvXKMKe_Cj-bNQDI3YZLD0AUIppabK2hC1g,54
ibis/backends/tests/snapshots/test_sql/test_sample/exasol-table/block.sql,sha256=1vOGv4xScJtQrEuFdZSUymNzqBt96C1kMV0LDQOTsEE,114
ibis/backends/tests/snapshots/test_sql/test_sample/exasol-table/row.sql,sha256=1vOGv4xScJtQrEuFdZSUymNzqBt96C1kMV0LDQOTsEE,114
ibis/backends/tests/snapshots/test_sql/test_sample/flink-subquery/block.sql,sha256=DLyI9QWuZKjVD4XRNWg_eMTYLquttmCz0J4lNgNJVfY,52
ibis/backends/tests/snapshots/test_sql/test_sample/flink-subquery/row.sql,sha256=DLyI9QWuZKjVD4XRNWg_eMTYLquttmCz0J4lNgNJVfY,52
ibis/backends/tests/snapshots/test_sql/test_sample/flink-table/block.sql,sha256=Ai3J0ikxCAicPlwt9MxpSaY-HNVA_X_55AVKwJLrcZU,112
ibis/backends/tests/snapshots/test_sql/test_sample/flink-table/row.sql,sha256=Ai3J0ikxCAicPlwt9MxpSaY-HNVA_X_55AVKwJLrcZU,112
ibis/backends/tests/snapshots/test_sql/test_sample/impala-subquery/block.sql,sha256=f9uEtk9696a7sQyqkhptCtOcgENKQeL2LdtI7g56lYc,54
ibis/backends/tests/snapshots/test_sql/test_sample/impala-subquery/row.sql,sha256=Dhfk6zLJpPchE6Pz_vy0LgaAAvBx5Hf6SmpyVq_Lf24,87
ibis/backends/tests/snapshots/test_sql/test_sample/impala-table/block.sql,sha256=gi0txWzDNJlcXyx5SmMzyQyzJ433_MPclhvJ68cJbLw,147
ibis/backends/tests/snapshots/test_sql/test_sample/impala-table/row.sql,sha256=gi0txWzDNJlcXyx5SmMzyQyzJ433_MPclhvJ68cJbLw,147
ibis/backends/tests/snapshots/test_sql/test_sample/mssql-subquery/block.sql,sha256=9xDD6i70w48fr4OcNlV4zPN6mV3B_ICZU3aRQJ5Bq7s,64
ibis/backends/tests/snapshots/test_sql/test_sample/mssql-subquery/row.sql,sha256=1f1u5-oc5VtZHK5rdTD0LuFB3QeH0V6j9brqlPbtdpQ,88
ibis/backends/tests/snapshots/test_sql/test_sample/mssql-table/block.sql,sha256=6g0dqWj8GSSsp0we_8bLRji0pQXLLDllapr613Hb-2U,169
ibis/backends/tests/snapshots/test_sql/test_sample/mssql-table/row.sql,sha256=6g0dqWj8GSSsp0we_8bLRji0pQXLLDllapr613Hb-2U,169
ibis/backends/tests/snapshots/test_sql/test_sample/mysql-subquery/block.sql,sha256=DLyI9QWuZKjVD4XRNWg_eMTYLquttmCz0J4lNgNJVfY,52
ibis/backends/tests/snapshots/test_sql/test_sample/mysql-subquery/row.sql,sha256=DLyI9QWuZKjVD4XRNWg_eMTYLquttmCz0J4lNgNJVfY,52
ibis/backends/tests/snapshots/test_sql/test_sample/mysql-table/block.sql,sha256=Ai3J0ikxCAicPlwt9MxpSaY-HNVA_X_55AVKwJLrcZU,112
ibis/backends/tests/snapshots/test_sql/test_sample/mysql-table/row.sql,sha256=Ai3J0ikxCAicPlwt9MxpSaY-HNVA_X_55AVKwJLrcZU,112
ibis/backends/tests/snapshots/test_sql/test_sample/oracle-subquery/block.sql,sha256=zj0WDo4ygCtZKywTQijn06IVTN65A09TwgCgjFbSm4Y,48
ibis/backends/tests/snapshots/test_sql/test_sample/oracle-subquery/row.sql,sha256=R1rP6EE-Q3uEC9EGEWP-O6Cc5JyVNGaGzNwHdh3jaRM,51
ibis/backends/tests/snapshots/test_sql/test_sample/oracle-table/block.sql,sha256=3_-b_dJKX3fJL5x_snV4SoGmDL4Z7i2jGi8GUzXCXIw,119
ibis/backends/tests/snapshots/test_sql/test_sample/oracle-table/row.sql,sha256=3_-b_dJKX3fJL5x_snV4SoGmDL4Z7i2jGi8GUzXCXIw,119
ibis/backends/tests/snapshots/test_sql/test_sample/postgres-subquery/block.sql,sha256=2wJm-slpGqpPSyDi3VEXaM56IMySohlhhUh23fDPNV8,56
ibis/backends/tests/snapshots/test_sql/test_sample/postgres-subquery/row.sql,sha256=2nVzNMV8bLSRvr9CxDIoGC16uOzuae5pIw4I1lVBSDI,59
ibis/backends/tests/snapshots/test_sql/test_sample/postgres-table/block.sql,sha256=1vOGv4xScJtQrEuFdZSUymNzqBt96C1kMV0LDQOTsEE,114
ibis/backends/tests/snapshots/test_sql/test_sample/postgres-table/row.sql,sha256=1vOGv4xScJtQrEuFdZSUymNzqBt96C1kMV0LDQOTsEE,114
ibis/backends/tests/snapshots/test_sql/test_sample/pyspark-subquery/block.sql,sha256=bwAh_KgY8ry9Yvg9sH52tWuLHAp4Q8B2E_tcDPejK2o,57
ibis/backends/tests/snapshots/test_sql/test_sample/pyspark-subquery/row.sql,sha256=bwAh_KgY8ry9Yvg9sH52tWuLHAp4Q8B2E_tcDPejK2o,57
ibis/backends/tests/snapshots/test_sql/test_sample/pyspark-table/block.sql,sha256=gjgp-exCdwpJSJjA7rmCbbe9Kg4qfhcR5LBiF948XSc,117
ibis/backends/tests/snapshots/test_sql/test_sample/pyspark-table/row.sql,sha256=gjgp-exCdwpJSJjA7rmCbbe9Kg4qfhcR5LBiF948XSc,117
ibis/backends/tests/snapshots/test_sql/test_sample/snowflake-subquery/block.sql,sha256=2wJm-slpGqpPSyDi3VEXaM56IMySohlhhUh23fDPNV8,56
ibis/backends/tests/snapshots/test_sql/test_sample/snowflake-subquery/row.sql,sha256=2nVzNMV8bLSRvr9CxDIoGC16uOzuae5pIw4I1lVBSDI,59
ibis/backends/tests/snapshots/test_sql/test_sample/snowflake-table/block.sql,sha256=TZve9qz3aqFjx_R4qnNlqk70_yAIXp2JE3WMuzVEung,155
ibis/backends/tests/snapshots/test_sql/test_sample/snowflake-table/row.sql,sha256=TZve9qz3aqFjx_R4qnNlqk70_yAIXp2JE3WMuzVEung,155
ibis/backends/tests/snapshots/test_sql/test_sample/sqlite-subquery/block.sql,sha256=_bpTB-HVF8fzEXguX9b-56kXodPbHtBgK98T4YPbsVg,124
ibis/backends/tests/snapshots/test_sql/test_sample/sqlite-subquery/row.sql,sha256=_bpTB-HVF8fzEXguX9b-56kXodPbHtBgK98T4YPbsVg,124
ibis/backends/tests/snapshots/test_sql/test_sample/sqlite-table/block.sql,sha256=n5BZH9y2_D5gR95t4HFqDBGTOVnqK9z8e1sDe8Z_mv4,184
ibis/backends/tests/snapshots/test_sql/test_sample/sqlite-table/row.sql,sha256=n5BZH9y2_D5gR95t4HFqDBGTOVnqK9z8e1sDe8Z_mv4,184
ibis/backends/tests/snapshots/test_sql/test_sample/trino-subquery/block.sql,sha256=2wJm-slpGqpPSyDi3VEXaM56IMySohlhhUh23fDPNV8,56
ibis/backends/tests/snapshots/test_sql/test_sample/trino-subquery/row.sql,sha256=2nVzNMV8bLSRvr9CxDIoGC16uOzuae5pIw4I1lVBSDI,59
ibis/backends/tests/snapshots/test_sql/test_sample/trino-table/block.sql,sha256=Lj4EJvxzOke_NXOaSUdaKlPo5wvIgZnAeLYiKgGHPos,116
ibis/backends/tests/snapshots/test_sql/test_sample/trino-table/row.sql,sha256=O0gYN-X7-iBjsKC07SN5a5v4xNa1yAWLRAyodtd7LU4,119
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/athena-random/out.sql,sha256=NOOBO6CRfrYE7Sdad8YkfBSVkdsF1HvuPXpG8zqK17s,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/athena-uuid/out.sql,sha256=7lxOpb92he0bMS6rb_L1ZhC6LyBQixEdxBkM-jGX1NI,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/bigquery-random/out.sql,sha256=5GzAksJG3kloc61AkjgNAPl48HcSJQv9HCGZf2BdVGw,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/bigquery-uuid/out.sql,sha256=eFsxoBiDXmtRdl2K2bevUwIBV1J7Ius241d3pCV2BR8,208
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/clickhouse-random/out.sql,sha256=iNEUCjtfux62wwYxWmCDA7NgAwgmvVusE0-liDWbFkg,254
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/clickhouse-uuid/out.sql,sha256=y3fUo9Eb96QA6J4WY1vNODB-sScL0PKhSm2OnlnoO4w,256
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/databricks-random/out.sql,sha256=5GzAksJG3kloc61AkjgNAPl48HcSJQv9HCGZf2BdVGw,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/databricks-uuid/out.sql,sha256=FHzVmIBsx-77Rvq6c5AE5snNF-nXD_jZFgusCY8GqPI,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/datafusion-random/out.sql,sha256=tJx7i131FYfbvfgMTu0D5e6RoUP_Y95I6qSU-aytkag,212
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/datafusion-uuid/out.sql,sha256=K_WaA_Ueu9zM9bW5jJgVSVSTwQ1fVPSgqZNtU6l-ChA,208
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/duckdb-random/out.sql,sha256=tJx7i131FYfbvfgMTu0D5e6RoUP_Y95I6qSU-aytkag,212
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/duckdb-uuid/out.sql,sha256=K_WaA_Ueu9zM9bW5jJgVSVSTwQ1fVPSgqZNtU6l-ChA,208
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/exasol-random/out.sql,sha256=tJx7i131FYfbvfgMTu0D5e6RoUP_Y95I6qSU-aytkag,212
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/flink-random/out.sql,sha256=5GzAksJG3kloc61AkjgNAPl48HcSJQv9HCGZf2BdVGw,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/flink-uuid/out.sql,sha256=FHzVmIBsx-77Rvq6c5AE5snNF-nXD_jZFgusCY8GqPI,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/impala-random/out.sql,sha256=Xdt7AxIEvT5KpMOdl5B_7gjz0BPPS6eUAwtrgnXAbHk,260
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/impala-uuid/out.sql,sha256=FHzVmIBsx-77Rvq6c5AE5snNF-nXD_jZFgusCY8GqPI,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/mssql-random/out.sql,sha256=0QAdJkZX_r9qkM5RCGkpHjJ36-yRpRR67HaP6pk91uw,225
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/mssql-uuid/out.sql,sha256=flK4Hw7gDoESHT8HrdsCJLDY7nECic9aYVXMduiNur8,193
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/mysql-random/out.sql,sha256=6HK-hw-2ZBJ4GXa1gHk32xNit7ttJ4Qs913LIClkx00,208
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/mysql-uuid/out.sql,sha256=s6tR-IA3mCcdbyjrBXCy0fX6o-NHL7Wl-3luE61ya8c,208
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/oracle-random/out.sql,sha256=7LSwgL39PbdHhO0-i1ND9kvekIc8iwchwQ-logspHoY,228
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/postgres-random/out.sql,sha256=tJx7i131FYfbvfgMTu0D5e6RoUP_Y95I6qSU-aytkag,212
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/postgres-uuid/out.sql,sha256=urW2JWw6eHzMaa8stR6Hl85YEl3Mwi7lkC4BrKXVMw8,230
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/pyspark-random/out.sql,sha256=5GzAksJG3kloc61AkjgNAPl48HcSJQv9HCGZf2BdVGw,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/snowflake-random/out.sql,sha256=cNH8XK7nO9aFd41yi8_pU-_GeDmcmww27vrgC8IZsYc,277
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/snowflake-uuid/out.sql,sha256=O8hegtUnZkTY_wlsMgeJXZjWMEBkxsDXRlwg4iTk1dc,205
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/sqlite-random/out.sql,sha256=MI3LyAR8iBt9A1ynBubhZqSTFKYrLCaWnG8QOMoHvIA,315
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/sqlite-uuid/out.sql,sha256=MMuMQUrhi4-zPODGBC1gNvL5FjF8g1fzeoyRQBtvqd4,191
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/trino-random/out.sql,sha256=NOOBO6CRfrYE7Sdad8YkfBSVkdsF1HvuPXpG8zqK17s,190
ibis/backends/tests/snapshots/test_sql/test_selects_with_impure_operations_not_merged/trino-uuid/out.sql,sha256=7lxOpb92he0bMS6rb_L1ZhC6LyBQixEdxBkM-jGX1NI,190
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/athena/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/bigquery/to_sql.sql,sha256=76T2-4HlHZzJctTs23XHtMtT48LULER31xE06S_Enjk,89
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/clickhouse/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/databricks/to_sql.sql,sha256=Zw2wyG9oRoPFp701VoQseHa9dAFFJAgi5RQeX_Nxz5U,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/datafusion/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/druid/to_sql.sql,sha256=6y38HO-soTzXSimqopdc0VU_eraIiaAazfVxUaVImmE,165
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/duckdb/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/exasol/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/flink/to_sql.sql,sha256=jshEf_dj2jPhFbFXyPe2HyGgtMUxnzGJ0b44oqBwlRw,165
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/impala/to_sql.sql,sha256=Zw2wyG9oRoPFp701VoQseHa9dAFFJAgi5RQeX_Nxz5U,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/mssql/to_sql.sql,sha256=RxaQPMl0jdsKWB2FIJHbOJ5dPK-eavFR9AWACcIEkWU,102
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/mysql/to_sql.sql,sha256=Zw2wyG9oRoPFp701VoQseHa9dAFFJAgi5RQeX_Nxz5U,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/oracle/to_sql.sql,sha256=7vSqTnwTLKMWv4sD9zFRXgSIJsoYLIqlCcg1JGYbDb0,85
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/postgres/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/pyspark/to_sql.sql,sha256=Zw2wyG9oRoPFp701VoQseHa9dAFFJAgi5RQeX_Nxz5U,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/risingwave/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/snowflake/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/sqlite/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_to_sql_default_backend/trino/to_sql.sql,sha256=FFfQHIDNHEJGvUaWCR5e5pudyF25U6-LvpW6cDuwauc,91
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/athena/out.sql,sha256=JnNh0TPB_ARche4nsb7qxdaL5OgWYiNmT2PZfgv0kMk,8056
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/bigquery/out.sql,sha256=MHzHJ_FkHn3LXJs40tawIxz6htxg1Ml-FIUrJ5OQhzk,7639
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/clickhouse/out.sql,sha256=nftJlHuXeCmbfdv5CddDBxp6prxiZSac6kDBXqP2p4s,2667
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/databricks/out.sql,sha256=MEcDkvNq_F_DSUrreDg3KA7vuu7oxNB0IjU1PMLx6pk,2935
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/datafusion/out.sql,sha256=dkXwzOzGxtHaNE50PWPtO8GousPOKvBP6mnI9QfthJQ,3293
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/duckdb/out.sql,sha256=VerO2Aj1jPWjxlGubbw7dVVszigoX2am7IO_OpkWnJU,2743
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/postgres/out.sql,sha256=bdN0vADyObGSwIsO7Sm_unH5u_EoYN9u36IylRmWF9s,3080
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/pyspark/out.sql,sha256=MEcDkvNq_F_DSUrreDg3KA7vuu7oxNB0IjU1PMLx6pk,2935
ibis/backends/tests/snapshots/test_sql/test_union_aliasing/trino/out.sql,sha256=JnNh0TPB_ARche4nsb7qxdaL5OgWYiNmT2PZfgv0kMk,8056
ibis/backends/tests/sql/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/tests/sql/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/tests/sql/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/tests/sql/__pycache__/test_compiler.cpython-312.pyc,,
ibis/backends/tests/sql/__pycache__/test_select_sql.cpython-312.pyc,,
ibis/backends/tests/sql/__pycache__/test_sql.cpython-312.pyc,,
ibis/backends/tests/sql/conftest.py,sha256=e243pcgPie4_GALRyMPCEVZFO5k8EctJLqlxBtTu8L4,4713
ibis/backends/tests/sql/snapshots/test_compiler/test_agg_and_non_agg_filter/out.sql,sha256=Asf61vdrUgP__VZq6zreFVY3qDAVsE36FopRMc8VD9I,257
ibis/backends/tests/sql/snapshots/test_compiler/test_agg_filter/out.sql,sha256=Pq7M1e5kFReQppzssTqN_miRf1UqFBYfpjmBoKZsr7k,304
ibis/backends/tests/sql/snapshots/test_compiler/test_agg_filter_with_alias/out.sql,sha256=Pq7M1e5kFReQppzssTqN_miRf1UqFBYfpjmBoKZsr7k,304
ibis/backends/tests/sql/snapshots/test_compiler/test_column_distinct/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_column_distinct/decompiled.py,sha256=161CAXEJdTb1ZCbDU1QLqW1qHfwEjIhhpYGp22OxTzU,582
ibis/backends/tests/sql/snapshots/test_compiler/test_column_distinct/out.sql,sha256=V-Q7lYvm_aM4KnV5AQ78i6A51cimGPaB0fCW1tzDwnU,70
ibis/backends/tests/sql/snapshots/test_compiler/test_column_expr_default_name/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_column_expr_default_name/decompiled.py,sha256=5C6ZUqWjprHz1Oyue1ZFq8gFOHENfDczJfnfqngeUpI,128
ibis/backends/tests/sql/snapshots/test_compiler/test_column_expr_default_name/out.sql,sha256=Ez_XytZq9KXyxSpAOi-EuccjWaBJl3X0nDYqnwhojXs,77
ibis/backends/tests/sql/snapshots/test_compiler/test_column_expr_retains_name/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_column_expr_retains_name/decompiled.py,sha256=q9wXBwsn6MtZslazgnOOwgLdw8sUQjn7xLlZD4GzVWU,144
ibis/backends/tests/sql/snapshots/test_compiler/test_column_expr_retains_name/out.sql,sha256=azqfxBA1hqZPA-c2KiDXG3I6cN5XLxXBKvl4h-V-2wM,65
ibis/backends/tests/sql/snapshots/test_compiler/test_count_distinct/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_count_distinct/decompiled.py,sha256=eiU1pOmulvMUGdInGSK_HslUSeW-_Po-kqxLAnmQmvc,651
ibis/backends/tests/sql/snapshots/test_compiler/test_count_distinct/out.sql,sha256=-WpoKA9yyCvcI-DaBY6Yc2M0sUy7SxLuk0jZjIDnXn8,189
ibis/backends/tests/sql/snapshots/test_compiler/test_difference_project_column/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_difference_project_column/decompiled.py,sha256=qLibDOryM4I22N6tuF3hudlaTzH4Ennab25AwaLbiT8,902
ibis/backends/tests/sql/snapshots/test_compiler/test_difference_project_column/out.sql,sha256=I_wlTzd3KXEDnfYiZQ61m1IwQXiCYJ9DTSvoeOKRZKg,441
ibis/backends/tests/sql/snapshots/test_compiler/test_having_from_filter/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_having_from_filter/decompiled.py,sha256=XEVEnu2Ilh1sBfPQ6Oky2n6OkyQ90Fxuhs4WIy-L3x4,240
ibis/backends/tests/sql/snapshots/test_compiler/test_having_from_filter/out.sql,sha256=dxQWl76yD1J63CEsRMcmnC9uHBHavJDoD4UmHNj5BP8,264
ibis/backends/tests/sql/snapshots/test_compiler/test_having_size/out.sql,sha256=6M3ujQdpbZwyCArp1khgom44clvncSVVaErXBtR4fLA,303
ibis/backends/tests/sql/snapshots/test_compiler/test_intersect_project_column/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_intersect_project_column/decompiled.py,sha256=867sWEUWBy7vR8NKCSLsTpx0wNilweUY6O1O_CJeEqQ,907
ibis/backends/tests/sql/snapshots/test_compiler/test_intersect_project_column/out.sql,sha256=vfx3MN4w724Ih3Z9DjnNj_CZRuFIuqL5bkFNb0cd8kk,444
ibis/backends/tests/sql/snapshots/test_compiler/test_multiple_count_distinct/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_multiple_count_distinct/decompiled.py,sha256=EBGMQRRJWKRdi4lGh4uzcXKIPawYstTMh_0_uWY_DqE,737
ibis/backends/tests/sql/snapshots/test_compiler/test_multiple_count_distinct/out.sql,sha256=EW_J7O9x261Jo0Rpl9ARnLTHcHE5PqF_gdIqi_E4BCE,180
ibis/backends/tests/sql/snapshots/test_compiler/test_pushdown_with_or/out.sql,sha256=VYiSB7drfAsMFbM0MJV2s8OPyAZam4QIT-k2BKZKf18,232
ibis/backends/tests/sql/snapshots/test_compiler/test_simple_agg_filter/out.sql,sha256=6hdZz2MwVMMyVFsKu9zwnALc9yptQjIDMtR6omqst8s,236
ibis/backends/tests/sql/snapshots/test_compiler/test_subquery_where_location/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_subquery_where_location/decompiled.py,sha256=ezcLfnb_GRot1A4fugsqxqE2m63Ymy62f8n9vMqSnCk,392
ibis/backends/tests/sql/snapshots/test_compiler/test_subquery_where_location/out.sql,sha256=wGMORj0gJplIMIvFHHA-rgdgr4IYo7N8S6YWLebsMjE,288
ibis/backends/tests/sql/snapshots/test_compiler/test_table_difference/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_table_difference/decompiled.py,sha256=b5x9RQaQILk7siT44Xkqp-EzlBNy9Vr6p3aAc2Ge0Gg,855
ibis/backends/tests/sql/snapshots/test_compiler/test_table_difference/out.sql,sha256=lqT_sYmDVaLIo-b-o03p3Ifq4PAv5SR6u_uqSOApa3s,362
ibis/backends/tests/sql/snapshots/test_compiler/test_table_distinct/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_table_distinct/decompiled.py,sha256=B2BSDiA31dEJYe4QeDyeXkjO-dkL8vDvIrJuwyKpP44,617
ibis/backends/tests/sql/snapshots/test_compiler/test_table_distinct/out.sql,sha256=Akj1FFLVm9xs1fIFTKzML2LEKpdwOCwYJlJcnIbFzf8,88
ibis/backends/tests/sql/snapshots/test_compiler/test_table_drop_with_filter/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_table_drop_with_filter/decompiled.py,sha256=1h-YLe2KTKtBlBDjmPU5_AHEglwNac5_3Fy5z2Hmuxc,657
ibis/backends/tests/sql/snapshots/test_compiler/test_table_drop_with_filter/out.sql,sha256=cp2T_6-PHASS7wFDeYKQU-6YSEPTIp7_NhbG-Yry_wI,332
ibis/backends/tests/sql/snapshots/test_compiler/test_table_intersect/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_table_intersect/decompiled.py,sha256=3QHmaLkyvyAC3boAFrfE7dTLjd2vGNAt_ABQ0EowcMI,854
ibis/backends/tests/sql/snapshots/test_compiler/test_table_intersect/out.sql,sha256=V_7DV6rkpAsSQ2fXbf6lRETFjmrUSpxXxBbWnkpoXps,365
ibis/backends/tests/sql/snapshots/test_compiler/test_union/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_union/decompiled.py,sha256=PFdfJvMBkl8UNJfYis6NQ9MljupOh1lhIkQSnxH0zH0,850
ibis/backends/tests/sql/snapshots/test_compiler/test_union/out.sql,sha256=1vXsKoISlSiAqft2LRRTYU0GvRs84gEfn5-BMNx_-pk,361
ibis/backends/tests/sql/snapshots/test_compiler/test_union_order_by/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_union_order_by/decompiled.py,sha256=T63Sq5cxD3XpeTXyBZU3bzfeCYt2afHsdv46uzf3w-U,124
ibis/backends/tests/sql/snapshots/test_compiler/test_union_order_by/out.sql,sha256=6MxNbuCEPOu94yr0VbXO1Y56YJn6Sjwb6m_hG638-Zo,146
ibis/backends/tests/sql/snapshots/test_compiler/test_union_project_column/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_compiler/test_union_project_column/decompiled.py,sha256=nzrAx3toKrqsEWndLdZAEtOKdO6kq-eaHMTUEWAyz0k,861
ibis/backends/tests/sql/snapshots/test_compiler/test_union_project_column/out.sql,sha256=vplcaYBvQvIPtnKHJz4GstLcMQY-d5V1IShAtg2lOSk,444
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_count_joined/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_count_joined/decompiled.py,sha256=jNyEMaFnWf0t6KciWefFNoNv5hSdVr_OFSI4JyAmZCA,663
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_count_joined/out.sql,sha256=oUEsb8iD15iEX0-afLO11DsPro36dqYlMGuSoRNYUsw,290
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_having/explicit.sql,sha256=vvk9dS_hQSscY8-jbLR8FecYu3rLvHqt27PNE8x9QlI,150
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_having/inline.sql,sha256=aslCKfiEVa4Gnk9ybEoKN-G8jpmuI73V0-f-0LpoH08,226
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_projection_alias_bug/out.sql,sha256=fA6qFFeXYCTXGumzUaPqsNdXW8RbJWAqCRvO3924JZ4,267
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_projection_subquery/agg_filtered.sql,sha256=VLnrloH3PnYcm9qMHf5MNvGtPc6-s-rgXsOxiaY9U-k,349
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_projection_subquery/agg_filtered2.sql,sha256=1b9kuVU4iyR8HDKqDkzOfze_oSuxNqlh82MTNdtqTRg,371
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_projection_subquery/filtered.sql,sha256=qd5CkDoRDQ185lorgbjMHMB6KxriZuvnQJZXRR6xTqM,235
ibis/backends/tests/sql/snapshots/test_select_sql/test_aggregate_projection_subquery/proj.sql,sha256=PcNnWKhAiaeMOr9EP1_AaDrMjIZAtvj750f6_GxtKTU,214
ibis/backends/tests/sql/snapshots/test_select_sql/test_anti_join/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_anti_join/decompiled.py,sha256=9N83BTSVYJ9A0XCM6sOOWN1OTcIdcdLLE0HORsSol74,371
ibis/backends/tests/sql/snapshots/test_select_sql/test_anti_join/out.sql,sha256=6PN3rycTigFadoQq35mfiT1TGmouUYu05X9Ueyds7UY,145
ibis/backends/tests/sql/snapshots/test_select_sql/test_bool_bool/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_bool_bool/decompiled.py,sha256=XmTgf9GadhbJQcQ8YywA0kv0ruAZN5LgTs-hPJvN5L0,201
ibis/backends/tests/sql/snapshots/test_select_sql/test_bool_bool/out.sql,sha256=wCz1RvQHo5aPRAli8pK0SSL3-nEQh1I6KSqSJzq40sw,91
ibis/backends/tests/sql/snapshots/test_select_sql/test_bug_duplicated_where/out.sql,sha256=LEARvk1XZ5Vc8GXFudkTsTH7wIECJwM_999keYsDdvI,512
ibis/backends/tests/sql/snapshots/test_select_sql/test_bug_project_multiple_times/out.sql,sha256=AUkwV8suzIy3GZLAY2OcVDB01L_UZ64_Lf3jNE1Buso,1042
ibis/backends/tests/sql/snapshots/test_select_sql/test_case_in_projection/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_case_in_projection/decompiled.py,sha256=IzMp7rXHRvCU6MzQzcLRYOX-Bd2nVSMhiITt-1xesc0,888
ibis/backends/tests/sql/snapshots/test_select_sql/test_case_in_projection/out.sql,sha256=dkQ1_4T5m71ohgCx6tgSRvszUsFjbRRrydJAdS8VcC8,374
ibis/backends/tests/sql/snapshots/test_select_sql/test_chain_limit_doesnt_collapse/result.sql,sha256=ffm5WYg8gE51X_iFdLLbwlheiP3ys74hNRq6gB3i-n4,500
ibis/backends/tests/sql/snapshots/test_select_sql/test_complex_union/result.sql,sha256=gxW3MtQv0y1YCgRXz1E53ZyJsaL53eAyg9rXML7FhQo,261
ibis/backends/tests/sql/snapshots/test_select_sql/test_double_nested_subquery_no_aliases/out.sql,sha256=n7wEe6Q5oYwQ5bqiRDehqeSvcdR47l7Z7vsWthRWXyY,374
ibis/backends/tests/sql/snapshots/test_select_sql/test_endswith/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_endswith/decompiled.py,sha256=FtETZIOVQtRITrQGNg-CJfkHJD_VplF0OKWnuvFYT9w,202
ibis/backends/tests/sql/snapshots/test_select_sql/test_endswith/out.sql,sha256=DhQLrKHbiUsYJFrvOFLJPaKp2Rk-AwTe2Cgpf4lMopQ,67
ibis/backends/tests/sql/snapshots/test_select_sql/test_exists_subquery/out.sql,sha256=RSn4fiTbFr7jIKsNqzxepmDKGnSl05L6gkYt3vVopDo,131
ibis/backends/tests/sql/snapshots/test_select_sql/test_filter_inside_exists/out.sql,sha256=v4Cff8PeEdxP35R3xHOHy41Cb0BIj3dMe71p27oynNA,177
ibis/backends/tests/sql/snapshots/test_select_sql/test_filter_predicates/out.sql,sha256=FTwZTAPaZjB3ABpEwg_AY7trK5TRKL75DdBQmeeWAE0,161
ibis/backends/tests/sql/snapshots/test_select_sql/test_filter_self_join_analysis_bug/result.sql,sha256=NQK6aYQRvPuPuosgEujvDKEGnHZGff3NkBKAkEmAFsk,419
ibis/backends/tests/sql/snapshots/test_select_sql/test_filter_subquery_derived_reduction/expr3.sql,sha256=Byi2PisipAD7yBkESJbZwb_vr8iEJxEVcFZKFkEk_2E,249
ibis/backends/tests/sql/snapshots/test_select_sql/test_filter_subquery_derived_reduction/expr4.sql,sha256=fSeK-VjdUzR6cjZDp3xcDpSAoAb1cfhf-XXd37UlhIs,287
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct-filter-order_by/out.sql,sha256=iyBCmCxh2l1OFEhhO2hr9bbLnH64g-SDdGYKPJWUTlw,134
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct-filter/out.sql,sha256=vmBsrk0Fcu6w9cL3Bv_KqOI_-QBrN-5GT_8xeFH8OyI,110
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct-non-trivial-select-distinct/out.sql,sha256=3aeisVIjq8VvnxfySsYkPFARKiZpGn_Mhrr6lxr-XtM,173
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct-non-trivial-select/out.sql,sha256=0PlEitwv5iKwJ2GbUYFZ_pKmAUv5pS7Gi0RxaUkeALs,164
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct-select-distinct/out.sql,sha256=CT9hZRhfcjbvMGbSBaP2sFgXnovsPhIOQWQSH1Nz1D4,162
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct-select/out.sql,sha256=af07IyOtiJJ2lUUx_xZA4sxBtppxLf8toPEPE533kKI,153
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/distinct/out.sql,sha256=aDuVc_tmehNshlQpVR9geIFm3me4vhPY45rbjTLU6wQ,92
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/non-trivial-select-distinct/out.sql,sha256=3BWq4UR2ojjWEFpKGU51OKlOTVIqaC1w3KL4Ke2x_Ko,91
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/order_by-distinct-drop/out.sql,sha256=1dxnbWaQl0BwgbzmIeaUaAbpky7NMGyWUPSGWgt4Wm8,181
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/order_by-distinct/out.sql,sha256=TW03IVSvCun5CDnLUPuXz_L5KIlYESZUl8a_8Hl1ys0,116
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/order_by-drop-distinct/out.sql,sha256=t566nzRLV3rxtW-VQTuJvAxiwzKmPhcE2JRWBh1vA_A,104
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_distinct/select-distinct/out.sql,sha256=YmqKgOqrPj3XT7kVWa8EX0NYZJRz-4Vxo24U6y9hAJA,80
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_projections/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_projections/decompiled.py,sha256=KrTIGCZxhBhwOT_m8cZR1vEc-PJIzWO2fC6AUoaFkco,248
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_projections/project.sql,sha256=X1pONPKx2MZjqbxBVqdosUU2ir_Qw6nUDM2_TjiSKGk,131
ibis/backends/tests/sql/snapshots/test_select_sql/test_fuse_projections/project_filter.sql,sha256=vBdgnaRJTbDRlckhdQpOGNC0VB-6YMZFFNbnrzdpyS8,156
ibis/backends/tests/sql/snapshots/test_select_sql/test_identifier_quoting/out.sql,sha256=MVACiqIowp2zyNQOZ94muVjp2M40DcgDYJU0F64fJZE,79
ibis/backends/tests/sql/snapshots/test_select_sql/test_incorrect_predicate_pushdown/result.sql,sha256=-0Ih6Q7Ps-AbMdR3BQH13GCZ-aooIdOwa0yZtl3UWHc,80
ibis/backends/tests/sql/snapshots/test_select_sql/test_incorrect_predicate_pushdown_with_literal/result.sql,sha256=TTagpPrMUa_3ladOBHGUV9iTGHVJqKDjREJDW0Bs5fI,48
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_between_joins/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_between_joins/decompiled.py,sha256=JzOE6_veOGwKFOMJb2uQLKH6Mj4HSC2zE-vHok5KsDc,1234
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_between_joins/out.sql,sha256=d30PDBQodfNW1Sg1vOsV82PVt29WL5SfiSC12ihb5Q4,403
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_filtered_tables_no_pushdown/out.sql,sha256=03_D9foWJLBKJi0Xmn13MeJxPJOM0VjeYtDPFCJRYpw,393
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_just_materialized/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_just_materialized/decompiled.py,sha256=4apJmdHnFXJb0Ho-eQIOv8xC-69nILuu-KkW10HbDeg,1373
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_just_materialized/out.sql,sha256=o9SILFrKotw_Q2Aadq6MXjRA-EraDTcDGoy-VeNQJLM,490
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_projection_subquery_bug/out.sql,sha256=AZY0h2XSWRfInH1_SZo1eeqRtKsvO1uw6CTKC2TmCwo,430
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_with_conditional_aggregate/result.sql,sha256=cII2V19LdbvAjgrLR1oy-BivQl_xI8rP8gcB1VdXJRU,445
ibis/backends/tests/sql/snapshots/test_select_sql/test_join_with_limited_table/out.sql,sha256=DFZOvp4-X7hroY14_Fd1v4xgqYWF4Yix7Y9FDh7JGm4,192
ibis/backends/tests/sql/snapshots/test_select_sql/test_limit_cte_extract/out.sql,sha256=kD3klZUkCYIsnoloX4hg6nGp3ab-bsm8f-7X7ezk0AM,397
ibis/backends/tests/sql/snapshots/test_select_sql/test_limit_with_self_join/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_limit_with_self_join/decompiled.py,sha256=RSPngBNCz5CbKmK_lbljrr_NvebMEa972rmJWI1HMP8,1983
ibis/backends/tests/sql/snapshots/test_select_sql/test_limit_with_self_join/out.sql,sha256=rUhWq2qf0b8lg5S7fzQEdNLJJWb_KgB8cIf5uxykv1g,1051
ibis/backends/tests/sql/snapshots/test_select_sql/test_loj_subquery_filter_handling/out.sql,sha256=212Qxaq0Vk_W-njLnArllxkjvEuD_M_BQu59QOUUzY8,345
ibis/backends/tests/sql/snapshots/test_select_sql/test_multiple_joins/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_multiple_joins/decompiled.py,sha256=pIcTelFzMX0hrtKfemr3z0F6RKtCEsmdILsSCyK9Rdw,544
ibis/backends/tests/sql/snapshots/test_select_sql/test_multiple_joins/out.sql,sha256=bq5UEQlIQHmhILdX7HuBYn2-Hb7itR45a2vtlZYVtm8,247
ibis/backends/tests/sql/snapshots/test_select_sql/test_multiple_limits/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_multiple_limits/decompiled.py,sha256=E8DukScq9atLdffqWLyO1A0HfuuLEIRfBRbPzLp525E,552
ibis/backends/tests/sql/snapshots/test_select_sql/test_multiple_limits/out.sql,sha256=db4LzSLygi80NQT7z-wZ6e3IwxGrVgLWQqXdSjrkeso,99
ibis/backends/tests/sql/snapshots/test_select_sql/test_nameless_table/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_nameless_table/decompiled.py,sha256=ovbuXiR7cZo-tV4Y3S2einJszrx1AJ2esz3WLdMXlLE,72
ibis/backends/tests/sql/snapshots/test_select_sql/test_order_by_on_limit_yield_subquery/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_order_by_on_limit_yield_subquery/decompiled.py,sha256=PJKd90eK-l3-TC0jiT946gdewALx1iwHbL-iB9e5Gr4,696
ibis/backends/tests/sql/snapshots/test_select_sql/test_order_by_on_limit_yield_subquery/out.sql,sha256=NnURbssobY9iKIrA0YDrPvcU1eAdW0FbgdT6ON2lmWw,180
ibis/backends/tests/sql/snapshots/test_select_sql/test_projection_filter_fuse/out.sql,sha256=bCY91eLPw3khbDY1g0l4NzUPfu8pTRpYyhjZlqfMPpQ,81
ibis/backends/tests/sql/snapshots/test_select_sql/test_scalar_subquery_different_table/out.sql,sha256=9uWRS3P5WMcKazvyFmmAk1uDzOdDcAIALgwjdaTePbE,120
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/agg_explicit_column/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/agg_explicit_column/decompiled.py,sha256=gnbMEhSwwZNGj9aqeoTRdEmGCKyrSXXsNa5MaD6c0ig,213
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/agg_explicit_column/out.sql,sha256=jrr9jsnCYSwOxSFxvX7RDLaxFnjh7Xb4WixgC88hVPI,84
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/agg_string_columns/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/agg_string_columns/decompiled.py,sha256=FrUJkWn7RphgBPkH4zeFOn4oZuf9euw9KzNdTCdWSvM,227
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/agg_string_columns/out.sql,sha256=txz61sVVzz1E1dtiGmq-Xp0ZzT4-oKHkalYEF3o4Kns,106
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/aggregate_table_count_metric/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/aggregate_table_count_metric/decompiled.py,sha256=GOtGJO99BzFHK0JLy6ILKDg1X3k-P-ZLhHLia1XSyn8,161
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/aggregate_table_count_metric/out.sql,sha256=n7rzy9B4C1iRqxCYiY86BhOgC7hfYcz8vMUKPy6hiPc,60
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/filter_then_limit/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/filter_then_limit/decompiled.py,sha256=ce2R6R9z12ixIpg1fmxDOLtaykbU74TVeUjlwAm7RGs,185
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/filter_then_limit/out.sql,sha256=WhFD6jbLGj5wUAFnyTE5Z4nVyM0ryyCKDitZJifQWm8,61
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_simple/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_simple/decompiled.py,sha256=OfbbQ-R3X2jOp6epFHfv-bkvR3f5dtvf8ZYJDZjCMlk,163
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_simple/out.sql,sha256=sOeHPoWtlurB6ts8K4sUTwvyAs6_1XA-3XB-6wEkyVU,40
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_then_filter/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_then_filter/decompiled.py,sha256=2WRNwd9qmLOrHCdRStnbqKkfL_YPnfSDCl9nnvT3b18,199
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_then_filter/out.sql,sha256=5GL1GuF6bu3NQ51FXeHMFjw5WDVR-zJ-MskogTPm-w0,97
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_with_offset/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_with_offset/decompiled.py,sha256=2Hd5TkaqnDDoMGVyE_zT0J6smpwJvC-arpxnmHI2qkE,173
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/limit_with_offset/out.sql,sha256=SFuRxG5plP6a9HUMTThNxAMa9yl70bY2seZG5y_yN6o,49
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/mixed_columns_ascending/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/mixed_columns_ascending/decompiled.py,sha256=2-jE9QANeX8E_AdAra5eperDFshT64txUU_qN5CNw64,195
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/self_reference_simple/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/self_reference_simple/decompiled.py,sha256=tvAjcV0PjBhI8ynErf0kuN7AIfnZ-HXlgHGJULWNlrA,138
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/single_column/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/single_column/decompiled.py,sha256=98PVuQjXIvuTVq7tTB_7noY-7qHSTH2L5pbg_UHik1Y,177
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/single_column/out.sql,sha256=oEzpd2YFoYa3-U-W_OYhwFhtaQz26YRzrRB4vN86-8A,55
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/test_physical_table_reference_translate/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/test_physical_table_reference_translate/decompiled.py,sha256=7iSF2_IidRXWU4IT78WQQEJ-ZDeHyp7lUHKFoza94T8,328
ibis/backends/tests/sql/snapshots/test_select_sql/test_select_sql/test_physical_table_reference_translate/out.sql,sha256=aOlBDP0oCcOZDiu0OBCFEUpk_f3X2sQDOazsXMwYk-4,26
ibis/backends/tests/sql/snapshots/test_select_sql/test_self_join_subquery_distinct_equal/out.sql,sha256=y3geIfbVJklhCzvxy2xTAQFoq5eaNEbHlRY2Km6Dv6o,485
ibis/backends/tests/sql/snapshots/test_select_sql/test_semi_join/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_semi_join/decompiled.py,sha256=ek6piN4ksf20nLgk3qL2n1FKKi-xEQXn0ifFahia6MU,371
ibis/backends/tests/sql/snapshots/test_select_sql/test_semi_join/out.sql,sha256=9UBJUVc3fpiYSTOqLgVaEYfZ0D2GGgcj0zIEGzk7Uck,145
ibis/backends/tests/sql/snapshots/test_select_sql/test_simple_joins/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_simple_joins/decompiled.py,sha256=VHPnV5jbwxPpLOl7iuyplKvR3jN_wl4kh8NLbCCet3g,406
ibis/backends/tests/sql/snapshots/test_select_sql/test_simple_joins/inner.sql,sha256=2TioL8AZbNdp8boOIuP7gw2NeRYKUHAOkptKstie94M,146
ibis/backends/tests/sql/snapshots/test_select_sql/test_simple_joins/inner_two_preds.sql,sha256=VeUqnzYMZCotkzamRT5sx6UDtuBuBo5f-23aiJcWR4w,180
ibis/backends/tests/sql/snapshots/test_select_sql/test_simple_joins/left.sql,sha256=YGdcBUoOQG4N-pkB74Xyt7mhhkUmJlbgQEgnKN7NYog,151
ibis/backends/tests/sql/snapshots/test_select_sql/test_simple_joins/outer.sql,sha256=Am7b7T620tZT5EOB733lYaIkSUhjR-jno-3x6ohncsI,151
ibis/backends/tests/sql/snapshots/test_select_sql/test_sort_then_group_by_propagates_keys/result1.sql,sha256=0bCGsW7F43c39QeL9iJwJWbPrlbc-uNelA-STTZ_nGI,141
ibis/backends/tests/sql/snapshots/test_select_sql/test_sort_then_group_by_propagates_keys/result2.sql,sha256=UYi8a7ofqcW7CxXY9ZbHt_A5ce9V1fB9E5buihQ3s04,141
ibis/backends/tests/sql/snapshots/test_select_sql/test_startswith/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_startswith/decompiled.py,sha256=ktM9-MmfHS2yYpMh8GNrOVY0KfVaMmE_o8f-JlLLqys,204
ibis/backends/tests/sql/snapshots/test_select_sql/test_startswith/out.sql,sha256=RaHVLvDXqChwXi8FlyWAtRDye8KYpU47HUOvGcmZrK8,72
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_factor_correlated_subquery/out.sql,sha256=WYraOFjZT32juUM7aJX8s9nS-hrH833KchKgUcfZ_-E,843
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_in_filter_predicate/expr.sql,sha256=SZU2d4LQusSrm4cQ4RMbIYPMzVipVKBs5BFuJQg5WN0,125
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_in_filter_predicate/expr2.sql,sha256=oYrSfpdMaLdACHUdgxLIcfg6bn5jgylCwnFc5hRuk1s,217
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_in_union/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_in_union/decompiled.py,sha256=nK4lMli0x3eE9sarorGoF9_SUPZVlbfv5IGRxI7Md6Y,631
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_in_union/out.sql,sha256=f5BaC1RwpoHc5WvEReI56jJp18TheoNgIeA95kWvRtg,342
ibis/backends/tests/sql/snapshots/test_select_sql/test_subquery_used_for_self_join/out.sql,sha256=227titIhsi7soYRAoHIZ2miT7n8-prXk6qfaXLZzzCU,512
ibis/backends/tests/sql/snapshots/test_select_sql/test_topk_analysis_bug/out.sql,sha256=zOoM14SdRdkYZWy85f-tcQCb4Zv3xjEZAje1UkjfklY,567
ibis/backends/tests/sql/snapshots/test_select_sql/test_topk_operation/e1.sql,sha256=qH6nAWRvVYRPBmFl2qSFnLJ7iFvSvdHUZb0tWMpXXTQ,338
ibis/backends/tests/sql/snapshots/test_select_sql/test_topk_operation/e2.sql,sha256=T6e_B-WAdVb8c_7e9YDe8t4hrjZAR3wTnBN1M_14cz8,336
ibis/backends/tests/sql/snapshots/test_select_sql/test_topk_predicate_pushdown_bug/out.sql,sha256=SuOqVlCfi4-qxafHh137QhIFWXnNFfWaTgYnJ0d7dDY,1139
ibis/backends/tests/sql/snapshots/test_select_sql/test_topk_to_aggregate/out.sql,sha256=rDG4xDGR8RZXNuckvsdQynZS-F19e6VNo7dzXeJxOJs,188
ibis/backends/tests/sql/snapshots/test_select_sql/test_tpch_self_join_failure/out.sql,sha256=Ro82KkgPJ8GBnLpa9fKu0O0dpdUxAzwX9BGPWBqfoWE,824
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_analyze_scalar_op/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_analyze_scalar_op/decompiled.py,sha256=2Rg8MDSGj5CQnDt--e5onn5dZSoj3qEob-guEY9VdMI,754
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_analyze_scalar_op/out.sql,sha256=YdwU0M9CDn1k8mli6emW5NlaP7xoOQ3eHzKNndXYsqA,313
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_no_pushdown_possible/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_no_pushdown_possible/decompiled.py,sha256=684sM_InKWcYpBg_jSbsGts4yYDaHPWNACksrUsRnAE,481
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_no_pushdown_possible/out.sql,sha256=bowfN9YoWDq0ET3T1GuYmkUMksm9k8Ys-lhxVJqN_2k,254
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_with_between/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_with_between/decompiled.py,sha256=NgQg3qGd1ky7cmIjYAgmCTjsHk26EiwwRK11W2xPO-E,440
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_with_between/out.sql,sha256=o4qo7YSX_CImF98EkZHo5Ey5KC9bHDT8AArpsM7u49Q,84
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_with_join/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_with_join/decompiled.py,sha256=LJ6OpZuzb2gkE5GS9cw4EJV0cbLoMo7va_gGjsjdynE,475
ibis/backends/tests/sql/snapshots/test_select_sql/test_where_with_join/out.sql,sha256=HFix-ib0t3xXQWKe9yoH84t0TbIL-OVBdf0kCwWjktU,274
ibis/backends/tests/sql/snapshots/test_sql/test_aggregate/having_count/out.sql,sha256=aslCKfiEVa4Gnk9ybEoKN-G8jpmuI73V0-f-0LpoH08,226
ibis/backends/tests/sql/snapshots/test_sql/test_aggregate/having_sum/out.sql,sha256=vvk9dS_hQSscY8-jbLR8FecYu3rLvHqt27PNE8x9QlI,150
ibis/backends/tests/sql/snapshots/test_sql/test_aggregate/single/out.sql,sha256=jrr9jsnCYSwOxSFxvX7RDLaxFnjh7Xb4WixgC88hVPI,84
ibis/backends/tests/sql/snapshots/test_sql/test_aggregate/two/out.sql,sha256=txz61sVVzz1E1dtiGmq-Xp0ZzT4-oKHkalYEF3o4Kns,106
ibis/backends/tests/sql/snapshots/test_sql/test_between/out.sql,sha256=DMWEw_9dV4--h7TdOyuWKxeF2G5AewAoKz29-leWhmo,87
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/add-dateintervalD/out.sql,sha256=P9QwVYxixr_iWOguGas6M_wyXqLT5hIUeoHoMf4GHkg,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/add-int8/out.sql,sha256=P9QwVYxixr_iWOguGas6M_wyXqLT5hIUeoHoMf4GHkg,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/add-interval/out.sql,sha256=P9QwVYxixr_iWOguGas6M_wyXqLT5hIUeoHoMf4GHkg,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/add-timeinterval/out.sql,sha256=P9QwVYxixr_iWOguGas6M_wyXqLT5hIUeoHoMf4GHkg,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/add-timestampinterval/out.sql,sha256=P9QwVYxixr_iWOguGas6M_wyXqLT5hIUeoHoMf4GHkg,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/and_-bool/out.sql,sha256=zqNa1OJ8Mb5Ps9KFP8NGbt_Ha7U-TGPyBMX4tr0_B4c,188
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/and_-int8/out.sql,sha256=3f1DXwWZVmmOmrLxHcSgr-8wRh-6ZqECHCaNPYP8OE4,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/lshift-int8/out.sql,sha256=5RjLi3vlXM1Vi9P2cauekBUsnWefVW0mti9zxogcCT8,194
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/mod-int8/out.sql,sha256=Qat5XE4hUoD8u83hHMT58XHOZ4UGjH_BNt-_YbOWXrU,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/mul-int8/out.sql,sha256=Iq0zLgy2_ygZ_ZR6tX1osnekIXJ4QWGJE9yDtLGjFa4,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/mul-intervalint8/out.sql,sha256=Iq0zLgy2_ygZ_ZR6tX1osnekIXJ4QWGJE9yDtLGjFa4,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/or_-bool/out.sql,sha256=bY6aeJ5XsCO7dD2_P1D5YEIHzenUfl3iqIF8I8naAEY,184
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/or_-int8/out.sql,sha256=-2P6p176YonVzTTuaZNdr7-taSV42bfCWnmMBS8X7EI,180
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/pow-int8/out.sql,sha256=g4g3zZ3yLC5fFdq9IS77Ta-K26l6h16RjybK1NG2sjc,214
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/rshift-int8/out.sql,sha256=YOohNUweW7YBo-4cgXykcg7z4ESWoHUPExnSJARzguY,194
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/sub-dateintervalD/out.sql,sha256=NAO_BZ4P_-7xU-tc2FdTDoE1WvqO5Da2KtAwrHeUtwM,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/sub-int8/out.sql,sha256=NAO_BZ4P_-7xU-tc2FdTDoE1WvqO5Da2KtAwrHeUtwM,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/sub-interval/out.sql,sha256=NAO_BZ4P_-7xU-tc2FdTDoE1WvqO5Da2KtAwrHeUtwM,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/sub-timeinterval/out.sql,sha256=NAO_BZ4P_-7xU-tc2FdTDoE1WvqO5Da2KtAwrHeUtwM,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/sub-timestampinterval/out.sql,sha256=NAO_BZ4P_-7xU-tc2FdTDoE1WvqO5Da2KtAwrHeUtwM,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/truediv-int8/out.sql,sha256=WXQBdcGr3jYiKMTe6da15OTJY2hq8A4C00zbiKaNMK8,190
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/xor-bool/out.sql,sha256=1hDmsYDWwLPS6FbzfNoVKCAuWNbwzcbpvUn0CFKZEn4,470
ibis/backends/tests/sql/snapshots/test_sql/test_binop_parens/xor-int8/out.sql,sha256=Axl2ixH_pg0GN5wDt-bre30hiJPWIi21IrrnHP6nt1Y,196
ibis/backends/tests/sql/snapshots/test_sql/test_binop_with_alias_still_parenthesized/out.sql,sha256=BcEjE9OLL1PfmloKVCcRH6GjMxglU8iWC9CGhpmtdFI,73
ibis/backends/tests/sql/snapshots/test_sql/test_boolean_conjunction/and/out.sql,sha256=RKaBhuOg5DzPn486k_KeSL2X5WfIhrfCf4Qo4kGPbvY,120
ibis/backends/tests/sql/snapshots/test_sql/test_boolean_conjunction/or/out.sql,sha256=aJFVNy-9Hy-KaRXAnwO4K5ZVcLd2d3914vuQ8IpTYvw,119
ibis/backends/tests/sql/snapshots/test_sql/test_coalesce/out.sql,sha256=usggO6tZhTLAycu_qLRIKMBWi_FhkWIyuC8sMsNFvJI,172
ibis/backends/tests/sql/snapshots/test_sql/test_comparisons/eq/out.sql,sha256=f4UBmRpqE6DxvxcPiVvTmCBnCNWkMoCzpyBc0oaEs20,74
ibis/backends/tests/sql/snapshots/test_sql/test_comparisons/ge/out.sql,sha256=T_DSdQyiinIv78qKGKJHvtqw0j-D9ApnCFkHKqAeqcE,75
ibis/backends/tests/sql/snapshots/test_sql/test_comparisons/gt/out.sql,sha256=5Ekz8CH3lmrC7af-s5CT_lCmlnk1Aw6zBf-MM2AIuuw,74
ibis/backends/tests/sql/snapshots/test_sql/test_comparisons/le/out.sql,sha256=HEcRDWnJOuVimnfEIrG1yj1h8NQjyfY_0FXARDWR8po,75
ibis/backends/tests/sql/snapshots/test_sql/test_comparisons/lt/out.sql,sha256=vZTKZqB8_ttbiP-r9qpyrMBqdKWgb_rJHz9IRR4kFaM,74
ibis/backends/tests/sql/snapshots/test_sql/test_comparisons/ne/out.sql,sha256=NSZVdTLEsMM4-HoDaT_6MJT3RRit03L6jCjVDENg54s,75
ibis/backends/tests/sql/snapshots/test_sql/test_cte_factor_distinct_but_equal/out.sql,sha256=c78XdKk1qTdZs0oYWHkNZKF7A_cT9eHLMVJEvhTeL60,291
ibis/backends/tests/sql/snapshots/test_sql/test_distinct/count_distinct/out.sql,sha256=WTCCVqyLFng9aa-Hd_K9TokHMTMwUiR3grDaO6qQheI,87
ibis/backends/tests/sql/snapshots/test_sql/test_distinct/group_by_count_distinct/out.sql,sha256=YwpkLk0eHF401UEjkERmdJ-h1MIo8XHwG7EdpV58wJY,121
ibis/backends/tests/sql/snapshots/test_sql/test_distinct/projection_distinct/out.sql,sha256=Akj1FFLVm9xs1fIFTKzML2LEKpdwOCwYJlJcnIbFzf8,88
ibis/backends/tests/sql/snapshots/test_sql/test_distinct/single_column_projection_distinct/out.sql,sha256=V-Q7lYvm_aM4KnV5AQ78i6A51cimGPaB0fCW1tzDwnU,70
ibis/backends/tests/sql/snapshots/test_sql/test_distinct/table_distinct/out.sql,sha256=S-Qj1exarFuWZh2H2z7tZO6nkOXl6yEPktK_zpdt9Lc,54
ibis/backends/tests/sql/snapshots/test_sql/test_double_order_by/out.sql,sha256=OoLnwXZAwGYp-KUCG-RGucd6xfVnapfMPE_lCp-rXtg,68
ibis/backends/tests/sql/snapshots/test_sql/test_double_order_by_deferred/out.sql,sha256=9wDHR_m0NlxiN10Lz8On3SNvB-0JzORMzkrOAdWXibQ,84
ibis/backends/tests/sql/snapshots/test_sql/test_double_order_by_different_expression/out.sql,sha256=5FStDGbEYB6yL56T9LXu6I7TQiAaW3vOqvl7ADlqXgE,104
ibis/backends/tests/sql/snapshots/test_sql/test_double_order_by_same_column/out.sql,sha256=9wDHR_m0NlxiN10Lz8On3SNvB-0JzORMzkrOAdWXibQ,84
ibis/backends/tests/sql/snapshots/test_sql/test_exists/e1.sql,sha256=RBd5W4JxXVrl8VfODviVB782yg-M6H1GLITfQAcB7PI,137
ibis/backends/tests/sql/snapshots/test_sql/test_exists/e2.sql,sha256=aHKCOMUmAf1Pba6cLG68sf_WgcYZuekZTAmIxvdcYEE,197
ibis/backends/tests/sql/snapshots/test_sql/test_filter_group_by_agg_with_same_name/out.sql,sha256=6GMqyo1D0M79qsv4Qgtgi_a9PdJKQrLizdrVBc8iOg8,166
ibis/backends/tests/sql/snapshots/test_sql/test_gh_1045/out.sql,sha256=RoNTJo38SsMWCVFN8QrP8kuY2SESo6OTYhSTT5icdko,704
ibis/backends/tests/sql/snapshots/test_sql/test_isnull_notnull/isnull/out.sql,sha256=cBV96-2_tkE2jJDOpkUK1ugBp8zb6c3mtNukfm0Pibw,78
ibis/backends/tests/sql/snapshots/test_sql/test_isnull_notnull/notnull/out.sql,sha256=QisnPMmGeL7Flr1VVC8u-cK8A-eIdrWnopLQyDbsqBI,82
ibis/backends/tests/sql/snapshots/test_sql/test_join_just_materialized/out.sql,sha256=o9SILFrKotw_Q2Aadq6MXjRA-EraDTcDGoy-VeNQJLM,490
ibis/backends/tests/sql/snapshots/test_sql/test_joins/inner/out.sql,sha256=m3anbOnqZ_gYY6_9EM6Zv3KV2rU3kOKTMn-MjMjF6FI,250
ibis/backends/tests/sql/snapshots/test_sql/test_joins/inner_select/out.sql,sha256=GZrfQHwYhWVcDdPiCadFiPzyfaiEx2fjDhUGShaJKT8,191
ibis/backends/tests/sql/snapshots/test_sql/test_joins/left/out.sql,sha256=1Q5GOaNHXe9nqAE9vHXo941efdKIfhpXWc3ta0wAVn0,255
ibis/backends/tests/sql/snapshots/test_sql/test_joins/left_select/out.sql,sha256=2mFoUJirzn8hqsfnv29DjdFJeIQmd6qrgx6zX_VSajw,196
ibis/backends/tests/sql/snapshots/test_sql/test_joins/outer/out.sql,sha256=mj2F7ggrg49IJ_ef-kZQvCDMNokSyInmLFGDmxmxaLk,255
ibis/backends/tests/sql/snapshots/test_sql/test_joins/outer_select/out.sql,sha256=suIrgIzPWOdz4uXUApCHBboHlADRjwLfizIKKzLzvp8,196
ibis/backends/tests/sql/snapshots/test_sql/test_limit/expr_fn0/out.sql,sha256=sOeHPoWtlurB6ts8K4sUTwvyAs6_1XA-3XB-6wEkyVU,40
ibis/backends/tests/sql/snapshots/test_sql/test_limit/expr_fn1/out.sql,sha256=SFuRxG5plP6a9HUMTThNxAMa9yl70bY2seZG5y_yN6o,49
ibis/backends/tests/sql/snapshots/test_sql/test_limit_filter/out.sql,sha256=WhFD6jbLGj5wUAFnyTE5Z4nVyM0ryyCKDitZJifQWm8,61
ibis/backends/tests/sql/snapshots/test_sql/test_limit_subquery/out.sql,sha256=5GL1GuF6bu3NQ51FXeHMFjw5WDVR-zJ-MskogTPm-w0,97
ibis/backends/tests/sql/snapshots/test_sql/test_lower_projection_sort_key/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_sql/test_lower_projection_sort_key/decompiled.py,sha256=FZ3K0xyI3-V_fRFBcaPdL5hbSPC2hNWiOr9zKZ6kzhg,516
ibis/backends/tests/sql/snapshots/test_sql/test_lower_projection_sort_key/out.sql,sha256=z9F1zBZkazMi5dE3jyiU9ttmZh5IRdVeCYoNI9JQ9dw,343
ibis/backends/tests/sql/snapshots/test_sql/test_multi_join/out.sql,sha256=Jcb8kLMV3qYTxgff37BUGrq5C-qK_uPZ0MsSUvtslXY,330
ibis/backends/tests/sql/snapshots/test_sql/test_mutate_filter_join_no_cross_join/out.sql,sha256=u3qwQQ9k4Hu6Z0T6ENBnLFba7N7mFQ-9FkVOlYoCHRU,65
ibis/backends/tests/sql/snapshots/test_sql/test_named_expr/out.sql,sha256=soNe2eIJkijC5O-6AH6C1wCS8ZzqcYOcr_u7lqUAyuQ,74
ibis/backends/tests/sql/snapshots/test_sql/test_negate/out.sql,sha256=RuExjBG66aLDicUhWxHN2Jp2po4D_FPjwkW-ah-BaQc,88
ibis/backends/tests/sql/snapshots/test_sql/test_no_cart_join/out.sql,sha256=IAM67Zy-C6jLFqjs_J4tRheCBTh9WD9FHDkz7WlNRzE,1125
ibis/backends/tests/sql/snapshots/test_sql/test_no_cartesian_join/out.sql,sha256=trODlHcsvK6VHem6cAXGjdGUfWpgZrWvJ82GJwP3MEc,1406
ibis/backends/tests/sql/snapshots/test_sql/test_no_cross_join/out.sql,sha256=XohKzKxQvxMciquHubKh_TsAaofzFzzZYQUODczy4r8,319
ibis/backends/tests/sql/snapshots/test_sql/test_not_exists/out.sql,sha256=y1GC7bjG_ZLR0j6FD3OhkAiIqmrncWjolDyprDWRahU,163
ibis/backends/tests/sql/snapshots/test_sql/test_order_by/column/out.sql,sha256=oEzpd2YFoYa3-U-W_OYhwFhtaQz26YRzrRB4vN86-8A,55
ibis/backends/tests/sql/snapshots/test_sql/test_order_by/random/out.sql,sha256=ZChFgLBscmvDq3EJwsCqV-D5hr97_Grus0TOdbHhTnk,55
ibis/backends/tests/sql/snapshots/test_sql/test_order_by_expr/out.sql,sha256=BQ4dj1SGaMLjMOSCuj2B-9BeGETJycuiHwWTesTOT4g,79
ibis/backends/tests/sql/snapshots/test_sql/test_searched_case/out.sql,sha256=_m7OaN5H3_VQON_t2BgGcZ77pRT1SQUZmcF5GGZd8nk,154
ibis/backends/tests/sql/snapshots/test_sql/test_self_reference_in_not_exists/anti.sql,sha256=mohcjbB6C5Pavp3tiOjNI77RiYZlmOiBPJCBPfPqXLE,203
ibis/backends/tests/sql/snapshots/test_sql/test_self_reference_in_not_exists/semi.sql,sha256=E7sa_wV99TMsvBtggnyE0KqH-MCaKwvUAn5jtcJxMWU,177
ibis/backends/tests/sql/snapshots/test_sql/test_self_reference_join/out.sql,sha256=ZKy-bql13gp5Xz9TMpgWP8vfzkrCDE9ROUSSldOLW2E,146
ibis/backends/tests/sql/snapshots/test_sql/test_simple_case/out.sql,sha256=FIE088L8cySYGVhmBtZdb7fpXjmwk6hBKzRDOnWR7Q8,118
ibis/backends/tests/sql/snapshots/test_sql/test_sort_aggregation_translation_failure/out.sql,sha256=XdsezcpuCnIADr1ukO6ChP1FTbljDHu2by5Z9XD5KxM,176
ibis/backends/tests/sql/snapshots/test_sql/test_struct_field_simplified/getattr/out.sql,sha256=Jga_CbAzDLDdRdfy5V2iUS0rEbT9gslVzhlWZluo_DM,81
ibis/backends/tests/sql/snapshots/test_sql/test_struct_field_simplified/getitem/out.sql,sha256=Jga_CbAzDLDdRdfy5V2iUS0rEbT9gslVzhlWZluo_DM,81
ibis/backends/tests/sql/snapshots/test_sql/test_subquery_aliased/out.sql,sha256=8N02L_5a6k1Zt74EVASLvXv0k_pUGqfaVv7fB9JGioc,231
ibis/backends/tests/sql/snapshots/test_sql/test_where_correlated_subquery/out.sql,sha256=dU8OLOcRwqTbizAh4yQ-oeQ7AxCJAKjNizto-78G-vs,223
ibis/backends/tests/sql/snapshots/test_sql/test_where_correlated_subquery_with_join/out.sql,sha256=ylqreoLVhQ7eNDeM1fOL8oM-qGFAc57BXhDblAMgNcQ,614
ibis/backends/tests/sql/snapshots/test_sql/test_where_simple_comparisons/__pycache__/decompiled.cpython-312.pyc,,
ibis/backends/tests/sql/snapshots/test_sql/test_where_simple_comparisons/decompiled.py,sha256=bFCxplJ-4BCko1ri3zGJ0Y9p_2VSaox7Uza62n5kMEM,204
ibis/backends/tests/sql/snapshots/test_sql/test_where_simple_comparisons/out.sql,sha256=Hy6HZJSeumdaipqxijG5yZY9NE8mtqbugWOnJdXFIHw,90
ibis/backends/tests/sql/snapshots/test_sql/test_where_uncorrelated_subquery/out.sql,sha256=zv0SzKw-DUgcSRlbru6jICnCx-yfAcsq7UnAHXsgRD4,108
ibis/backends/tests/sql/test_compiler.py,sha256=7efFJtj365J-DTLC1S80jIqE2pN6jDkwylG3XOJ9EWw,7663
ibis/backends/tests/sql/test_select_sql.py,sha256=ToDPLVcIerHFIEe_z1e2PHqK6UND_rtU_Mi4bBfSaSI,28684
ibis/backends/tests/sql/test_sql.py,sha256=4sz93fybgREFdhd1uOmlIQ_0k_O6D0_8838rUCNzYD4,20520
ibis/backends/tests/test_aggregation.py,sha256=rfYxJpxRGwZq8PLGyxw-EFT2r9dcOngpiheTEFqEtw0,56488
ibis/backends/tests/test_api.py,sha256=xUCxeM81GD8wLvd6SCBjPodCzRGgacxgH-zTtsodfx4,4309
ibis/backends/tests/test_array.py,sha256=FODrF4nHk4GbQSjRUC5iaJu2SDxMhR7hBxfNx5X1bz8,59067
ibis/backends/tests/test_asof_join.py,sha256=e7zOeMXP3LcsOWgKDTEvG1Kxlsc7sfyOS2JbJexBUbY,4564
ibis/backends/tests/test_binary.py,sha256=pFlfUQnKL-o0XqcQYQpKJfPxC0Ftmn--I6bqrig_TRg,1033
ibis/backends/tests/test_client.py,sha256=cs2HqsE0KPV8u-cIxsy-PJDPb38CS51ANxHO3032iEk,59836
ibis/backends/tests/test_column.py,sha256=VI09xQSRHi--WWewhLUyRNntczs-GG4gB0BJRDTVYwE,1132
ibis/backends/tests/test_conditionals.py,sha256=615f3c8DLwiqSu4OLrvI81zjC_yjAYRCrUk44Rblvus,4649
ibis/backends/tests/test_dataframe_interchange.py,sha256=yi3v6BDJ4tHkNXDipz4ng6mPfrtcMr594hYF6haby_Q,3915
ibis/backends/tests/test_dot_sql.py,sha256=N6AL6fT0OYE0H9v-RsEgoIkbpF7MGWAu_rayglpgvZo,10722
ibis/backends/tests/test_examples.py,sha256=UJDG5DWKMnor2pEDnxA2XWZv0VHGvdLs6vNxpH8dBJM,2193
ibis/backends/tests/test_export.py,sha256=qNk5adinB8OSZ76OG0yqO3OTtpa_pOQ7BNv-gRndtO4,23423
ibis/backends/tests/test_expr_caching.py,sha256=3SXrIMdZ97zlZR3WcI4yyz6TsHdX_dJkGjVXJGbtlEU,5188
ibis/backends/tests/test_generic.py,sha256=heH2UCP_qfJHEJmL11j-SvDQOqo_v6X-k4fYi5lYSbs,79740
ibis/backends/tests/test_impure.py,sha256=eeEgLYEvVB2rpKh2T_UDacYSBdaFcbxFlATlVA6ZRLs,7575
ibis/backends/tests/test_interactive.py,sha256=_LoWYThJgzhRLw7Vl_cdeKuKgAK0yTwjGAfzHEUPMpg,3610
ibis/backends/tests/test_io.py,sha256=Bx26raQNChXCcQtq_innOmo9y4xsN7-ZN04zL-k9ZIc,10117
ibis/backends/tests/test_join.py,sha256=7ZPvLmXXRNLMl49YV6BW2L7lpjrbqy1Fu0LSl_bkp7k,12685
ibis/backends/tests/test_json.py,sha256=-V9ZOSFe8jbOePWbW2sBjqF6vTQeLZ23xqoDKddVWhs,4497
ibis/backends/tests/test_map.py,sha256=IFNCQ_pNh-QlVEJuDKbo8D1jRq4YVDRDj-_g-t1hflQ,17602
ibis/backends/tests/test_markers.py,sha256=HUjeOtwepc7J9YjpZEGDpVaCdnMLUrjVaRU8nS48yxQ,1733
ibis/backends/tests/test_network.py,sha256=ESnZ4avEiojcnjwh3OiHtj6oiSJNG2vhzFNb_6yQ_i4,4276
ibis/backends/tests/test_numeric.py,sha256=jbdtnKBfj987HFI-0GBlfo8ClITt8SqaIbQwE20vr1s,55132
ibis/backends/tests/test_param.py,sha256=jXDlUb4qiECxy14Pa0kciTO0pTu3MS7Y7LRWetWjWyU,6006
ibis/backends/tests/test_set_ops.py,sha256=rl5RVXO6bYYXp6jZZ5EN2Bb21OTUUjgW2GrtS38BUkg,8065
ibis/backends/tests/test_signatures.py,sha256=aRhJmnfbnUlqhVRpFbicUhK6bUQ3gE8A4hSRindxHRU,3135
ibis/backends/tests/test_sql.py,sha256=UMOHnAPXIs3oawW2k19-gNZlHdvPdj_w-DP2Gqk6b38,9333
ibis/backends/tests/test_string.py,sha256=9lyRqk3x4z-R50WwP2w5PQscj098sw2mUeRVFuSx8c0,45588
ibis/backends/tests/test_struct.py,sha256=XP-DaYDV_PrwYpSKW-bmTb-cwrwJIhqz0RmeGSgd258,8404
ibis/backends/tests/test_temporal.py,sha256=Z90qOWnNaQBJKSgkF0NWVXql4Xz3vnfdp2YZmYMZudE,79934
ibis/backends/tests/test_udf.py,sha256=duyrw3e-wtJ71VLCTB_QNf_NwxZmnPOlzYc8dJEaC3Q,5612
ibis/backends/tests/test_uuid.py,sha256=OsP3fxIERoCKS0XQOTZAUPWss062vlf0WxvAcQDz5Kc,2380
ibis/backends/tests/test_vectorized_udf.py,sha256=owAAEIWwuPfm6Q3s5uMgvA79jPF6RJ2IzJtzMZaK_4A,27104
ibis/backends/tests/test_window.py,sha256=qYLO_tAFzITsdy-O1-6kQKk_gxm7X8LzveZ0Wj3WriA,44506
ibis/backends/tests/tpc/README.md,sha256=pp0wAmz0KBH_Vb0B5VGYgYePF6UmLMNmOyl-FiFZiHo,1161
ibis/backends/tests/tpc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/tests/tpc/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/tests/tpc/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/tests/tpc/conftest.py,sha256=PAo2pgv556u5NO5HX0y-WSCZpCzuscP0f9n8U-LAu3U,5418
ibis/backends/tests/tpc/ds/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/tests/tpc/ds/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/tests/tpc/ds/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/tests/tpc/ds/__pycache__/test_queries.cpython-312.pyc,,
ibis/backends/tests/tpc/ds/conftest.py,sha256=REjzyq0Cg-fI6cfe_TiO5UdbFkdsA8gcdku-sSy9WyY,2386
ibis/backends/tests/tpc/ds/test_queries.py,sha256=I5_XCgClPrNNKOgvVi7KFn74EhFPnPqcy2U9iCNK93k,164571
ibis/backends/tests/tpc/h/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/tests/tpc/h/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/tests/tpc/h/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/tests/tpc/h/__pycache__/test_queries.cpython-312.pyc,,
ibis/backends/tests/tpc/h/conftest.py,sha256=h5i7H-Z3J0bbIhIoW8ZoDQMw9poKNVtrdmiV3izNCFI,758
ibis/backends/tests/tpc/h/test_queries.py,sha256=9UyiCfLl6Y-S91CakgTCs15YkNgoKRmLpManNI5ltQo,23753
ibis/backends/tests/tpc/queries/clickhouse/ds/13.sql,sha256=P3QuEH2V8woWauUAHV99eRSOQySfy6LW9_EdYe8iv6s,2197
ibis/backends/tests/tpc/queries/clickhouse/ds/48.sql,sha256=tlrbVrMKYIueXPlnAoAA50E05V5IFcYALjCNcQHIgME,1322
ibis/backends/tests/tpc/queries/clickhouse/ds/64.sql,sha256=9a-Cz8v0vSuoCgR_wanskPQHDyUKC0yMTWlSIQ0jT_8,3759
ibis/backends/tests/tpc/queries/clickhouse/h/19.sql,sha256=xZBwIDL2muD6uTcSMSTIQwHGG6hXs1i9ayc1Gj3AAmg,995
ibis/backends/tests/tpc/queries/duckdb/ds/01.sql,sha256=4r1uvnFP2iwq92VCRIkY9lEbR0-nrAQdA5LTdkygqV8,690
ibis/backends/tests/tpc/queries/duckdb/ds/02.sql,sha256=O5dvajpSsH7AQ8e-RGZWSbo-tK1jZ1HyCR9_CvjbuF4,2561
ibis/backends/tests/tpc/queries/duckdb/ds/03.sql,sha256=BDvLPIUYzETsEypuSrKZ1WfxfTrYpvWxSrsNRLpT6e0,447
ibis/backends/tests/tpc/queries/duckdb/ds/04.sql,sha256=Ih9z6oxWyr5lCPkJqiy9crQ-EFz8IDZ18_e0vbGBgnk,4662
ibis/backends/tests/tpc/queries/duckdb/ds/05.sql,sha256=Io-48aacCJnO2MaW0Npj6hrHEuupnHfYW0kN12A46E8,4239
ibis/backends/tests/tpc/queries/duckdb/ds/06.sql,sha256=geS6jQ8UnxXYsKGXz3_EjDmy_qeHOEQWktFuYCh4fk4,655
ibis/backends/tests/tpc/queries/duckdb/ds/07.sql,sha256=eFf6r_ot--HPE0eTqwBTpd4-LA9R3F8at77En4dA8v0,577
ibis/backends/tests/tpc/queries/duckdb/ds/08.sql,sha256=zNsuwJouwd7F2IvDu5hqwBcJ0rIRaBzI50548vUsOcI,19170
ibis/backends/tests/tpc/queries/duckdb/ds/09.sql,sha256=hHY26XzMZooCYw3nARSUjs_nsL_0Kc7qL6mO85_P6-I,2448
ibis/backends/tests/tpc/queries/duckdb/ds/10.sql,sha256=bO0QPWlwsOIAQVhk6SmYeMhatHV_eDyUbjshf6WrCXg,1859
ibis/backends/tests/tpc/queries/duckdb/ds/11.sql,sha256=W7TCE3rfNzRu0C_UxDMCDWDP6XzuP8BTDV6BbHyBsXM,3007
ibis/backends/tests/tpc/queries/duckdb/ds/12.sql,sha256=nG-jy7V61q2R3WLjIYSMfiEO_RgyRT_y3JJfs_-4QeM,750
ibis/backends/tests/tpc/queries/duckdb/ds/13.sql,sha256=bSqztjbP4o7qoiYrhpxS7hWcvP3nywEsByUL2SFOEao,2438
ibis/backends/tests/tpc/queries/duckdb/ds/14.sql,sha256=oQcI13v7LaK3wrK4dqUaUUFkGAmvTKJDCZ-qbp6k8PM,4230
ibis/backends/tests/tpc/queries/duckdb/ds/15.sql,sha256=bbjEur8NR60NJBO8xHTjSZ2Fzwj1OFBXdKffdQdWt3Q,830
ibis/backends/tests/tpc/queries/duckdb/ds/16.sql,sha256=b7zlPrboDb13hSwj7Gq_LHdSQmeRlrYN0PSShjPIuds,836
ibis/backends/tests/tpc/queries/duckdb/ds/17.sql,sha256=9OrIi2l-MI9a3bpwcpd8mqLNRVUIUwU2RlPdKFBkvuE,1765
ibis/backends/tests/tpc/queries/duckdb/ds/18.sql,sha256=68HmzAaZAqvDrIe2N7WwLj8I8HbNPZ3RUwMyfoxrIlw,1480
ibis/backends/tests/tpc/queries/duckdb/ds/19.sql,sha256=sI65Nfdhp74lGdy1_mnVr5UT8SaVPfFyzNYTfwomUH0,709
ibis/backends/tests/tpc/queries/duckdb/ds/20.sql,sha256=KbWK4SA3YEqT5Wj0X9WVI4ML0ilGf53RWFq67Emira4,821
ibis/backends/tests/tpc/queries/duckdb/ds/21.sql,sha256=A7hyUNbuW63Batsu_XRh8eLtIdZ8W3HxXS2fLR-LsMg,1015
ibis/backends/tests/tpc/queries/duckdb/ds/22.sql,sha256=k1V5uLm4L8MGX1BK8EXk5-HocYWibnvDapi9Pu5ewd8,487
ibis/backends/tests/tpc/queries/duckdb/ds/23.sql,sha256=rBTXTtm1PTMb2Itr0AEYPyc9HkNJsxVX8quatZy_OJU,2430
ibis/backends/tests/tpc/queries/duckdb/ds/24.sql,sha256=awN3kN8oHAtZptu5FbdJ0ZO39YSOx7tHfAysfqD-VPU,1265
ibis/backends/tests/tpc/queries/duckdb/ds/25.sql,sha256=h0TIwXXxX5nz2uqu2jtU_srdhNb6Z3GVjZRH1yQ5d5k,1069
ibis/backends/tests/tpc/queries/duckdb/ds/26.sql,sha256=n5IVBGdraB8jjRajpnprMhrRAEt8jzEMFb5PrjHzebY,584
ibis/backends/tests/tpc/queries/duckdb/ds/27.sql,sha256=u3kotUbzbZm60tNp3fzvE9Q7WzDssQpj5ohijodusRU,1551
ibis/backends/tests/tpc/queries/duckdb/ds/28.sql,sha256=eCCb-Oe0P2fCOc5OLVysOFAy920LMJjDscutFaCOQGs,2057
ibis/backends/tests/tpc/queries/duckdb/ds/29.sql,sha256=5xbi5NPxdGsv_VhFm_3QkeMli2UDna6PTY-fX_BM6lQ,1090
ibis/backends/tests/tpc/queries/duckdb/ds/30.sql,sha256=cDR_1jyrgtBNxpAtvbDeOVCHU-B9IiL1Mvkytpeplns,1508
ibis/backends/tests/tpc/queries/duckdb/ds/31.sql,sha256=0dHuhK81Zi-OC3ZsDHu8OMEnfCQ6h0M-4QweY8MMTYw,1995
ibis/backends/tests/tpc/queries/duckdb/ds/32.sql,sha256=Efzo42ZpyPcmn5GdWQpRLsACh3UqCPUn7dbg--SHLNk,562
ibis/backends/tests/tpc/queries/duckdb/ds/33.sql,sha256=z1I-AxfqW_PNLeQOGuGGndKIfvRLffJ25SfRLsmNTRY,1671
ibis/backends/tests/tpc/queries/duckdb/ds/34.sql,sha256=Ytk69TX3QLokUF8s5yY4Gt-peh5ycQfJtPwCXOsWLxk,1450
ibis/backends/tests/tpc/queries/duckdb/ds/35.sql,sha256=DW3Mkr1mw1siqnXlAR_uqXkFx6Smz_FcqkGZtpvGrBw,1761
ibis/backends/tests/tpc/queries/duckdb/ds/36.sql,sha256=sM5w0wKCtldhWIYm0y3IuIJqyRfeOLCbsspa8CIP8NQ,1845
ibis/backends/tests/tpc/queries/duckdb/ds/37.sql,sha256=C_0RHc9yaZychBheVsPMD6UBUsyVI3mTA1M8oLchaeo,593
ibis/backends/tests/tpc/queries/duckdb/ds/38.sql,sha256=ZueWJJZFJ8HcCFnmqwJ-zgNGohOASSlXs3TVPWeB_ss,1037
ibis/backends/tests/tpc/queries/duckdb/ds/39.sql,sha256=QrA2kJkXDrv9FdeCwUJ8e09gJUoDPkm1jpVWpnQUynM,1547
ibis/backends/tests/tpc/queries/duckdb/ds/40.sql,sha256=ydHWrwEa1G4ydFjrhQMvj2cuuoEtfj7Q-u59nCFtS0U,1048
ibis/backends/tests/tpc/queries/duckdb/ds/41.sql,sha256=fycIdnOa1TY75U2kOq0SGUVso5IAR-4E7wYpMM8NP10,2748
ibis/backends/tests/tpc/queries/duckdb/ds/42.sql,sha256=cgLKPyYvBewYVAk6rhhxi84uAy8nXaFbf_P2HJQgD1o,567
ibis/backends/tests/tpc/queries/duckdb/ds/43.sql,sha256=n9DOuSM4NMJ34X32g0qEXNeBsIvQY0h3BW4TseAo5G4,1348
ibis/backends/tests/tpc/queries/duckdb/ds/44.sql,sha256=yii145WYdjFU5_cdbobAzjbZAULFwQtlQFRJO6NfbQI,1407
ibis/backends/tests/tpc/queries/duckdb/ds/45.sql,sha256=W_U8WRk5VYnVIbcS-sIWwWcOiB3yRqhm1c4bEbiJetw,1176
ibis/backends/tests/tpc/queries/duckdb/ds/46.sql,sha256=RftwrfUuknkKn8dFZPRULStS1xelPD2keLtrXWHVw_o,1476
ibis/backends/tests/tpc/queries/duckdb/ds/47.sql,sha256=DUob3QoL1zRV3O_IjZ5U7IqtGnEFFI7zVFvDmnm_cRc,2474
ibis/backends/tests/tpc/queries/duckdb/ds/48.sql,sha256=d28AtLi-cuWaz2IZmD4qbp3BFIomLpTnzddb7jkF6AU,1432
ibis/backends/tests/tpc/queries/duckdb/ds/49.sql,sha256=r8HydugnGmTSsYM-4ZGO8-4X7o8Cb6518R6ERtGu5Ko,4354
ibis/backends/tests/tpc/queries/duckdb/ds/50.sql,sha256=w9_YZyJTuVngI1oMBCAUu6FxNu4ieHkdjN5U4Jfy3So,1904
ibis/backends/tests/tpc/queries/duckdb/ds/51.sql,sha256=iRJvbSLToY2i3HOGroceJArCWG3neDqiZ6CWiKFi_7A,2000
ibis/backends/tests/tpc/queries/duckdb/ds/52.sql,sha256=7PReHWT_3NZxWs03UQ9iTywC-6GH2h0g8Tjg_3EPb6M,470
ibis/backends/tests/tpc/queries/duckdb/ds/53.sql,sha256=rX0cCWaQcDpiWk01-avBygsSSxRNpwtKNYucSRKS0-4,1809
ibis/backends/tests/tpc/queries/duckdb/ds/54.sql,sha256=0FWxoECTkaQUWV3AvNONLmdPE8lo2H89X9vWjQbMLcM,1666
ibis/backends/tests/tpc/queries/duckdb/ds/55.sql,sha256=VLObPcAckfbxMQ2BDddDQ0JEvZobA9o8t_LKk8EKsg8,347
ibis/backends/tests/tpc/queries/duckdb/ds/56.sql,sha256=ZtkWKkiCSVZeZTqgYdNGOLGejercYrrHweo6Z658kuk,1858
ibis/backends/tests/tpc/queries/duckdb/ds/57.sql,sha256=mUq6sGfQ2AtkAZLMkd_p8jfzBq3ITV2vWEYGD4VIH-E,2123
ibis/backends/tests/tpc/queries/duckdb/ds/58.sql,sha256=msjtaO1llDFHhvRL9WMNU_Tp_D8_tYoAKVUQ4iXz2Js,2266
ibis/backends/tests/tpc/queries/duckdb/ds/59.sql,sha256=eN7yYfMcHZKjw1iNAnU4kF2TqQ3Viesh6Yikcfeu-QY,2782
ibis/backends/tests/tpc/queries/duckdb/ds/60.sql,sha256=tj8BcJvy71Mhr8IKlba9_7KpZM09_sbxlUfCwxGOCJI,1600
ibis/backends/tests/tpc/queries/duckdb/ds/61.sql,sha256=Xor3aC0_fVrlE7hEQFDTLw8QiHOPusjgQd-FQanrdBw,1296
ibis/backends/tests/tpc/queries/duckdb/ds/62.sql,sha256=wtihtLZ1u0it4z5sKInELyfHKdNQlI9cB-uKrUFGC8A,1422
ibis/backends/tests/tpc/queries/duckdb/ds/63.sql,sha256=5mWTSnuK8R9pBXKVLtormQvcsSdTirCt6Pvs6e2mCWo,1796
ibis/backends/tests/tpc/queries/duckdb/ds/64.sql,sha256=6Nif7Jc_fFrtAohSGM6eZ7X1E3MBs9dimlD8OVYLW88,3784
ibis/backends/tests/tpc/queries/duckdb/ds/65.sql,sha256=ovFKlrtigKcloY6EqUuZ_aCimeDBqsvbVqB7QNNkzrM,998
ibis/backends/tests/tpc/queries/duckdb/ds/66.sql,sha256=Tn7sKsUxHbo1wm-uLXWf1aqfFTAFTf30NutLUZh7GcI,7432
ibis/backends/tests/tpc/queries/duckdb/ds/67.sql,sha256=_TP_5_Mc0Rbo8cxAd7vpJ9RitN-UQ1UHs9Se4-5A7Kw,1225
ibis/backends/tests/tpc/queries/duckdb/ds/68.sql,sha256=aKq8dkf6utNvflBMUnP8SXtjoUkCmyWdMJxrfDT9Lrk,1454
ibis/backends/tests/tpc/queries/duckdb/ds/69.sql,sha256=2p73O3u9cHfA64I2qsYaeUS1wuYhw-fnLTgxWc-Z4H0,1436
ibis/backends/tests/tpc/queries/duckdb/ds/70.sql,sha256=cS6AgC8ksRHgnzzWhfRudSAgCVJLEyYJeB0C9wP7CMc,1259
ibis/backends/tests/tpc/queries/duckdb/ds/71.sql,sha256=d7A3M1E5-7hpX2DEDf1CSGRZpBuLTGjaxk-3xOlCbvE,1369
ibis/backends/tests/tpc/queries/duckdb/ds/72.sql,sha256=LFHGhLttw5eKea2MTZfutn6JTMkAlsSb1w_3i5c_-Lc,1370
ibis/backends/tests/tpc/queries/duckdb/ds/73.sql,sha256=lJeI53icgUiwXIGSiiJMuOqHOyC4k-0rIeHQuKc5U70,1379
ibis/backends/tests/tpc/queries/duckdb/ds/74.sql,sha256=g_N0d7ClAhPI2gj9oAclGZlzsVB37Q6DxXU-4hsFETs,2152
ibis/backends/tests/tpc/queries/duckdb/ds/75.sql,sha256=_Y_l0dyrqhSBdBHXQHUb5u2ZbOpSEHGt2LYSdHI5uYs,3006
ibis/backends/tests/tpc/queries/duckdb/ds/76.sql,sha256=iM0jl7HdRADeqYTNpHrTRpK-e60ux9iCOvr36qzxBWU,1709
ibis/backends/tests/tpc/queries/duckdb/ds/77.sql,sha256=5P_wd-Mn5UFDtwXZozkrDbygW_rvZ8nbtLMF1DjQUDk,3130
ibis/backends/tests/tpc/queries/duckdb/ds/78.sql,sha256=ZZO5Ys422V6QIVdaCWeqQ_-9G3R1EtlIVoHaxAna3Pw,2513
ibis/backends/tests/tpc/queries/duckdb/ds/79.sql,sha256=Tc3QcZ8XnVylbi22yHJDPz-TVIRoNVZkkEn8fXNkdMA,1206
ibis/backends/tests/tpc/queries/duckdb/ds/80.sql,sha256=sFrGZJcr3pTd1xrM7k97kLQ-CQw_NJoIwus_y2RLb-4,3675
ibis/backends/tests/tpc/queries/duckdb/ds/81.sql,sha256=ZgIY1dDcB3b_0IVrTo0PQnGgfOvE6eTZ6iHILm7o4co,1460
ibis/backends/tests/tpc/queries/duckdb/ds/82.sql,sha256=38uHJVPoSL7dIb4jk1FOTDL-mzAqLFVUWM6Zg24t9tU,592
ibis/backends/tests/tpc/queries/duckdb/ds/83.sql,sha256=8tpaUc3LlDC9xmeLpgss2096QT6rH3b1Nxv5X_5vg6U,2241
ibis/backends/tests/tpc/queries/duckdb/ds/84.sql,sha256=L47tyjtxDs1KvMWj7iKwoFHM11f-bDHOaNAo48I-h7w,607
ibis/backends/tests/tpc/queries/duckdb/ds/85.sql,sha256=-WaJFwdHJBY0WgWofjj2Qh_OQ6JTbaAWTu_7cmzV_l0,2190
ibis/backends/tests/tpc/queries/duckdb/ds/86.sql,sha256=JQFNRVy68AioSgyRnMavjFArfBR9aZMbz1EuRjT03xY,848
ibis/backends/tests/tpc/queries/duckdb/ds/87.sql,sha256=RMGzksJtvmZHlC8gqRaVROPH_qq6AgOL0qcOhwiTih4,1173
ibis/backends/tests/tpc/queries/duckdb/ds/88.sql,sha256=ve994m0kQRgKLvceUP1bDikWJf63gBeUEYuSmem2KKc,5659
ibis/backends/tests/tpc/queries/duckdb/ds/89.sql,sha256=uwbokSzEudwr84wUCx64L0UpX01qao57YtY2t3LqZAU,967
ibis/backends/tests/tpc/queries/duckdb/ds/90.sql,sha256=_RZ2C2O8BnkAihxe0Cd5rgpqRZ-9woH8w19_f3HS_3A,984
ibis/backends/tests/tpc/queries/duckdb/ds/91.sql,sha256=qyLzaVUudIQXYfiBPDUXwLL5hmZTm3-ca_kDhRcSAYQ,983
ibis/backends/tests/tpc/queries/duckdb/ds/92.sql,sha256=1eT6vAYN6uhKZjES93TH_wcWma-dIHBVH9FIyRdjYjw,584
ibis/backends/tests/tpc/queries/duckdb/ds/93.sql,sha256=LnYoou_fypRXLi9CFDZIRyTEJBM7VehX6dYiVSEBVPY,670
ibis/backends/tests/tpc/queries/duckdb/ds/94.sql,sha256=nbmquwE6IURC18QRmVIgNkmn7BJpbBg1xKBbMc8oMRE,811
ibis/backends/tests/tpc/queries/duckdb/ds/95.sql,sha256=-M83QCE99vtDYy8PT4pPpjBMBl3Jyt4Y8_4-Srjy0jQ,1025
ibis/backends/tests/tpc/queries/duckdb/ds/96.sql,sha256=xa-8Cseg1WzQZgiBHOsQ-lOP1mu6lZeiBkExCktg9aI,387
ibis/backends/tests/tpc/queries/duckdb/ds/97.sql,sha256=3c87o-GJfcX0_G-vYXvZLWhKCRbVAhnqaybrhVGyYc0,1160
ibis/backends/tests/tpc/queries/duckdb/ds/98.sql,sha256=RudeeOgaXTv7iNoFsYtjoA6SkELY6MLAda80ZNbMGQA,812
ibis/backends/tests/tpc/queries/duckdb/ds/99.sql,sha256=MqANWwyI7XXP0ZxuRZez6sjXEoXvFuQpd27YbrqzRhg,1485
ibis/backends/tests/tpc/queries/duckdb/h/01.sql,sha256=iu_nehyXRZvGmsu82vO4Y32Ooi38Ose-cy1ERtqDtps,545
ibis/backends/tests/tpc/queries/duckdb/h/02.sql,sha256=oiGYk_lS3SolhqspfZCewtRA-qu15pLySIAViqaHc0Q,841
ibis/backends/tests/tpc/queries/duckdb/h/03.sql,sha256=HxINeQ1JFGUsvz-8D6KO1lc-St7mGucWSow9mbfGt_4,468
ibis/backends/tests/tpc/queries/duckdb/h/04.sql,sha256=Kivol1L8kXvHHhpZFo6tnQDUDEDqC75kYgbkFLNy-Ig,408
ibis/backends/tests/tpc/queries/duckdb/h/05.sql,sha256=SV3vfeuwKrHe8x2gf7yItHloE7dUgyzhXVSjXcsQRXc,522
ibis/backends/tests/tpc/queries/duckdb/h/06.sql,sha256=NjhTX7kqYCazNZoq2ebtfamtkIAVJRPm_TgY0ahTwp8,243
ibis/backends/tests/tpc/queries/duckdb/h/07.sql,sha256=XoB3EhpCyo6_X_VOWEKmFOg52pRbAzHjtj2cytI2EgE,954
ibis/backends/tests/tpc/queries/duckdb/h/08.sql,sha256=lIM49UuXosSa6hX4xNYJ7PJE0ArXdU7c6mC0dIUEoc4,950
ibis/backends/tests/tpc/queries/duckdb/h/09.sql,sha256=r5DMvjDs45YgnmYnwQwAhu1UIIEANXki7kBN6LuGflY,678
ibis/backends/tests/tpc/queries/duckdb/h/10.sql,sha256=C0iwDP-jd2sy5n6QQRkd6iUJXiqVLuUlGZC5yFhQMe0,580
ibis/backends/tests/tpc/queries/duckdb/h/11.sql,sha256=8xPHZvrFB8EgT7byus01cZphoRwPaWATAlVRUN3e2ng,582
ibis/backends/tests/tpc/queries/duckdb/h/12.sql,sha256=OBTJ9Lekdu6JTEd9Q87bKZYDse0PUxV5DVKDnI2jVb8,704
ibis/backends/tests/tpc/queries/duckdb/h/13.sql,sha256=SkoLLFZGssrY8iCo9Lc8y58KzUVUI1PWPBimkie8IKM,366
ibis/backends/tests/tpc/queries/duckdb/h/14.sql,sha256=5-x5YKTEkhn8dtwDSVoVMK6zDHlkE90dydUBuGk08iI,364
ibis/backends/tests/tpc/queries/duckdb/h/15.sql,sha256=i_3iFQAUg9id6WAsxR7xJCUq3BKK0AMYZUoDFFeBJsA,951
ibis/backends/tests/tpc/queries/duckdb/h/16.sql,sha256=RQhednLLFQDmJeBIFW_FU2nnqbyR_TZHw_dzWocMPvg,553
ibis/backends/tests/tpc/queries/duckdb/h/17.sql,sha256=38fwCsilAMKsJzWwW0G0Yrvkr5znzWw35C0bud9KAuE,329
ibis/backends/tests/tpc/queries/duckdb/h/18.sql,sha256=ZMJubZ7EBR3XhKGtFHcpRjb-1R_7Vl4Vz4yWdcq1l9c,555
ibis/backends/tests/tpc/queries/duckdb/h/19.sql,sha256=NB-X0fdN6zCsoqiw7oSXcqOw4JigbPEiZsY1c8uYVHg,1043
ibis/backends/tests/tpc/queries/duckdb/h/20.sql,sha256=-patXLiXvzHsm5amsqDR9xMuceL0gTIebbFmKLw-X9U,909
ibis/backends/tests/tpc/queries/duckdb/h/21.sql,sha256=spRj8wpk5Fp5UEfublV-bDwfAfxhKsyZeHNZQEjnS1k,803
ibis/backends/tests/tpc/queries/duckdb/h/22.sql,sha256=2O1jMudfkZM5EAC6Bx4Xrc9zWi2ZxaTaD3wT1pXMvUI,807
ibis/backends/trino/__init__.py,sha256=kwGC43FxyyOkWUAOGxdSkRjAO2kWxqEtDMbcMcZYLd4,17740
ibis/backends/trino/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/trino/__pycache__/converter.cpython-312.pyc,,
ibis/backends/trino/converter.py,sha256=_A7AkdDJ8C0gP6Ti4SfF4gu6quCmjn4aT4RomgVIVWg,862
ibis/backends/trino/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/backends/trino/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/backends/trino/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/backends/trino/tests/__pycache__/test_client.cpython-312.pyc,,
ibis/backends/trino/tests/__pycache__/test_datatypes.cpython-312.pyc,,
ibis/backends/trino/tests/conftest.py,sha256=q-MPv_QZyr6kHqOhjws9Gim-wuQRw1OimNlNP3dBPX4,6336
ibis/backends/trino/tests/snapshots/test_client/test_builtin_scalar_udf/result.txt,sha256=qhUAenksstlsRCJhQMglQXSqLRL9m5M0RoRnKwumCFs,174
ibis/backends/trino/tests/test_client.py,sha256=hlgTKuiqVrSUEdgxyE6SnL0qy6YibEy6q604PV9u1J4,6702
ibis/backends/trino/tests/test_datatypes.py,sha256=_IQETFW-Z4NnAiQlxd29QoByvc_uZFcF6fhuw8BR75g,2270
ibis/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/common/__pycache__/__init__.cpython-312.pyc,,
ibis/common/__pycache__/annotations.cpython-312.pyc,,
ibis/common/__pycache__/bases.cpython-312.pyc,,
ibis/common/__pycache__/caching.cpython-312.pyc,,
ibis/common/__pycache__/collections.cpython-312.pyc,,
ibis/common/__pycache__/deferred.cpython-312.pyc,,
ibis/common/__pycache__/dispatch.cpython-312.pyc,,
ibis/common/__pycache__/egraph.cpython-312.pyc,,
ibis/common/__pycache__/exceptions.cpython-312.pyc,,
ibis/common/__pycache__/graph.cpython-312.pyc,,
ibis/common/__pycache__/grounds.cpython-312.pyc,,
ibis/common/__pycache__/numeric.cpython-312.pyc,,
ibis/common/__pycache__/patterns.cpython-312.pyc,,
ibis/common/__pycache__/selectors.cpython-312.pyc,,
ibis/common/__pycache__/temporal.cpython-312.pyc,,
ibis/common/__pycache__/typing.cpython-312.pyc,,
ibis/common/annotations.py,sha256=FcM59OUQ9vKZPdP3HRGJ_GdRpAFqxWD3cX-H_XVcJHk,19952
ibis/common/bases.py,sha256=exyxhK4QIUv8FkK1YGxTyGALns1QE6O1DilDAGhtJ3c,7833
ibis/common/caching.py,sha256=6OElBRGrs454s0-Ovk1GAloONwiuhiN8WUW8ovkZO0M,470
ibis/common/collections.py,sha256=5t8F0NAb8OIFSUwNwxzk5ht17ski-aVgNrs2Y_kbNzk,11134
ibis/common/deferred.py,sha256=112FWVUFAegxfiCO0rFIeMGEmHa7_F_77dz5WstV1ZA,18437
ibis/common/dispatch.py,sha256=IHC61ETfRlFu15Wc4u2LHKQJCdiIJ08-O-FyfHL7QF0,4289
ibis/common/egraph.py,sha256=CvGahlFTXr4fvky8x6zsOXEdKldNKA6id9db8HiTByw,24797
ibis/common/exceptions.py,sha256=TPQQzkDFPC9xKkcIRfH7X7kBW28EJR9uEu9vBGkErzw,4706
ibis/common/graph.py,sha256=31HquKsZFY5b8Tsr5Ux9mCUrbEbNBF9WE9PHgg8ZKRc,26331
ibis/common/grounds.py,sha256=C4m6SaghD9w0V1XnlZ45bugvFa0HDCv8_VOGBmVS2Oc,8159
ibis/common/numeric.py,sha256=xHJanURYlkCinUmwSkbiuxg8dl8e74HKQfHcSOjeRJQ,1406
ibis/common/patterns.py,sha256=iZciT7gux7UgecofeJZ2bYDi3t3yi8tKrzWSfdQj1v8,49588
ibis/common/selectors.py,sha256=KF8la6W6BqlYa93lcb5dq3TM9UxejVsSAbFsVnsThDM,3052
ibis/common/temporal.py,sha256=QnDgtooxsvC7HrRJPY1r67_09q7ZEi-mmg2FOUKPU0U,7242
ibis/common/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/common/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/common/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_annotations.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_bases.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_collections.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_deferred.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_dispatch.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_egraph.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_graph.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_graph_benchmarks.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_grounds.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_grounds_benchmarks.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_grounds_py310.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_numeric.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_patterns.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_temporal.cpython-312.pyc,,
ibis/common/tests/__pycache__/test_typing.cpython-312.pyc,,
ibis/common/tests/conftest.py,sha256=BRTVIXRH1FsCKtP4phVxidhZURkvQZnHcWvKk0ex0HE,161
ibis/common/tests/snapshots/test_annotations/test_annotated_function_without_decoration/error.txt,sha256=1YoVZStmrcIF-QXnjWEUwTssAku846gjsWYhmeHt1HY,96
ibis/common/tests/snapshots/test_annotations/test_signature_from_callable_with_keyword_only_arguments/too_many_positional_arguments.txt,sha256=f1VCmfyrpuLiVRmjhX3YzL1fGvF7eU3a334k-HTtfPY,114
ibis/common/tests/snapshots/test_grounds/test_error_message/error_message.txt,sha256=yJd07Apa40vdvSaLiTuPNLl1I3Y2JnkdLz12WZSQ1Pg,486
ibis/common/tests/snapshots/test_grounds/test_error_message/error_message_py311.txt,sha256=UDVoG--vIRz2CITeepyXxszH64XhiOWjN1KsnIISU9w,496
ibis/common/tests/test_annotations.py,sha256=1py6RbPCEaGgsZW9-eSUQi18QjD9XV6kEKEghLd9dwY,13619
ibis/common/tests/test_bases.py,sha256=4k2PE0gIX9kdNWLo0gT2P2-Mr1bu_WR7De8Y-xpuj_U,8547
ibis/common/tests/test_collections.py,sha256=H9ostMf69RcxC76XM22KnDNohdTolXleePNRf3nj7T0,13529
ibis/common/tests/test_deferred.py,sha256=4Lq3WNVVlVTAgzhY1gZGMSQbpGlAMjSi3utTwAlV8AA,15554
ibis/common/tests/test_dispatch.py,sha256=PNWOg9cUVhUVB76oE91sJiKNW5uMUQ0FHfQDVRFX1rU,2441
ibis/common/tests/test_egraph.py,sha256=HtVN3DgiSVmmACgmrnKg6a9QzOK-jy3vHZUTO-4sFPs,12995
ibis/common/tests/test_graph.py,sha256=FNr5A-n9EulMM6jYxYWJM8JmYLdv_4iDNPyN_7Wy8Nw,12897
ibis/common/tests/test_graph_benchmarks.py,sha256=3zydVIKMRkdxaUkhrxQuHWrYKJP1oXIP9sk28qycZuo,1866
ibis/common/tests/test_grounds.py,sha256=y_ws1Fv9v3mzlCZGBknKzWsz382NMvWtM8xRmEzRuGg,26997
ibis/common/tests/test_grounds_benchmarks.py,sha256=QQ3oGt42ErP1GQppTRWpPJpyWpzrNjD-Gdx6mhIjHN4,937
ibis/common/tests/test_grounds_py310.py,sha256=hhK399m3intOpsj0pC9hPRHwye2ggbwBlinlSkVsIwo,1011
ibis/common/tests/test_numeric.py,sha256=_M1gp35nFsDWyhlI3P6csxg0Y6XfLh9Imx70AUyrGfA,2314
ibis/common/tests/test_patterns.py,sha256=T3TcRvQGg5BQzwfArFTgQgnDR03t0ErRCp0tTZGyA80,39986
ibis/common/tests/test_temporal.py,sha256=ND25XDwLBqqwfx_hr94_arxM_Xc-jqLjoIhAXS3xqvY,8556
ibis/common/tests/test_typing.py,sha256=qQb89C3mgQLiRjmCvg9gljgsePi737Pkr4bbKXfVdLs,3698
ibis/common/typing.py,sha256=7hDpfYJsVq0IyfGm3yZnvzIbigFGP9NE-w25j0iJWa0,7620
ibis/config.py,sha256=7WpFVn3DWExlRQIOHcFHaa1u-huNZdU_2M6n6dLe7qs,5309
ibis/conftest.py,sha256=52YUYES9PPLpz6zG0OXBtl-NZoBtR81fRRH5is7mwDk,1777
ibis/examples/CITATIONS.md,sha256=0l-CnpoIzxP8Zb9eRZul0RCSjlUiJ-LATsBHk-qCgNs,758
ibis/examples/README.md,sha256=2f0SSsDPapqoSbSDXNiWgaLUgaJdRb4ERsM2SwfUWWk,983
ibis/examples/__init__.py,sha256=bKm5zKh3uR9Mzq2AwE5k1ph2THmysDqJyZC4xqYLKBg,2743
ibis/examples/__pycache__/__init__.cpython-312.pyc,,
ibis/examples/metadata.json,sha256=3eMjjChQokVWHvY_cU4LApvQ9V1C8awbBglORUK6ciY,39401
ibis/examples/pixi.lock,sha256=2qQAy1tdi9-mKp4kPxjTGCXn-lqK8kmJKiJ9LPoMZtk,209992
ibis/examples/pixi.toml,sha256=-E4aRTno1cOG3Wpz4vCbuFRvgJ6hreW3AizjKU8TKYM,499
ibis/examples/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/examples/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/examples/tests/__pycache__/test_examples.cpython-312.pyc,,
ibis/examples/tests/test_examples.py,sha256=x-XDn61zHtxWwBkJGxaXu4zxK4mcUaieoUoiYeQH0GM,2386
ibis/expr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/expr/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/__pycache__/api.cpython-312.pyc,,
ibis/expr/__pycache__/builders.cpython-312.pyc,,
ibis/expr/__pycache__/datashape.cpython-312.pyc,,
ibis/expr/__pycache__/decompile.cpython-312.pyc,,
ibis/expr/__pycache__/format.cpython-312.pyc,,
ibis/expr/__pycache__/rewrites.cpython-312.pyc,,
ibis/expr/__pycache__/rules.cpython-312.pyc,,
ibis/expr/__pycache__/schema.cpython-312.pyc,,
ibis/expr/__pycache__/sql.cpython-312.pyc,,
ibis/expr/__pycache__/visualize.cpython-312.pyc,,
ibis/expr/api.py,sha256=c7cKNeOKcM3EUGVgnmZ64Q7QaOBn4IGCSN7rBBlrW08,72444
ibis/expr/builders.py,sha256=ZxqYK-ndxtMxHaxPyYf5N8E_iSUoFgsMe8bTZNxgRFM,10691
ibis/expr/datashape.py,sha256=Ook0xjg33FJvrtxKDmKdX0uWpc4fh_D70EFqZ2ZgmLY,1327
ibis/expr/datatypes/__init__.py,sha256=JAc5o9XceuTYpq3ww8y6nNBnu40-MObRTrnSyXJ9--c,523
ibis/expr/datatypes/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/datatypes/__pycache__/cast.cpython-312.pyc,,
ibis/expr/datatypes/__pycache__/core.cpython-312.pyc,,
ibis/expr/datatypes/__pycache__/parse.cpython-312.pyc,,
ibis/expr/datatypes/__pycache__/value.cpython-312.pyc,,
ibis/expr/datatypes/cast.py,sha256=6fpGnIC-4sSiO8hs1hKFTEkYNEqZ-RAla5lr0RDn0Ww,5057
ibis/expr/datatypes/core.py,sha256=bmut3KCjFzx9uLMqfwu59amu_9vOf8Ahv2K9MhWm4OI,33253
ibis/expr/datatypes/parse.py,sha256=GM3pPw0aVW-j5YgUfFVaBHbcHv29w2p5cpKBzLEtC2Q,6784
ibis/expr/datatypes/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/expr/datatypes/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/datatypes/tests/__pycache__/test_cast.cpython-312.pyc,,
ibis/expr/datatypes/tests/__pycache__/test_core.cpython-312.pyc,,
ibis/expr/datatypes/tests/__pycache__/test_pandas_numpy_value.cpython-312.pyc,,
ibis/expr/datatypes/tests/__pycache__/test_parse.cpython-312.pyc,,
ibis/expr/datatypes/tests/__pycache__/test_value.cpython-312.pyc,,
ibis/expr/datatypes/tests/test_cast.py,sha256=DX8FKp8WOKacWMHiJts94qfhyef53SsatTvczmQCTME,4228
ibis/expr/datatypes/tests/test_core.py,sha256=S0plMX3CUezOESGSU1FmvTnm31O6zyh2LlUQvfBAD4Y,18659
ibis/expr/datatypes/tests/test_pandas_numpy_value.py,sha256=MdKthlNd35S3yaz6XC5_LYa4TCofy2bvH7qOyZMiCg0,7116
ibis/expr/datatypes/tests/test_parse.py,sha256=xfhYIXXSVJ4-rxSp23fJQ16GuWoyocTqYjVdMiN7nF0,9118
ibis/expr/datatypes/tests/test_value.py,sha256=X0Px7PvnDgGqKlP-XBvDmstsEdYg9ZlnbA5_qHQczv8,7699
ibis/expr/datatypes/value.py,sha256=DBa97J05SS4cMjQwrGxuiwN0TwXmkCZR7kyoWlp5Kh4,11619
ibis/expr/decompile.py,sha256=AGpP_YGdsryEazV1w2xMSnilC3sLfbrJKqERwsGSf_o,12695
ibis/expr/format.py,sha256=zCvv5zabeASytYh9X5yxNYnCsh4T1_UglWYaG2m598o,11612
ibis/expr/operations/__init__.py,sha256=422_-3RXQA_AkKmydvuAE2lY9C7QtPWMqxU9rk1rj-k,1247
ibis/expr/operations/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/operations/__pycache__/analytic.cpython-312.pyc,,
ibis/expr/operations/__pycache__/arrays.cpython-312.pyc,,
ibis/expr/operations/__pycache__/core.cpython-312.pyc,,
ibis/expr/operations/__pycache__/generic.cpython-312.pyc,,
ibis/expr/operations/__pycache__/geospatial.cpython-312.pyc,,
ibis/expr/operations/__pycache__/histograms.cpython-312.pyc,,
ibis/expr/operations/__pycache__/json.cpython-312.pyc,,
ibis/expr/operations/__pycache__/logical.cpython-312.pyc,,
ibis/expr/operations/__pycache__/maps.cpython-312.pyc,,
ibis/expr/operations/__pycache__/numeric.cpython-312.pyc,,
ibis/expr/operations/__pycache__/reductions.cpython-312.pyc,,
ibis/expr/operations/__pycache__/relations.cpython-312.pyc,,
ibis/expr/operations/__pycache__/sortkeys.cpython-312.pyc,,
ibis/expr/operations/__pycache__/strings.cpython-312.pyc,,
ibis/expr/operations/__pycache__/structs.cpython-312.pyc,,
ibis/expr/operations/__pycache__/subqueries.cpython-312.pyc,,
ibis/expr/operations/__pycache__/temporal.cpython-312.pyc,,
ibis/expr/operations/__pycache__/temporal_windows.cpython-312.pyc,,
ibis/expr/operations/__pycache__/udf.cpython-312.pyc,,
ibis/expr/operations/__pycache__/vectorized.cpython-312.pyc,,
ibis/expr/operations/__pycache__/window.cpython-312.pyc,,
ibis/expr/operations/analytic.py,sha256=GIAwe2Hz6eGVjSlC6fD3T6z9s9a4m9-XAUbUh1DN_dI,1779
ibis/expr/operations/arrays.py,sha256=rXIUCMNHFQL7024bvOTz9LFfOiV3NPuiiEtUnPyOLu0,6654
ibis/expr/operations/core.py,sha256=1qixBk-cg0C-wPYJ0RKEZvKOLdX1A0dU_BB_QXHdeHk,5099
ibis/expr/operations/generic.py,sha256=ysu5YrS8yTPMMtcGLCCWfFVpso1amepERsqdMFd92hA,7123
ibis/expr/operations/geospatial.py,sha256=OD5lhz-RonRdpx4Kzf92_9N9bqh5ZswQRNeooN1eux8,10623
ibis/expr/operations/histograms.py,sha256=Nc5sYK87LWzfJtkBj39Eza_OCxBcz-YutCCAl4eqphQ,1527
ibis/expr/operations/json.py,sha256=5ujsgO2b4WY2wzvtDKIIKkZdciUE415r0NFYfrQVlLA,1706
ibis/expr/operations/logical.py,sha256=Cb-E2euTZ8MGGFWVCU28bKX7ggnYa-fnYTjYpuAMLus,3759
ibis/expr/operations/maps.py,sha256=_46AHkSADn4Ki4wG1vQoTKHQRgRCOXnRxBgSEteK93g,1893
ibis/expr/operations/numeric.py,sha256=cjB2iFGTS0zoSbm7vjSh3R47a87VseuOFDlt98vUUBU,7225
ibis/expr/operations/reductions.py,sha256=T9H-RoxNPziuBAUdD5BlYRStPRdoP-mtS_IpUQqcy0g,9175
ibis/expr/operations/relations.py,sha256=LTqKj6XrynlOJ8bQqlzUsp3aazL4acL9HrKLcEgBHpQ,13368
ibis/expr/operations/sortkeys.py,sha256=6YMbFrBPwaFvixfG05Ue6fNsJgOSRP6jff3RIMto_8k,971
ibis/expr/operations/strings.py,sha256=jQOXkRrnKlDrkNLI27GSiDPy0AwWv_jBJsMm4sVs7uw,7359
ibis/expr/operations/structs.py,sha256=5-QC2Uz1WYKNiPfUR7vgJZoEYyY3QKvKKLP8Fl8JJfI,1603
ibis/expr/operations/subqueries.py,sha256=-OgFBiKFJCH8vLZM1BfVyiq4jQgUrbwM-6yhDZrCmtU,2182
ibis/expr/operations/temporal.py,sha256=2hXPkLWR0vH9zm-R2ybmR2mCaZwzM04daxevo8-stx0,9633
ibis/expr/operations/temporal_windows.py,sha256=hxdyXPVo_UdbAwCo9UUOeimku0IkFoZKrJnbBkLnxwE,1245
ibis/expr/operations/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/expr/operations/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/test_core.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/test_core_py310.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/test_generic.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/test_rewrites.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/test_sortkeys.cpython-312.pyc,,
ibis/expr/operations/tests/__pycache__/test_structs.cpython-312.pyc,,
ibis/expr/operations/tests/conftest.py,sha256=GSorr9Ivtgzy3zFQ1n3-efcFzNMQzcf_O7RyXJaPQAc,158
ibis/expr/operations/tests/snapshots/test_generic/test_error_message_when_constructing_literal/call0-missing_a_required_argument/missing_a_required_argument.txt,sha256=uPiqEcZlWY6_cbvnksoALgduByYWu5rAZIEM1mqQEng,166
ibis/expr/operations/tests/snapshots/test_generic/test_error_message_when_constructing_literal/call1-too_many_positional_arguments/too_many_positional_arguments.txt,sha256=uJFm8fLPfvY5z9Vie6LF8CCrHb96qQpMBGqMeu-lBpA,187
ibis/expr/operations/tests/snapshots/test_generic/test_error_message_when_constructing_literal/call2-got_an_unexpected_keyword/got_an_unexpected_keyword.txt,sha256=1zLoi7E0oKaQMKRLN7MDdm6uCW3gPpLQhz2u5j4utUE,204
ibis/expr/operations/tests/snapshots/test_generic/test_error_message_when_constructing_literal/call3-multiple_values_for_argument/multiple_values_for_argument.txt,sha256=esqL6VuVCZ-g4PUvvmBQSYfNil4OlJDYYHBMTv63xds,215
ibis/expr/operations/tests/snapshots/test_generic/test_error_message_when_constructing_literal/call4-invalid_dtype/invalid_dtype.txt,sha256=NvCLOJROU4kfAC3LjGk6CrUA3xYEWvJSAGNAY5WpFcI,238
ibis/expr/operations/tests/snapshots/test_generic/test_error_message_when_constructing_literal/call5-unable_to_normalize/unable_to_normalize.txt,sha256=d1ESoNH7TsAFhMrF_qlBZJmg1rRFSJFCt4MVpoJUhUI,46
ibis/expr/operations/tests/test_core.py,sha256=wewdHlBiiaT89HaX7gujWyMFRD7iT4BiLAo6ld-ZqIk,5615
ibis/expr/operations/tests/test_core_py310.py,sha256=mYq-pkH8qRd8QoeuCPZrqRVg5K1-FQ4EeHiiVKdLg8I,789
ibis/expr/operations/tests/test_generic.py,sha256=yRQKSqwjW_xcMBgCDYCkV4mw9ek0A5CyamX4ZnjFWrM,5216
ibis/expr/operations/tests/test_rewrites.py,sha256=kWUjCP-CWZCn51_HcPwgdFdsMJ_BsLm3DdcHgY2CbWI,3058
ibis/expr/operations/tests/test_sortkeys.py,sha256=0H4U5Yy84sIKIc4Y98SUg8GGO2q4O52OhP0MgnJve8o,432
ibis/expr/operations/tests/test_structs.py,sha256=xzhX3Q744D0d1skhMFCJ8cl0TSJzzhqmsrpaZR7a0HY,909
ibis/expr/operations/udf.py,sha256=YvcO5_17f3Ak3lLA2H4YcRKfCqvWX9WKbYh_uE0YtAA,20864
ibis/expr/operations/vectorized.py,sha256=ZAene9tDdko8Cp5DR-jCr7QZA942tLKj2EY0ipqDHaY,1136
ibis/expr/operations/window.py,sha256=ryO4LKYvHiiWS2w8u6Jx9_S8-3nzVA7R0ZmXSM7lSsA,3646
ibis/expr/rewrites.py,sha256=c9TWuBWGimpSru2sZWCoCsybCnsXE17XHtPDD4UrBv0,12911
ibis/expr/rules.py,sha256=UrG0Gq94I7LSkAtj-0rzAB4L5Fayl6Dh0WjWmkhob8s,4486
ibis/expr/schema.py,sha256=UGotsS-I2z346cEo_0tFMyzleoeyXweHYAk2hOcwbME,10731
ibis/expr/sql.py,sha256=xWo_716h4aeRW1CXBgRMc7I6wPp0oPbF_nVa5Bv8Qzg,14720
ibis/expr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/expr/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_api.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_datashape.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_decompile.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_dereference.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_format.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_newrels.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_reductions.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_rewrites.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_schema.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_sql.cpython-312.pyc,,
ibis/expr/tests/__pycache__/test_visualize.cpython-312.pyc,,
ibis/expr/tests/conftest.py,sha256=b6k_p_lhPzo2vA_jrrQk4_lrrCoeJNGphE1gjs4aipk,11451
ibis/expr/tests/snapshots/test_format/test_aggregate_arg_names/repr.txt,sha256=lEalWKvQV2zPtSZCARzRqUndtx4qz3dAWBGqcWtk4es,264
ibis/expr/tests/snapshots/test_format/test_arbitrary_traversables_are_supported/repr.txt,sha256=BntcFpDxDNz55c7V70xOMHwEGDm0kElz2GG6g7SL9SI,89
ibis/expr/tests/snapshots/test_format/test_argument_repr_shows_name/repr.txt,sha256=IfiSHN4axxci-lX-d0E3NFx4JpKoGNAzqpgwMEJNMnA,116
ibis/expr/tests/snapshots/test_format/test_asof_join/repr.txt,sha256=KcY60mD5mHozO4CceyAhC1PvAWHLFzrknJ62hvz-fEo,421
ibis/expr/tests/snapshots/test_format/test_complex_repr/repr.txt,sha256=miZTZGNDZ7oOUaADryo_3Tmw49q1l7iOLchdSrRPwEU,206
ibis/expr/tests/snapshots/test_format/test_default_format_implementation/repr.txt,sha256=Bzvy4pR7cMAyIxMowNEZi2QnnhBtzdQ8VMoBzC4G-uw,87
ibis/expr/tests/snapshots/test_format/test_destruct_selection/repr.txt,sha256=e77-zSjXLzCAh05em11Nh8rAA07vrnr0eXhFGg1sBg0,394
ibis/expr/tests/snapshots/test_format/test_fill_null/fill_null_dict_repr.txt,sha256=NptJd6VVz5aACK1YyhvAhEZibTTc6Vx4-DhksoVqWNc,81
ibis/expr/tests/snapshots/test_format/test_fill_null/fill_null_int_repr.txt,sha256=vzCgSGTDinjRR8VX03X8aRVQcJkZR5v5shV0aKpCnxg,107
ibis/expr/tests/snapshots/test_format/test_fill_null/fill_null_str_repr.txt,sha256=0pJtt8TVhxm_Gwz0SEI3uBAtsue1wByz0fEnP31w_5U,111
ibis/expr/tests/snapshots/test_format/test_format_dummy_table/repr.txt,sha256=Hg5cd2bgYiEc6ibnhM5HquQVqa1BqTY7mU-hDrpdSP0,28
ibis/expr/tests/snapshots/test_format/test_format_in_memory_table/repr.txt,sha256=z1M58jIRGXVdqe_OreCr4-SlBUR0e7Y0u73KElfoTIQ,153
ibis/expr/tests/snapshots/test_format/test_format_multiple_join_with_projection/repr.txt,sha256=9LEdrIKCwOf5aONn62lKd6fadm1mwUixA8PJsR-XjYk,478
ibis/expr/tests/snapshots/test_format/test_format_new_relational_operation/repr.txt,sha256=L2rxolHmOCl1cS734JWi1rdnoMFdLlqreQ9OcgW96IQ,479
ibis/expr/tests/snapshots/test_format/test_format_projection/repr.txt,sha256=blrkc819SsH56J1jofXla6w-0VSjuvO1PKwfvWkiZ1Q,204
ibis/expr/tests/snapshots/test_format/test_format_show_variables/repr.txt,sha256=93QqjqyxPvV2Z7xzR-OvHPxbTQfO-ESVx8g-3FWFFfs,435
ibis/expr/tests/snapshots/test_format/test_format_table_column/repr.txt,sha256=wxX5NBn9Sc8iyFQeto1BavTLHTBh4IuSnZagIpFIZfA,155
ibis/expr/tests/snapshots/test_format/test_format_table_with_empty_schema/repr.txt,sha256=kp3SR440_aAaYbW35hUXhSuvU14DnICsTARNkkkG6y0,32
ibis/expr/tests/snapshots/test_format/test_format_unbound_table_namespace/repr.txt,sha256=lYrUopB1BJ158hmCKWinfLF8T9NwUwFENREqHM6dplI,38
ibis/expr/tests/snapshots/test_format/test_format_unbound_table_namespace/reprcatdb.txt,sha256=dEwPKuHK5BW1C4AcGeA_03e0hFqYgKdJWxKtZSQX_FU,47
ibis/expr/tests/snapshots/test_format/test_format_unbound_table_namespace/reprdb.txt,sha256=2Ri-hODQ0oe2LW7C8IIM5RH6FeIq59RiARl85R280Fk,43
ibis/expr/tests/snapshots/test_format/test_memoize_filtered_table/repr.txt,sha256=g3mMAEtcSqwHNzQeTvSv07PbhPvx8r-7JSLoj3HZm7Q,315
ibis/expr/tests/snapshots/test_format/test_memoize_filtered_tables_in_join/repr.txt,sha256=Fh6WcMytaeN4QsKMcnEkDNv2pMQdEa2SeB3xHnjSecs,455
ibis/expr/tests/snapshots/test_format/test_named_value_expr_show_name/repr.txt,sha256=leC0CGqCo1uSfs8CGfzzEJbeulp9tY5ey2uqjEwWoA4,172
ibis/expr/tests/snapshots/test_format/test_named_value_expr_show_name/repr2.txt,sha256=0K1U-I0PIaEn6qTdcFWXzS_COTZFmbKgrQDXtRBu_38,161
ibis/expr/tests/snapshots/test_format/test_repr_exact/repr.txt,sha256=m4XKMeT76Xa4A80n76dYKd851US84MdLEW82HWq_8ZY,153
ibis/expr/tests/snapshots/test_format/test_same_column_multiple_aliases/repr.txt,sha256=_paf2w9uyNJ-x1QVicnyQry2WCP-hoBwxFNzLgj8UnA,88
ibis/expr/tests/snapshots/test_format/test_schema_truncation/repr1.txt,sha256=XRzVtKpEDSNc1JfVO-cb9FrHobnY7GGSwPM8X7UQ7sg,26
ibis/expr/tests/snapshots/test_format/test_schema_truncation/repr8.txt,sha256=5dEOV0Nv1FtEbzQ85xvuxJsAiMqb0JLs9dAvJecrUcc,116
ibis/expr/tests/snapshots/test_format/test_schema_truncation/repr_all.txt,sha256=hJ_6lX669WJuYiQnIZjCDg9fBx42E8jtE78ivzNvBFI,235
ibis/expr/tests/snapshots/test_format/test_table_count_expr/cnt_repr.txt,sha256=JuDdVRa697ySiep0_dVHn8XrRPJSJxK3rqCrFgzdscI,74
ibis/expr/tests/snapshots/test_format/test_table_count_expr/join_repr.txt,sha256=vr8AgLNbtzdl7xff1KIhbfPiNf7kGVh4WCQ0IToxvrE,242
ibis/expr/tests/snapshots/test_format/test_table_count_expr/union_repr.txt,sha256=wpc2alyhvBgRKSg6e8mq9B4g2ljkxcJvGLmPvr7sEDw,155
ibis/expr/tests/snapshots/test_format/test_table_type_output/repr.txt,sha256=X89bmt65AcpIera3hRGXRECRycgcKRTwgcv9aPv2xRg,168
ibis/expr/tests/snapshots/test_format/test_two_inner_joins/repr.txt,sha256=_07iSZvL6Skn_fylKR4Imij_tem7hj5fUd9Q0mrbqUI,514
ibis/expr/tests/snapshots/test_format/test_window_group_by/repr.txt,sha256=FULB0B6D_TlvlSCd7zoiAkfU1eIRO8J9Di5FuSd6_BU,113
ibis/expr/tests/snapshots/test_format/test_window_no_group_by/repr.txt,sha256=EMPicce6QH57phv7OHn-rs2OHOdZSOEkHZ4Djt86Xvc,143
ibis/expr/tests/snapshots/test_sql/test_parse_sql_aggregation_with_multiple_joins/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_aggregation_with_multiple_joins/decompiled.py,sha256=vCxQdPxLo54IDveW3dyKz8lWzAhDMPK1ms9c6dKaeTs,1084
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_aggregation/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_aggregation/decompiled.py,sha256=XT6Vgvd_cbVwRC5Y3LvyhjXNgbqx1rgfKv1NsMx60vk,341
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_aggregation_with_join/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_aggregation_with_join/decompiled.py,sha256=aGAYcmGcXcSEfhgz22IWJjr67MjFimR5ptPROO4zNXM,756
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_join/inner/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_join/inner/decompiled.py,sha256=pGNSf6nH0JUkocRDp9I2AQCyZqtmqdIkWNehphQ8BDQ,917
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_join/left/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_join/left/decompiled.py,sha256=4Hx8UqlhlFhXHX5q3zHADEOY0VzfDDJABmDXXwhOsPM,916
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_join/right/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_join/right/decompiled.py,sha256=lPsDa1G0TeNqKSmW5X-A_zFynD0fg6xelxA6_1_CWRY,917
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_projection/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_basic_projection/decompiled.py,sha256=brHAtlM7bVn0NoybBfLxHmAovUK7CPeGuN-QjbcMOeM,282
ibis/expr/tests/snapshots/test_sql/test_parse_sql_in_clause/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_in_clause/decompiled.py,sha256=VrSnr6lwAXQ4jXSV4PYaVh0VdQdehk_HEKGfP5TCu5g,423
ibis/expr/tests/snapshots/test_sql/test_parse_sql_join_subquery/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_join_subquery/decompiled.py,sha256=rEBgw6Efi5yY3sq7uQ3dKJfXNmzYll-eTpI86T933O4,533
ibis/expr/tests/snapshots/test_sql/test_parse_sql_join_with_filter/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_join_with_filter/decompiled.py,sha256=4Hx8UqlhlFhXHX5q3zHADEOY0VzfDDJABmDXXwhOsPM,916
ibis/expr/tests/snapshots/test_sql/test_parse_sql_limited_join/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_limited_join/decompiled.py,sha256=AJUn_iULhnzfzNuRzG44wBUQhE7vPmm3364wHrSSrxk,750
ibis/expr/tests/snapshots/test_sql/test_parse_sql_multiple_joins/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_multiple_joins/decompiled.py,sha256=xkiqqooaZDatul8F39M--bqPK7Q4EFA_EOp7IUgZQY0,963
ibis/expr/tests/snapshots/test_sql/test_parse_sql_scalar_subquery/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_scalar_subquery/decompiled.py,sha256=zUShegF5El_5qqvBZZ5zOZDVrAEVVH8lZrTnkJU7U3s,519
ibis/expr/tests/snapshots/test_sql/test_parse_sql_simple_reduction/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_simple_reduction/decompiled.py,sha256=uRPjV2ozF6242JSDojYSyXNVkPzB7WvSe-vO8pakKUE,309
ibis/expr/tests/snapshots/test_sql/test_parse_sql_simple_select_count/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_simple_select_count/decompiled.py,sha256=4mB2I-u64HvTPVT2l8UmCai9DRxzOrriiXXZrdl4V3Q,199
ibis/expr/tests/snapshots/test_sql/test_parse_sql_table_alias/__pycache__/decompiled.cpython-312.pyc,,
ibis/expr/tests/snapshots/test_sql/test_parse_sql_table_alias/decompiled.py,sha256=wptCrBbxXxC2gG-nI8MOX_x6OzdmOHOAjb6FO02WcR8,133
ibis/expr/tests/test_api.py,sha256=_xgO1syLMZ3pACT7eEvQxxG3hpvwHcQpuPMiN7QPj4Q,5860
ibis/expr/tests/test_datashape.py,sha256=jNxvftzLgrOZCGyc-pvR6SqISqORIORMgifISgSQev0,2006
ibis/expr/tests/test_decompile.py,sha256=ONi271gdaM3awu-Ihgv0N0eScN5X8rNrW2q2PdKNZpw,2302
ibis/expr/tests/test_dereference.py,sha256=J-ZPAoLc1NFF_sYPmI5JDCwBteA_a1-WLequIYpQUVU,1026
ibis/expr/tests/test_format.py,sha256=8iZdqPbAB4wNTTmJ3tfCxjhH6UvS7HflqZqGH6o-xtc,13826
ibis/expr/tests/test_newrels.py,sha256=kD4mxO4BT35I09hbsx-FuYXQmRYT49OZkR45juVP4TE,56128
ibis/expr/tests/test_reductions.py,sha256=8NlnfZoAX9OTFvYg6mrkgrEtMq_OBJXJ3bpwbl45Ex0,5710
ibis/expr/tests/test_rewrites.py,sha256=ZQKBYl2Ba7UW6tlCV1TWm4TFUTmvl8XFxiicG3vHHQw,2721
ibis/expr/tests/test_schema.py,sha256=do8h8ty6dykF_9qgf3VZ4-3yPXyLq8nWmQ_0fk3WzuE,11786
ibis/expr/tests/test_sql.py,sha256=3Y7nXonGpoa_Uvz6udCKmLOwa1bdptXajwKI0nAjhl8,4828
ibis/expr/tests/test_visualize.py,sha256=j2UFfOrB634cc81d94qqls2f1n9UaagPQIPNcksK5C8,4656
ibis/expr/types/__init__.py,sha256=3UH2Z2QMVfo8pQbTzEH8azBHzQG4GrA_Rjfvx07LpGM,931
ibis/expr/types/__pycache__/__init__.cpython-312.pyc,,
ibis/expr/types/__pycache__/_rich.cpython-312.pyc,,
ibis/expr/types/__pycache__/arrays.cpython-312.pyc,,
ibis/expr/types/__pycache__/binary.cpython-312.pyc,,
ibis/expr/types/__pycache__/collections.cpython-312.pyc,,
ibis/expr/types/__pycache__/core.cpython-312.pyc,,
ibis/expr/types/__pycache__/dataframe_interchange.cpython-312.pyc,,
ibis/expr/types/__pycache__/generic.cpython-312.pyc,,
ibis/expr/types/__pycache__/geospatial.cpython-312.pyc,,
ibis/expr/types/__pycache__/groupby.cpython-312.pyc,,
ibis/expr/types/__pycache__/inet.cpython-312.pyc,,
ibis/expr/types/__pycache__/joins.cpython-312.pyc,,
ibis/expr/types/__pycache__/json.cpython-312.pyc,,
ibis/expr/types/__pycache__/logical.cpython-312.pyc,,
ibis/expr/types/__pycache__/maps.cpython-312.pyc,,
ibis/expr/types/__pycache__/numeric.cpython-312.pyc,,
ibis/expr/types/__pycache__/relations.cpython-312.pyc,,
ibis/expr/types/__pycache__/rich.cpython-312.pyc,,
ibis/expr/types/__pycache__/strings.cpython-312.pyc,,
ibis/expr/types/__pycache__/structs.cpython-312.pyc,,
ibis/expr/types/__pycache__/temporal.cpython-312.pyc,,
ibis/expr/types/__pycache__/temporal_windows.cpython-312.pyc,,
ibis/expr/types/__pycache__/typing.cpython-312.pyc,,
ibis/expr/types/__pycache__/uuid.cpython-312.pyc,,
ibis/expr/types/_rich.py,sha256=-9iYGHIJXY-UnK6MSpwZxoLrADwFhaY0QiLbT8fRtyM,15005
ibis/expr/types/arrays.py,sha256=mtUXDyExPnri_jfwy-vyBIomf5kkSvezT9gTlgUMbqM,65036
ibis/expr/types/binary.py,sha256=QwY7hLxgoFCjQv7TCnS9uPV6lF94L6pat2qbOA38jEg,813
ibis/expr/types/collections.py,sha256=36yYemOIB55gC9npZM2Ky6hIdyL-38ivsMk0EkmehmY,271
ibis/expr/types/core.py,sha256=b0y8dlzt74E8dsVImfS0SD6pWQYIQCo3qdwTyDQ4z1o,30378
ibis/expr/types/dataframe_interchange.py,sha256=3ea6ElVZEpH-qObQc5pAxt9m8BjMBKwXel3DLGfgDr4,5408
ibis/expr/types/generic.py,sha256=df2av9kvfQ8WRnR_RV8Rbl05c5OY9BTGz9E5_pPWAXY,116090
ibis/expr/types/geospatial.py,sha256=GmVjm3o1nvQqBr8u82MpBDYa5ijv_ZxopGcGHiAEJM0,67340
ibis/expr/types/groupby.py,sha256=JlX0MUEsC-NY7IiOPpdt-_4WznyaGS_22eTHOcuWVQg,12447
ibis/expr/types/inet.py,sha256=RkjIyBYwOPMdYqWCUb3frIOtoiaSn0IcxBBjgWVNCo8,446
ibis/expr/types/joins.py,sha256=EnwmGRrYn2QbzWE1SIXAGM_g_lqVBRedXSfxD28Z4cc,16550
ibis/expr/types/json.py,sha256=dd6rPTnl0vrLQvNSBKC2OnYjDLX4U3FBsfgt5tAKhNQ,29187
ibis/expr/types/logical.py,sha256=za8PnOhOA7bdaojPOC2fTAg6-tpRiD7VkcX1KlCFW3s,21288
ibis/expr/types/maps.py,sha256=vfDzL3_wBHqN5hey7oMHjoaYF69OfaKMJxlddVM4tBE,17923
ibis/expr/types/numeric.py,sha256=N70bHpziQc7q4pgcmfoOT8O9_uVk6_YFvx-UKLCd6_U,64848
ibis/expr/types/relations.py,sha256=y-Glo3khwP6LRjgrbWj9qO2npanuhosDtETSwdw2UZA,267424
ibis/expr/types/rich.py,sha256=CM_HiX6WNGrKRzyFKFQ8mE6dFEN_70JZY1_pN1mq0hg,2289
ibis/expr/types/strings.py,sha256=9PPg31RdvN7nb-mpACugG5rXuB1yxfkft6NuWr0xwsM,61033
ibis/expr/types/structs.py,sha256=xojKf2AU9PmEwJHlRvVzTnDJnugdpaEJsdYasOzx70Q,14939
ibis/expr/types/temporal.py,sha256=4kJ4Pj9UWehJHQPXvolvK0W3o5VULHdu5J3kz2HdC7M,53321
ibis/expr/types/temporal_windows.py,sha256=OzTrQtAzmvMPgR_ukJ4g8M-nxFhc-maJRWuI-RaPHNg,3002
ibis/expr/types/typing.py,sha256=l5xRPUzsQMRiXNFGUYUo12Y1NPAM9ez-PbpmHDHhn3Y,173
ibis/expr/types/uuid.py,sha256=JaN-7ujmS7IEGuYbowJxCc6KmZKNT8rHljzPhU_uBzs,276
ibis/expr/visualize.py,sha256=shmql0fBJ0YwqUhFS0EpXEtOlvBG5jXpDMODCtRIxb8,7669
ibis/formats/__init__.py,sha256=7w4QYJfrQIdinr3HMjLX5SPTbUFmJczo9wGWBGtfObo,6712
ibis/formats/__pycache__/__init__.cpython-312.pyc,,
ibis/formats/__pycache__/numpy.cpython-312.pyc,,
ibis/formats/__pycache__/pandas.cpython-312.pyc,,
ibis/formats/__pycache__/polars.cpython-312.pyc,,
ibis/formats/__pycache__/pyarrow.cpython-312.pyc,,
ibis/formats/numpy.py,sha256=ONQb-LGrPNBxf6993rOj0jeGLm2ETFBFP5c791QjaTs,3721
ibis/formats/pandas.py,sha256=3gZm8i-LrPSg6n8BOty7geO_uJadw3v8mAvXIhlXnIo,14260
ibis/formats/polars.py,sha256=u59Nta2cI9gpReFnlJYu4bdG-QrsB_EJS3k6S_1tQjM,6264
ibis/formats/pyarrow.py,sha256=TnknYGq5bQ2b8YY_3gPhTJvkIf-F7KPBNXI9Pnt3AkI,14198
ibis/formats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/formats/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/formats/tests/__pycache__/test_numpy.cpython-312.pyc,,
ibis/formats/tests/__pycache__/test_pandas.cpython-312.pyc,,
ibis/formats/tests/__pycache__/test_polars.cpython-312.pyc,,
ibis/formats/tests/__pycache__/test_pyarrow.cpython-312.pyc,,
ibis/formats/tests/test_numpy.py,sha256=yff79ov_oQrT4tTYh4U3JcsKh1IGTESD1ktY5X1ccn4,4416
ibis/formats/tests/test_pandas.py,sha256=-McbXUnT19uTsMaURqk4-RljVKH7_XbeatClfx-zuuY,14343
ibis/formats/tests/test_polars.py,sha256=IyJNCefGZoQCvRcnPThIIdbDQ0rG0e3R0G6gUuinVi8,5864
ibis/formats/tests/test_pyarrow.py,sha256=y_vZjoKZWpAmhGOsxh_NRW-rbf60Nggz9YHSbhfsiWg,10122
ibis/interactive.py,sha256=VQFMWQr03zYxjGWIA270hqoQ5ZJzFj8v8ywx2O2RZXU,235
ibis/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/legacy/__pycache__/__init__.cpython-312.pyc,,
ibis/legacy/udf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/legacy/udf/__pycache__/__init__.cpython-312.pyc,,
ibis/legacy/udf/__pycache__/validate.cpython-312.pyc,,
ibis/legacy/udf/__pycache__/vectorized.cpython-312.pyc,,
ibis/legacy/udf/validate.py,sha256=IlnewwXcRTh0wtwk6qK44iOIequwEo3hcMmvSN4DRR0,2149
ibis/legacy/udf/vectorized.py,sha256=R0lIFqDuj41R2puyaKdkM2QacWbFRCW4gkh5dVN05YI,9530
ibis/selectors.py,sha256=196C6w_O4uAWFPetKGxYIdZW22TND8pT3eofCrXU6x8,28903
ibis/streamlit/__init__.py,sha256=nbngqi-mJAN9XRC7F2G8NxhFcB0dJoXWgpHKHU68fC0,2186
ibis/streamlit/__pycache__/__init__.cpython-312.pyc,,
ibis/tests/__init__.py,sha256=V7zVAh1pDuB8itqiCJQVoSXY6UckprY1ciHk39C6aoQ,609
ibis/tests/__pycache__/__init__.cpython-312.pyc,,
ibis/tests/__pycache__/conftest.cpython-312.pyc,,
ibis/tests/__pycache__/strategies.cpython-312.pyc,,
ibis/tests/__pycache__/test_api.cpython-312.pyc,,
ibis/tests/__pycache__/test_config.cpython-312.pyc,,
ibis/tests/__pycache__/test_strategies.cpython-312.pyc,,
ibis/tests/__pycache__/test_util.cpython-312.pyc,,
ibis/tests/__pycache__/test_version.cpython-312.pyc,,
ibis/tests/__pycache__/util.cpython-312.pyc,,
ibis/tests/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/tests/benchmarks/__pycache__/__init__.cpython-312.pyc,,
ibis/tests/benchmarks/__pycache__/benchfuncs.cpython-312.pyc,,
ibis/tests/benchmarks/__pycache__/test_benchmarks.cpython-312.pyc,,
ibis/tests/benchmarks/benchfuncs.py,sha256=jg7KSMObPKiSxhQQSF50K4fPMrs_aSX2pvdy44LMLhY,15000
ibis/tests/benchmarks/test_benchmarks.py,sha256=b9F4rOyMuZJ0bdDfoXmN3AUC3EwDGpeD-jFXjr0-1Y4,27563
ibis/tests/conftest.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/tests/expr/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis/tests/expr/__pycache__/__init__.cpython-312.pyc,,
ibis/tests/expr/__pycache__/conftest.cpython-312.pyc,,
ibis/tests/expr/__pycache__/mocks.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_aggregation.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_analysis.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_analytics.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_case.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_decimal.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_format_sql_operations.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_geospatial.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_literal.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_pipe.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_pretty_repr.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_relocate.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_selectors.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_set_operations.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_sql_builtins.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_string.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_struct.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_table.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_temporal.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_temporal_windows.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_timestamp.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_udf.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_uuid.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_value_exprs.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_window_frames.cpython-312.pyc,,
ibis/tests/expr/__pycache__/test_window_functions.cpython-312.pyc,,
ibis/tests/expr/conftest.py,sha256=Hg-TxT0zILcuvGSnZzgIFPhm6RGnQkQj8lH4hKex8ss,1729
ibis/tests/expr/mocks.py,sha256=37Zmvpp1BIPcr6KYRkFiJEVrkJLay03j4_rrstY72B4,3004
ibis/tests/expr/snapshots/test_format_sql_operations/test_format_sql_query_result/repr.txt,sha256=CT3nwgTUtjVSkLXdBVa5kim-TLNuRW0bAHKKaGZDwmc,1984
ibis/tests/expr/snapshots/test_format_sql_operations/test_memoize_database_table/repr.txt,sha256=LP7wiOfnJV5Aji3QZ47XIUywsLIJ4kkx8LueojvuJ_g,421
ibis/tests/expr/snapshots/test_format_sql_operations/test_memoize_insert_sort_key/repr.txt,sha256=h9y1b38EysNEE4UhWAyUoL03NBjX4A07s6sCdnhIdl8,1234
ibis/tests/expr/test_aggregation.py,sha256=015LnCqnyxcf-LHNyr0kKek1e81W65vUoDXK0a6XpxY,3252
ibis/tests/expr/test_analysis.py,sha256=E-HQ2hwBW2i_gRavmWFtygIW6ZIcfKzx9SH0UOVj8mU,8969
ibis/tests/expr/test_analytics.py,sha256=P7Z9OJ0Zn3kFv8yINBNWQ72aTBxFj5n-eb_MvbQvKFw,3314
ibis/tests/expr/test_case.py,sha256=dJwR3pZ-OVMVLzGkKAYreZkxD-7ksagWNEC0C_evUm4,6132
ibis/tests/expr/test_decimal.py,sha256=sf7xyd-9MoXQNGOfiX81dtCfAeEPc7YIXQMxdDgeUV4,4134
ibis/tests/expr/test_format_sql_operations.py,sha256=A9DAX-B4qGAvLjeDNYlxzDNTyBzLa6G2DYque9HYPEo,1657
ibis/tests/expr/test_geospatial.py,sha256=dzqClmyEQx9JejeX_88_KjnxOFfNTylwSPGHXtZngEE,778
ibis/tests/expr/test_literal.py,sha256=eHLyGTVYfdSZD6BYAORRFK6BipAN3GtEs99KlyxHt3A,5105
ibis/tests/expr/test_pipe.py,sha256=-jLHXJ9NbuqbYSYOUvarpzeu278Irp1r_Lxuu5yexkI,1599
ibis/tests/expr/test_pretty_repr.py,sha256=CFh6zsjMQPbfwXkBvF8HYH1r6Z508ZKgMIMTCJzHZmc,6177
ibis/tests/expr/test_relocate.py,sha256=J1GvsUHY2_K7Sb7dFi5_LfQXEbuJgAmpbORyHQOYwyM,3654
ibis/tests/expr/test_selectors.py,sha256=gM9Bu3lfFZapEPYXwMZt_5Ey2YHMKDOEm1HOSJ3DVNk,16623
ibis/tests/expr/test_set_operations.py,sha256=4CDRO11j_-DNhoRoN_QvtD1geWRAGn8YYbKVnAkbQnY,1261
ibis/tests/expr/test_sql_builtins.py,sha256=-sNjQ8Q6U6cYWjHFpn0T46-oEILzXPHDwXbAOLyuUeg,6616
ibis/tests/expr/test_string.py,sha256=oZBRhi0sBud62zFKUEE1SVfYMeSuwbOZMDYucmvjQJE,3191
ibis/tests/expr/test_struct.py,sha256=xlN6J21SKImFW7I0vlofZFnnw0iHvIkhCv8oySsnkas,2883
ibis/tests/expr/test_table.py,sha256=hlJ9YL0K3H5BITRA41-W_EgYedo8F6ukK1fYTQfCrLA,62274
ibis/tests/expr/test_temporal.py,sha256=VuTn6h6ssI4dxkPTtPRBjxHERZ556SBsTfh1iUydaRw,27349
ibis/tests/expr/test_temporal_windows.py,sha256=CQGtWO3GxE6u_0NyIZ-D7ZTYD4u5uOdi7ixTgsX3yII,2300
ibis/tests/expr/test_timestamp.py,sha256=f9K_C6usrYE2oPJliHz2q4Hm990gkNf9PeR0EV93BO8,6144
ibis/tests/expr/test_udf.py,sha256=TiaGdp46MhszL3UsHA54S0sR_xg8MudU2Ffbkx4p2Tk,4474
ibis/tests/expr/test_uuid.py,sha256=HyN8x6gK6J45OF0dn5SFCYdnIkfoy2ovq0YusNF4zDo,181
ibis/tests/expr/test_value_exprs.py,sha256=_jqCi3CY6QYh9IftVeFpK5pyGG2eoCFCuARdq3l7Ux8,50476
ibis/tests/expr/test_window_frames.py,sha256=aDs9t6w4D5HDV6PIBrZhYsnwuBLRTSM_mbzVkb7uqWU,17729
ibis/tests/expr/test_window_functions.py,sha256=le0wBCJnUqiL6dPgGCg-qjWqwBNzXWtGlZw5EeGPVC8,2000
ibis/tests/strategies.py,sha256=opwammvmeeZH55OfyW5Xu3r0CUpEpGK8coFe5IZ5GG8,7645
ibis/tests/test_api.py,sha256=znhJzsx6ti_cnJqDshT64BCNobOkW_EtIf07pQrXjMU,2761
ibis/tests/test_config.py,sha256=ztk3npkyKjPDrw0ToD2YGl_Xf7UJKSuE4M7menS7syY,401
ibis/tests/test_strategies.py,sha256=_DKfuDJLQ9M3Arbg1g0Sb6f-v07qJDzIAHBvrkEH1gQ,4867
ibis/tests/test_util.py,sha256=VLF5o6x3B6c1WwzA7DfQN-9fmAgreWu9lZ9fjpS-Ma0,3741
ibis/tests/test_version.py,sha256=FqtgwOfg45FqG7l6wv7DsGXrU6nMSMuctGgxgPaQCqg,226
ibis/tests/util.py,sha256=oeF64hY9bQRGYbgdrwlmHVnpkVC7W8OCp-MMTYFa5F4,1961
ibis/util.py,sha256=rdXRxaIDDGU0gDgK7NMsZqekY7qGpPQuc-3kr4s-sek,17523
ibis_framework-10.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ibis_framework-10.6.0.dist-info/METADATA,sha256=D2z9kF1KnfZZ3guogFikqh5tOb05Y9W9e9BKEwmFL4c,20866
ibis_framework-10.6.0.dist-info/RECORD,,
ibis_framework-10.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ibis_framework-10.6.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
ibis_framework-10.6.0.dist-info/entry_points.txt,sha256=i7fxSc0NZxXFH3QDG7sJ3Dh4X1Ar0kMis9u2q8bSsEA,654
ibis_framework-10.6.0.dist-info/licenses/LICENSE.txt,sha256=9Em1f5N5eaNwS3AVxkJGqfBykMECd7HQd8bATKsFdbM,11346
