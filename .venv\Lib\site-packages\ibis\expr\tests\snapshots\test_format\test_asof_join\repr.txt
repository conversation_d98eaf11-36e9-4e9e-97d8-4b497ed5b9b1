r0 := UnboundTable: left
  time1 int32
  value float64

r1 := UnboundTable: right
  time2  int32
  value2 float64

r2 := SelfReference[r1]

<PERSON><PERSON><PERSON><PERSON><PERSON>[r0]
  JoinLink[asof, r1]
    r0.time1 >= r1.time2
  JoinLink[inner, r2]
    r0.value == r2.value2
  values:
    time1:        r0.time1
    value:        r0.value
    time2:        r1.time2
    value2:       r1.value2
    time2_right:  r2.time2
    value2_right: r2.value2