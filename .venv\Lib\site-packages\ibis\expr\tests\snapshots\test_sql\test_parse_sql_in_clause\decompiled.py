import ibis


employee = ibis.table(
    name="employee",
    schema={"first_name": "string", "last_name": "string", "id": "int64"},
)
f = employee.filter(
    employee.first_name.isin(
        (
            ibis.literal("<PERSON>"),
            ibis.literal("<PERSON>"),
            ibis.literal("<PERSON>"),
            ibis.literal("<PERSON>"),
            ibis.literal("<PERSON>"),
        )
    )
)

result = f.select(f.first_name)
