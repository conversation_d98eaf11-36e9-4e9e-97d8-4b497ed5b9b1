r0 := UnboundTable: one
  c      int32
  f      float64
  foo_id string
  bar_id string

r1 := UnboundTable: two
  foo_id string
  value1 float64

r2 := UnboundTable: three
  bar_id string
  value2 float64

r3 := Filter[r0]
  r0.f > 0

<PERSON><PERSON><PERSON><PERSON><PERSON>[r3]
  JoinLink[left, r1]
    r3.foo_id == r1.foo_id
  JoinLink[inner, r2]
    r3.bar_id == r2.bar_id
  values:
    c:      r3.c
    f:      r3.f
    foo_id: r3.foo_id
    bar_id: r3.bar_id
    value1: r1.value1
    value2: r2.value2