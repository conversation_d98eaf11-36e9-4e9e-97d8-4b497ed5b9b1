r0 := DatabaseTable: airlines
  year                int32
  month               int32
  day                 int32
  dayofweek           int32
  dep_time            int32
  crs_dep_time        int32
  arr_time            int32
  crs_arr_time        int32
  carrier             string
  flight_num          int32
  tail_num            int32
  actual_elapsed_time int32
  crs_elapsed_time    int32
  airtime             int32
  arrdelay            int32
  depdelay            int32
  origin              string
  dest                string
  distance            int32
  taxi_in             int32
  taxi_out            int32
  cancelled           int32
  cancellation_code   string
  diverted            int32
  carrier_delay       int32
  weather_delay       int32
  nas_delay           int32
  security_delay      int32
  late_aircraft_delay int32

r1 := Project[r0]
  arrdelay: r0.arrdelay
  dest:     r0.dest

r2 := Project[r1]
  arrdelay: r1.arrdelay
  dest:     r1.dest
  dest_avg: WindowFunction(func=Mean(r1.arrdelay), how='rows', group_by=[r1.dest])
  dev:      r1.arrdelay - WindowFunction(func=Mean(r1.arrdelay), how='rows', group_by=[r1.dest])

r3 := Filter[r2]
  NotNull(r2.dev)

r4 := Sort[r3]
  desc r3.dev

Limit[r4, n=10]